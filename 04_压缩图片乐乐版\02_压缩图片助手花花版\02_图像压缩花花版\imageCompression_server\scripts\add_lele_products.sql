-- 为图片压缩乐乐版添加商品配置
-- 执行前请确保数据库连接正常

USE image_compression_service;

-- 检查是否已存在该AppID的商品
SELECT COUNT(*) as existing_count
FROM products
WHERE app_id = 'wx_imagecomp_lele_004';

-- 如果不存在，则插入新商品
INSERT IGNORE INTO products (app_id, product_code, product_name, description, price, duration_hours, sort_order) VALUES
-- 图片压缩乐乐版
('wx_imagecomp_lele_004', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('wx_imagecomp_lele_004', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2);

-- 验证插入结果
SELECT
    app_id,
    product_code,
    product_name,
    price,
    duration_hours,
    is_active,
    created_at
FROM products
WHERE app_id = 'wx_imagecomp_lele_004'
ORDER BY sort_order;

-- 显示所有小程序的商品配置
SELECT 
    app_id,
    COUNT(*) as product_count,
    GROUP_CONCAT(product_code ORDER BY sort_order) as products
FROM products 
WHERE is_active = TRUE
GROUP BY app_id
ORDER BY app_id;
