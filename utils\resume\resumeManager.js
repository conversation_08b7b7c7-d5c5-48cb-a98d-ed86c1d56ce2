/**
 * 简历数据管理工具类
 * 用于管理多份简历数据的存储、读取、切换等操作
 */

// 存储键名常量
const STORAGE_KEYS = {
  RESUMES_LIST: 'resumesList',           // 简历列表
  CURRENT_RESUME_ID: 'currentResumeId',  // 当前编辑的简历ID
  RESUME_PREFIX: 'resume_'               // 简历数据前缀
};

/**
 * 生成简历ID
 * @returns {string} 简历ID
 */
function generateResumeId() {
  return 'resume_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * 获取所有简历列表
 * @returns {Array} 简历列表
 */
function getResumesList() {
  try {
    const resumesList = wx.getStorageSync(STORAGE_KEYS.RESUMES_LIST);
    return resumesList || [];
  } catch (error) {
    console.error('获取简历列表失败:', error);
    return [];
  }
}

/**
 * 保存简历列表
 * @param {Array} resumesList - 简历列表
 */
function saveResumesList(resumesList) {
  try {
    wx.setStorageSync(STORAGE_KEYS.RESUMES_LIST, resumesList);
  } catch (error) {
    console.error('保存简历列表失败:', error);
  }
}

/**
 * 创建新简历
 * @param {string} title - 简历标题
 * @param {Object} resumeData - 简历数据（可选，默认为空简历）
 * @returns {Object} 创建的简历信息
 */
function createResume(title, resumeData = null) {
  const resumeId = generateResumeId();
  const now = new Date().getTime();
  
  // 创建简历信息
  const resumeInfo = {
    id: resumeId,
    title: title || '新建简历',
    createTime: now,
    updateTime: now,
    isDefault: false
  };
  
  // 默认简历数据结构
  const defaultResumeData = {
    basicInfo: {
      name: '',
      gender: '',
      phone: '',
      city: '',
      email: '',
      wechat: '',
      age: '',
      birthday: '',
      marriage: '',
      politics: '',
      nation: '',
      hometown: '',
      height: '',
      weight: '',
      photoUrl: '',
      customTitle1: '',
      customContent1: '',
      customTitle2: '',
      customContent2: ''
    },
    jobIntention: {
      position: '',
      salary: '',
      city: '',
      type: ''
    },
    educations: [],
    schools: [],
    internships: [],
    works: [],
    projects: [],
    skills: '',
    awards: '',
    interests: '',
    evaluation: '',
    customs: []
  };
  
  // 使用传入的数据或默认数据
  const finalResumeData = resumeData || defaultResumeData;
  
  // 保存简历数据
  const dataKey = STORAGE_KEYS.RESUME_PREFIX + resumeId;
  try {
    wx.setStorageSync(dataKey, finalResumeData);
  } catch (error) {
    console.error('保存简历数据失败:', error);
    return null;
  }
  
  // 更新简历列表
  const resumesList = getResumesList();
  resumesList.unshift(resumeInfo); // 新简历放在最前面
  saveResumesList(resumesList);
  
  console.log('创建简历成功:', resumeInfo);
  return resumeInfo;
}

/**
 * 获取简历数据
 * @param {string} resumeId - 简历ID
 * @returns {Object|null} 简历数据
 */
function getResumeData(resumeId) {
  if (!resumeId) return null;
  
  const dataKey = STORAGE_KEYS.RESUME_PREFIX + resumeId;
  try {
    return wx.getStorageSync(dataKey);
  } catch (error) {
    console.error('获取简历数据失败:', error);
    return null;
  }
}

/**
 * 保存简历数据
 * @param {string} resumeId - 简历ID
 * @param {Object} resumeData - 简历数据
 * @returns {boolean} 是否保存成功
 */
function saveResumeData(resumeId, resumeData) {
  if (!resumeId || !resumeData) return false;
  
  const dataKey = STORAGE_KEYS.RESUME_PREFIX + resumeId;
  try {
    wx.setStorageSync(dataKey, resumeData);
    
    // 更新简历列表中的更新时间
    updateResumeUpdateTime(resumeId);
    
    return true;
  } catch (error) {
    console.error('保存简历数据失败:', error);
    return false;
  }
}

/**
 * 更新简历的更新时间
 * @param {string} resumeId - 简历ID
 */
function updateResumeUpdateTime(resumeId) {
  const resumesList = getResumesList();
  const resumeIndex = resumesList.findIndex(resume => resume.id === resumeId);
  
  if (resumeIndex !== -1) {
    resumesList[resumeIndex].updateTime = new Date().getTime();
    saveResumesList(resumesList);
  }
}

/**
 * 删除简历
 * @param {string} resumeId - 简历ID
 * @returns {boolean} 是否删除成功
 */
function deleteResume(resumeId) {
  if (!resumeId) return false;
  
  try {
    // 删除简历数据
    const dataKey = STORAGE_KEYS.RESUME_PREFIX + resumeId;
    wx.removeStorageSync(dataKey);
    
    // 从简历列表中移除
    const resumesList = getResumesList();
    const filteredList = resumesList.filter(resume => resume.id !== resumeId);
    saveResumesList(filteredList);
    
    // 如果删除的是当前简历，清除当前简历ID
    const currentResumeId = getCurrentResumeId();
    if (currentResumeId === resumeId) {
      clearCurrentResumeId();
    }
    
    console.log('删除简历成功:', resumeId);
    return true;
  } catch (error) {
    console.error('删除简历失败:', error);
    return false;
  }
}

/**
 * 重命名简历
 * @param {string} resumeId - 简历ID
 * @param {string} newTitle - 新标题
 * @returns {boolean} 是否重命名成功
 */
function renameResume(resumeId, newTitle) {
  if (!resumeId || !newTitle) return false;
  
  const resumesList = getResumesList();
  const resumeIndex = resumesList.findIndex(resume => resume.id === resumeId);
  
  if (resumeIndex !== -1) {
    resumesList[resumeIndex].title = newTitle;
    resumesList[resumeIndex].updateTime = new Date().getTime();
    saveResumesList(resumesList);
    console.log('重命名简历成功:', resumeId, newTitle);
    return true;
  }
  
  return false;
}

/**
 * 设置当前编辑的简历
 * @param {string} resumeId - 简历ID
 */
function setCurrentResumeId(resumeId) {
  try {
    wx.setStorageSync(STORAGE_KEYS.CURRENT_RESUME_ID, resumeId);
    console.log('设置当前简历ID:', resumeId);
  } catch (error) {
    console.error('设置当前简历ID失败:', error);
  }
}

/**
 * 获取当前编辑的简历ID
 * @returns {string|null} 当前简历ID
 */
function getCurrentResumeId() {
  try {
    return wx.getStorageSync(STORAGE_KEYS.CURRENT_RESUME_ID);
  } catch (error) {
    console.error('获取当前简历ID失败:', error);
    return null;
  }
}

/**
 * 清除当前简历ID
 */
function clearCurrentResumeId() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.CURRENT_RESUME_ID);
  } catch (error) {
    console.error('清除当前简历ID失败:', error);
  }
}

/**
 * 获取当前简历数据
 * @returns {Object|null} 当前简历数据
 */
function getCurrentResumeData() {
  const currentResumeId = getCurrentResumeId();
  if (!currentResumeId) return null;
  
  return getResumeData(currentResumeId);
}

/**
 * 保存当前简历数据
 * @param {Object} resumeData - 简历数据
 * @returns {boolean} 是否保存成功
 */
function saveCurrentResumeData(resumeData) {
  const currentResumeId = getCurrentResumeId();
  if (!currentResumeId) return false;
  
  return saveResumeData(currentResumeId, resumeData);
}

/**
 * 复制简历
 * @param {string} resumeId - 要复制的简历ID
 * @param {string} newTitle - 新简历标题
 * @returns {Object|null} 新创建的简历信息
 */
function duplicateResume(resumeId, newTitle) {
  const originalData = getResumeData(resumeId);
  if (!originalData) return null;
  
  return createResume(newTitle || '简历副本', originalData);
}

module.exports = {
  // 简历列表管理
  getResumesList,
  createResume,
  deleteResume,
  renameResume,
  duplicateResume,
  
  // 简历数据管理
  getResumeData,
  saveResumeData,
  
  // 当前简历管理
  setCurrentResumeId,
  getCurrentResumeId,
  clearCurrentResumeId,
  getCurrentResumeData,
  saveCurrentResumeData,
  
  // 工具方法
  generateResumeId
};
