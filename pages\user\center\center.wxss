/* pages/user/center/center.wxss */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 用户信息区域样式 */
.user-header {
  background-color: #4B8BF5;
  padding: 40rpx 30rpx;
  color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-detail {
  margin-left: 30rpx;
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.membership-status {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.status-text {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
}

.status-text.normal {
  background-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.status-text.member {
  background-color: #FFD700;
  color: #333;
  font-weight: bold;
}

.upgrade-btn {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.9);
  color: #4B8BF5;
  border-radius: 20rpx;
  border: none;
  line-height: 1.2;
}

.upgrade-btn::after {
  border: none;
}

.login-btn {
  background-color: #fff;
  color: #4B8BF5;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin: 0;
  line-height: 1.5;
  min-width: 160rpx;
  text-align: center;
}

/* 使用统计区域样式 */
.stats-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #4B8BF5;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 26rpx;
  color: #666;
}

/* 功能菜单区域样式 */
.menu-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.resume-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzRCOEJGNSI+PHBhdGggZD0iTTE0IDJIMWEyIDIgMCAwIDAtMiAydjE2YTIgMiAwIDAgMCAyIDJoMThhMiAyIDAgMCAwIDItMlY4bC02LTZ6bTQgMTh2MUgxVjRoMTJ2NWg1djExek0xMiA5SDZWNWg2djR6bS0xIDRIN3YtMmg0djJ6bTUgMkg3di0yaDl2MnptMCAzSDd2LTJoOXYyeiIvPjwvc3ZnPg==');
}

.record-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzRCOEJGNSI+PHBhdGggZD0iTTEyIDJhMTAgMTAgMCAxIDAgMTAgMTBBMTAgMTAgMCAwIDAgMTIgMnptMCAxOGE4IDggMCAxIDEgOC04IDggOCAwIDAgMS04IDh6bTEtOC41VjdoLTJ2NmwyLjI1IDMgMS43NS0xLjI1LTItMi43NXoiLz48L3N2Zz4=');
}

.settings-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzRCOEJGNSI+PHBhdGggZD0iTTEyIDEwYy0xLjEgMC0yIC45LTIgMnMuOSAyIDIgMiAyLS45IDItMi0uOS0yLTItMnptNy40My0yLjMzYy0uMTQtLjQtLjMtLjgtLjQ5LTEuMTlsLjc0LTEuNDYtLjg3LS44Ny0xLjQ2Ljc0Yy0uMzktLjE5LS43OS0uMzUtMS4xOS0uNDlMMTUuOCAzaC0xLjIzbC0uMzYgMS42MmMtLjQuMTQtLjguMy0xLjE5LjQ5bC0xLjQ2LS43NC0uODcuODcuNzQgMS40NmMtLjE5LjM5LS4zNS43OS0uNDkgMS4xOUw5IDcuMnYxLjIzbDEuNjIuMzZjLjE0LjQuMy44LjQ5IDEuMTlsLS43NCAxLjQ2Ljg3Ljg3IDEuNDYtLjc0Yy4zOS4xOS43OS4zNSAxLjE5LjQ5TDE0LjIgMTVoMS4yM2wuMzYtMS42MmMuNC0uMTQuOC0uMy0xLjE5LS40OWwxLjQ2LS43NC44Ny0uODctLjc0LTEuNDZjLjE5LS4zOS4zNS0uNzkuNDktMS4xOUwyMSA4LjQzVjcuMmwtMS42Mi0uMzZ6TTEyIDE0Yy0xLjEgMC0yLS45LTItMnMuOS0yIDItMiAyIC45IDIgMi0uOSAyLTIgMnoiLz48L3N2Zz4=');
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 3rpx solid #999;
  border-right: 3rpx solid #999;
  transform: rotate(45deg);
}

/* 最近操作记录区域样式 */
.recent-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.recent-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.recent-item:last-child {
  border-bottom: none;
}

.action-type {
  font-size: 28rpx;
  color: #333;
}

.action-time {
  font-size: 24rpx;
  color: #999;
}

/* 退出登录按钮样式 */
.logout-btn {
  margin: 40rpx 20rpx;
  background-color: #fff;
  color: #ff4d4f;
  font-size: 32rpx;
  border: 1rpx solid #ff4d4f;
}
