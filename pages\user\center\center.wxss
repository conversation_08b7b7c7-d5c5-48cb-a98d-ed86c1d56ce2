/* pages/user/center/center.wxss */

/* 主题色变量 */
page {
  background-color: #F7F7F7;
}

/* 新拟物风格的混入 */
.neumorphism {
  background: #F7F7F7;
  box-shadow: 12px 12px 24px rgba(0, 0, 0, 0.1),
              -8px -8px 20px rgba(255, 255, 255, 0.9),
              inset 2px 2px 4px rgba(255, 255, 255, 0.5),
              inset -2px -2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 磨砂玻璃风格的混入 */
.glassmorphism {
  background: rgba(247, 247, 247, 0.98);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-container {
  padding: 30rpx;
}

.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  padding: 0 30rpx;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button-container {
  min-width: 44px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 0;
  margin: 0;
  outline: none;
  transition: opacity 0.2s ease;
}

.back-btn::after {
  display: none !important;
}

.back-btn:active {
  opacity: 0.6;
  background: transparent !important;
  background-color: transparent !important;
}

.back-icon {
  font-size: 36px;
  color: #000000;
  font-weight: normal;
  line-height: 1;
  margin-left: -2px;
}

.nav-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.nav-right-buttons {
  min-width: 44px;
}

.main-content {
  position: relative;
  width: 100%;
  padding-bottom: 40rpx;
  margin-top: -40rpx;
}

.user-info-card {
  border-radius: 30rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  color: #333333;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 30rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.member-badge {
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  font-weight: 500;
}

/* 普通用户 - 清新蓝灰色 */
.member-normal {
  background: linear-gradient(135deg, #8E9AAF, #A8B5C8);
  color: white;
}

/* 日卡会员 - 活力橙色 */
.member-day {
  background: linear-gradient(135deg, #FF8A65, #FFB74D);
  color: white;
}

/* 永久会员 - 尊贵金色 */
.member-permanent {
  background: linear-gradient(135deg, #FFD700, #FFA000);
  color: #333;
  font-weight: 600;
}

.member-icon .crown-image {
  width: 90rpx;
  height: 90rpx;
}

.member-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 30rpx;
}

.member-header {
  text-align: center;
  margin-bottom: 20rpx;
}

.member-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.member-subtitle {
  font-size: 28rpx;
  color: #666666;
}

.divider-line {
  width: 100%;
  height: 1px;
  border-top: 1px solid #f0f0f0;
  margin: 20rpx 0;
}

.privileges-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
  align-items: center;
  justify-items: center;
  padding: 0 20rpx;
}

.privilege-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding-left: 60rpx;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  background: linear-gradient(135deg, #FF8066, #E6674D);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-mark {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.privilege-text {
  font-size: 26rpx;
  text-align: left;
  line-height: 1.4;
  white-space: nowrap;
  flex: 1;
}

.member-validity {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.renew-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #FF8066, #E6674D);
  border: none;
  border-radius: 40rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(255, 128, 102, 0.3);
  position: relative;
  overflow: hidden;
}

/* 闪光效果的伪元素 */
.renew-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.6s ease;
}

.renew-btn:active {
  background: linear-gradient(135deg, #E6674D, #CC5A42);
  transform: scale(0.98);
}

/* 呼吸心跳动效 - 仅对普通用户显示 */
.heartbeat-animation {
  animation: heartbeat 2s ease-in-out infinite;
}

/* 添加闪光动画 */
.heartbeat-animation::before {
  animation: shimmer 3s ease-in-out infinite;
}

.menu-section {
  margin: 30rpx 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: scale(0.98);
}

.menu-item:last-child {
  margin-bottom: 0;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.icon-image {
  width: 48rpx;
  height: 48rpx;
}

.menu-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}

/* 呼吸心跳动画关键帧 */
@keyframes heartbeat {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 16rpx rgba(255, 128, 102, 0.3);
  }
  14% {
    transform: scale(1.05);
    box-shadow: 0 8rpx 24rpx rgba(255, 128, 102, 0.4);
  }
  28% {
    transform: scale(1);
    box-shadow: 0 4rpx 16rpx rgba(255, 128, 102, 0.3);
  }
  42% {
    transform: scale(1.05);
    box-shadow: 0 8rpx 24rpx rgba(255, 128, 102, 0.4);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 4rpx 16rpx rgba(255, 128, 102, 0.3);
  }
}

/* 闪光动画关键帧 */
@keyframes shimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}
