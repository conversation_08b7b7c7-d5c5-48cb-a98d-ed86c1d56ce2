const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    evaluationFormData: null // 将在 onLoad 中初始化
  },

  onLoad() {
    console.log('=== 自我评价页面加载 ===');
    this.loadEvaluationData();
  },

  /**
   * 从全局管理器加载自我评价数据
   */
  loadEvaluationData() {
    try {
      const evaluationData = ResumeFormHelper.loadFieldData('evaluation', app);

      // evaluation字段是数组，取第一个元素作为内容
      const evaluationContent = Array.isArray(evaluationData) && evaluationData.length > 0
        ? evaluationData[0]
        : { content: '' };

      this.setData({
        evaluationFormData: evaluationContent
      });

      console.log('✅ 自我评价数据加载成功:', evaluationContent);
    } catch (error) {
      console.error('❌ 加载自我评价数据失败:', error);
      // 出错时使用空数据
      this.setData({
        evaluationFormData: { content: '' }
      });
    }
  },

  /**
   * 处理输入
   */
  handleInput(e) {
    const content = e.detail.value;
    this.setData({
      'evaluationFormData.content': content
    });
  },

  /**
   * 保存自我评价
   */
  saveEvaluation() {
    const { evaluationFormData } = this.data;

    if (!evaluationFormData.content.trim()) {
      wx.showToast({
        title: '请填写自我评价',
        icon: 'none'
      });
      return;
    }

    try {
      // 获取当前的moduleOrder
      const moduleOrders = wx.getStorageSync('moduleOrders') || {};
      const moduleOrder = moduleOrders['evaluation'] || 10;

      // 构造评价数据（数组格式）
      const evaluationData = [{
        ...evaluationFormData,
        moduleOrder: moduleOrder
      }];

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('evaluation', evaluationData, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 自我评价保存成功:', evaluationData);

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 自我评价保存失败');
      }
    } catch (error) {
      console.error('❌ 保存自我评价时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除自我评价
   */
  deleteEvaluation() {
    wx.showModal({
      title: '提示',
      content: '确定要删除自我评价吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用 ResumeFormHelper 清空数据
            const success = ResumeFormHelper.clearFieldData('evaluation', app);

            if (success) {
              // 更新页面显示
              this.setData({
                evaluationFormData: { content: '' }
              });

              wx.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });

              console.log('✅ 自我评价删除成功');

              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
              console.error('❌ 自我评价删除失败');
            }
          } catch (error) {
            console.error('❌ 删除自我评价时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});