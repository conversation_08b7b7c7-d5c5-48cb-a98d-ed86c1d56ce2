const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 确保data目录存在
const dataDir = path.join(__dirname, '../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

/**
 * 获取token文件路径
 * @param {string} appId 小程序AppID
 * @returns {string} token文件路径
 */
function getTokenFilePath(appId) {
  return path.join(__dirname, `../data/access_token_${appId}.json`);
}

/**
 * 初始化token文件（如果不存在）
 * @param {string} appId 小程序AppID
 */
function initTokenFile(appId) {
  const tokenFilePath = getTokenFilePath(appId);
  if (!fs.existsSync(tokenFilePath)) {
    fs.writeFileSync(tokenFilePath, JSON.stringify({
      access_token: '',
      expires_at: 0
    }));
  }
}

/**
 * 获取微信access_token
 * @param {string} appId 小程序AppID
 * @param {string} appSecret 小程序AppSecret
 * @returns {Promise<string>} access_token
 */
async function getAccessToken(appId, appSecret) {
  try {
    // 确保token文件存在
    initTokenFile(appId);

    // 获取token文件路径
    const tokenFilePath = getTokenFilePath(appId);

    // 读取存储的token信息
    const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
    const now = Date.now();

    // 如果token未过期，直接返回
    if (tokenData.access_token && tokenData.expires_at > now) {
      console.log(`使用缓存的access_token (appId: ${appId})`);
      return tokenData.access_token;
    }

    // 获取新token
    console.log(`获取新的access_token (appId: ${appId})`);
    const response = await axios.get(
      `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`
    );

    if (response.data.errcode) {
      throw new Error(`获取access_token失败: ${response.data.errmsg}`);
    }

    // 保存新token
    const newTokenData = {
      access_token: response.data.access_token,
      expires_at: now + (response.data.expires_in * 1000) - 300000 // 提前5分钟过期
    };

    fs.writeFileSync(tokenFilePath, JSON.stringify(newTokenData));
    return newTokenData.access_token;
  } catch (error) {
    console.error(`获取access_token出错 (appId: ${appId}):`, error);
    throw error;
  }
}

module.exports = {
  getAccessToken
};
