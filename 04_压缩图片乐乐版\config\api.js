/**
 * API配置文件
 * 统一管理所有API接口地址和配置
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
}

// 当前环境（可根据需要切换）
const CURRENT_ENV = ENV.PRODUCTION

// 服务器配置
const SERVER_CONFIG = {
  [ENV.DEVELOPMENT]: {
    // 开发环境 - 本地服务器
    IMAGE_COMPRESSION: 'http://localhost:8851',
    MEDIA_CHECK: 'http://localhost:3000'
  },
  [ENV.PRODUCTION]: {
    // 生产环境 - 线上服务器
    IMAGE_COMPRESSION: 'https://image-compression.gbw8848.cn',
    MEDIA_CHECK: 'https://media-check.gbw8848.cn'
  }
}

// 获取当前环境的服务器配置
const getCurrentConfig = () => SERVER_CONFIG[CURRENT_ENV]

// 小程序配置
export const APP_CONFIG = {
  // 当前小程序的AppID
  APP_ID: 'wx41b6b8204ad8a5fd',
  // 小程序名称
  APP_NAME: '压缩图片乐乐版'
}

// API接口配置
export const API_CONFIG = {
  // 图像压缩服务接口
  IMAGE_COMPRESSION: {
    BASE_URL: getCurrentConfig().IMAGE_COMPRESSION,
    // 用户相关接口
    USER: {
      LOGIN: '/api/user/login',
      INFO: '/api/user/info',
      UPDATE: '/api/user/update'
    },
    // 会员相关接口
    MEMBERSHIP: {
      STATUS: '/api/membership/status',
      PRODUCTS: '/api/membership/products',
      CHECK_PERMISSION: '/api/membership/check-permission'
    },
    // 订单相关接口
    ORDER: {
      LIST: '/api/order/list',
      DETAIL: '/api/order/detail',
      CREATE: '/api/order/create'
    },
    // 支付相关接口
    PAYMENT: {
      CREATE: '/api/payment/create',
      QUERY: '/api/payment/query',
      NOTIFY: '/api/payment/notify'
    }
  },
  
  // 内容安全检测服务接口
  MEDIA_CHECK: {
    BASE_URL: getCurrentConfig().MEDIA_CHECK,
    // OpenID获取接口
    GET_OPENID: '/api/get-openid',
    // 图片安全检测接口
    CHECK_IMAGE: '/api/check-image'
  }
}

// 请求超时配置
export const TIMEOUT_CONFIG = {
  DEFAULT: 10000,      // 默认超时时间 10秒
  UPLOAD: 30000,       // 上传超时时间 30秒
  PAYMENT: 15000       // 支付超时时间 15秒
}

// 重试配置
export const RETRY_CONFIG = {
  MAX_RETRIES: 2,      // 最大重试次数
  RETRY_DELAY: 1000    // 重试延迟时间（毫秒）
}

// 构建完整的API URL
export function buildApiUrl(service, endpoint) {
  const config = API_CONFIG[service]
  if (!config) {
    throw new Error(`未知的服务: ${service}`)
  }
  
  return config.BASE_URL + endpoint
}

// 构建图像压缩服务API URL
export function buildImageCompressionUrl(endpoint) {
  return buildApiUrl('IMAGE_COMPRESSION', endpoint)
}

// 构建内容检测服务API URL
export function buildMediaCheckUrl(endpoint) {
  return buildApiUrl('MEDIA_CHECK', endpoint)
}

// 获取用户登录API URL
export function getUserLoginUrl() {
  return buildImageCompressionUrl(API_CONFIG.IMAGE_COMPRESSION.USER.LOGIN)
}

// 获取会员状态API URL
export function getMemberStatusUrl() {
  return buildImageCompressionUrl(API_CONFIG.IMAGE_COMPRESSION.MEMBERSHIP.STATUS)
}

// 获取商品列表API URL
export function getProductListUrl() {
  return buildImageCompressionUrl(API_CONFIG.IMAGE_COMPRESSION.MEMBERSHIP.PRODUCTS)
}

// 获取订单列表API URL
export function getOrderListUrl() {
  return buildImageCompressionUrl(API_CONFIG.IMAGE_COMPRESSION.ORDER.LIST)
}

// 获取OpenID API URL
export function getOpenIdUrl() {
  return buildMediaCheckUrl(API_CONFIG.MEDIA_CHECK.GET_OPENID)
}

// 获取支付创建URL
export function getPaymentCreateUrl() {
  return buildImageCompressionUrl(API_CONFIG.IMAGE_COMPRESSION.PAYMENT.CREATE)
}

// 获取支付查询URL
export function getPaymentQueryUrl() {
  return buildImageCompressionUrl(API_CONFIG.IMAGE_COMPRESSION.PAYMENT.QUERY)
}

// 导出默认配置
export default {
  APP_CONFIG,
  API_CONFIG,
  TIMEOUT_CONFIG,
  RETRY_CONFIG,
  buildApiUrl,
  buildImageCompressionUrl,
  buildMediaCheckUrl,
  getUserLoginUrl,
  getMemberStatusUrl,
  getProductListUrl,
  getOrderListUrl,
  getOpenIdUrl
}
