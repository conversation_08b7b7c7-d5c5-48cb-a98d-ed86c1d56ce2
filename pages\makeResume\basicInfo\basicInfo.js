const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    basicInfoFormData: null, // 将在 onLoad 中初始化为 BasicInfo 实例
    genderArray: ['男', '女'],
    genderIndex: -1,
    marriageArray: ['未婚', '已婚', '离异', '丧偶'],
    marriageIndex: -1,
    politicsArray: ['群众', '共青团员', '中共党员', '民主党派'],
    politicsIndex: -1
  },

  onLoad() {
    this.loadBasicInfoData();
  },

  /**
   * 从全局管理器加载基本信息数据
   */
  loadBasicInfoData() {
    try {
      const basicInfoData = ResumeFormHelper.loadFieldData('basicInfo', app);

      this.setData({
        basicInfoFormData: basicInfoData,
        genderIndex: this.data.genderArray.indexOf(basicInfoData.gender || ''),
        marriageIndex: this.data.marriageArray.indexOf(basicInfoData.marriage || ''),
        politicsIndex: this.data.politicsArray.indexOf(basicInfoData.politics || '')
      });
    } catch (error) {
      console.error('❌ 加载基本信息数据失败:', error);
      // 出错时使用空数据
      const emptyBasicInfo = ResumeFormHelper.getEmptyFieldData('basicInfo');
      this.setData({
        basicInfoFormData: emptyBasicInfo,
        genderIndex: -1,
        marriageIndex: -1,
        politicsIndex: -1
      });
    }
  },

  // 处理输入框内容变化
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`basicInfoFormData.${field}`]: value
    });
  },

  // 处理性别选择器
  handleGenderPicker(e) {
    const index = e.detail.value;
    this.setData({
      genderIndex: index,
      'basicInfoFormData.gender': this.data.genderArray[index]
    });
  },

  // 处理婚姻状况选择器
  handleMarriagePicker(e) {
    const index = parseInt(e.detail.value);
    if (index >= 0 && index < this.data.marriageArray.length) {
      this.setData({
        marriageIndex: index,
        'basicInfoFormData.marriage': this.data.marriageArray[index]
      });
    }
  },

  // 处理政治面貌选择器
  handlePoliticsPicker(e) {
    const index = parseInt(e.detail.value);
    if (index >= 0 && index < this.data.politicsArray.length) {
      this.setData({
        politicsIndex: index,
        'basicInfoFormData.politics': this.data.politicsArray[index]
      });
    }
  },

  // 处理日期选择器
  handlePicker(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`basicInfoFormData.${field}`]: value
    });
  },

  // 清除字段内容
  clearField(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`basicInfoFormData.${field}`]: ''
    });
  },

  // 清除自定义字段
  clearCustomField(e) {
    const { index } = e.currentTarget.dataset;
    this.setData({
      [`basicInfoFormData.customTitle${index}`]: '',
      [`basicInfoFormData.customContent${index}`]: ''
    });
  },



  /**
   * 保存基本信息
   */
  saveInfo() {
    try {
      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('basicInfo', this.data.basicInfoFormData, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        // 延迟返回，让用户看到保存成功的提示
        setTimeout(() => {
          wx.navigateBack({
            delta: 1  // 返回上一级页面
          });
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('❌ 保存基本信息时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除基本信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有信息吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用 ResumeFormHelper 清空数据
            const success = ResumeFormHelper.clearFieldData('basicInfo', app);

            if (success) {
              // 更新页面显示
              const emptyBasicInfo = ResumeFormHelper.getEmptyFieldData('basicInfo');
              this.setData({
                basicInfoFormData: emptyBasicInfo,
                genderIndex: -1,
                marriageIndex: -1,
                politicsIndex: -1
              });

              wx.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });

              // 延迟返回，让用户看到删除成功的提示
              setTimeout(() => {
                // 返回上一页并刷新数据
                const pages = getCurrentPages();
                const prePage = pages[pages.length - 2];
                if (prePage) {
                  prePage.onShow(); // 调用上一页的onShow方法刷新数据
                }
                wx.navigateBack({
                  delta: 1
                });
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('❌ 删除基本信息时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});