/**
 * 商品管理器
 * 处理商品信息获取、缓存等功能
 */

import { getProductListUrl } from '@/config/api.js'
import { apiGet, handleApiError } from '@/utils/apiRequest.js'
import { APP_CONFIG } from '@/config/api.js'

// 存储键名
const PRODUCTS_KEY = 'products_cache'
const PRODUCTS_CACHE_TIME_KEY = 'products_cache_time'

// 缓存有效期（毫秒）- 1小时
const CACHE_DURATION = 60 * 60 * 1000

/**
 * 获取商品列表
 * @param {boolean} forceRefresh 是否强制刷新
 * @returns {Promise<Array>} 商品列表
 */
export async function getProductList(forceRefresh = false) {
  try {
    console.log('🔍 开始获取商品列表...');
    
    // 检查缓存是否有效
    if (!forceRefresh && isCacheValid()) {
      const cachedProducts = uni.getStorageSync(PRODUCTS_KEY);
      if (cachedProducts && Array.isArray(cachedProducts)) {
        console.log('📱 使用缓存的商品列表:', cachedProducts.length, '个商品');
        return cachedProducts;
      }
    }
    
    // 从API获取商品列表
    try {
      const apiUrl = getProductListUrl() + '?appId=' + encodeURIComponent('wx76b4aebe290bf455');
      console.log('🔍 [ProductManager] 请求API:', apiUrl);

      const result = await apiGet(apiUrl);
      console.log('📦 [ProductManager] API原始返回:', JSON.stringify(result, null, 2));

      if (result.success && result.data && Array.isArray(result.data.products)) {
        console.log('🔄 [ProductManager] 原始产品数据:', JSON.stringify(result.data.products, null, 2));

        const products = result.data.products.map(product => {
          console.log('🔄 [ProductManager] 处理产品:', product);
          const mappedProduct = {
            id: product.productId,
            productCode: product.productCode,  // 保持原始字段名
            type: product.productCode,         // 同时提供type字段用于兼容
            name: product.productName,
            price: product.price,
            originalPrice: product.originalPrice || product.price,
            durationHours: product.durationHours,
            description: product.description,
            isActive: product.isActive !== false
          };
          console.log('✅ [ProductManager] 映射后产品:', mappedProduct);
          return mappedProduct;
        });

        // 缓存商品列表
        uni.setStorageSync(PRODUCTS_KEY, products);
        uni.setStorageSync(PRODUCTS_CACHE_TIME_KEY, Date.now());

        console.log('✅ [ProductManager] 最终产品列表:', JSON.stringify(products, null, 2));
        return products;
      } else {
        throw new Error('商品数据格式错误');
      }
    } catch (apiError) {
      console.warn('⚠️ API获取商品列表失败，使用默认配置:', apiError.message);
      
      // 如果API失败，返回默认商品配置
      const defaultProducts = getDefaultProducts();
      
      // 缓存默认配置（短时间缓存）
      uni.setStorageSync(PRODUCTS_KEY, defaultProducts);
      uni.setStorageSync(PRODUCTS_CACHE_TIME_KEY, Date.now() - CACHE_DURATION + 5 * 60 * 1000); // 5分钟后过期
      
      return defaultProducts;
    }
  } catch (error) {
    console.error('❌ 获取商品列表失败:', error);
    handleApiError(error, { showToast: false });
    
    // 返回默认商品配置
    return getDefaultProducts();
  }
}

/**
 * 根据商品类型获取商品信息
 * @param {string} productType 商品类型
 * @returns {Promise<Object|null>} 商品信息
 */
export async function getProductByType(productType, forceRefresh = false) {
  try {
    console.log('🔍 [ProductManager] 查找产品类型:', productType, forceRefresh ? '(强制刷新)' : '(使用缓存)');
    const products = await getProductList(forceRefresh);
    console.log('📦 [ProductManager] 可用产品列表:', JSON.stringify(products, null, 2));

    const foundProduct = products.find(product => product.type === productType || product.productCode === productType) || null;
    console.log('🎯 [ProductManager] 查找结果:', foundProduct);
    
    return foundProduct;
  } catch (error) {
    console.error('❌ 根据类型获取商品失败:', error);
    return null;
  }
}

/**
 * 检查缓存是否有效
 * @returns {boolean} 缓存是否有效
 */
function isCacheValid() {
  try {
    const cacheTime = uni.getStorageSync(PRODUCTS_CACHE_TIME_KEY);
    if (!cacheTime) return false;
    
    const now = Date.now();
    const isValid = (now - cacheTime) < CACHE_DURATION;
    
    console.log('🕒 缓存检查:', {
      cacheTime: new Date(cacheTime).toLocaleString(),
      now: new Date(now).toLocaleString(),
      age: Math.round((now - cacheTime) / 1000 / 60) + '分钟',
      isValid
    });
    
    return isValid;
  } catch (error) {
    console.error('检查缓存有效性失败:', error);
    return false;
  }
}

/**
 * 清除产品缓存
 */
export function clearProductCache() {
  try {
    uni.removeStorageSync(PRODUCTS_KEY);
    uni.removeStorageSync(PRODUCTS_CACHE_TIME_KEY);
    console.log('🗑️ 产品缓存已清除');
  } catch (error) {
    console.error('清除产品缓存失败:', error);
  }
}

/**
 * 获取默认商品配置
 * @returns {Array} 默认商品列表
 */
function getDefaultProducts() {
  return [
    {
      id: 'default_day',
      productCode: 'day_card',
      type: 'day_card',
      name: '日卡会员',
      price: 0.01,
      originalPrice: 0.01,
      durationHours: 24,
      description: '24小时无限压缩',
      isActive: true,
      features: ['无限次压缩', '批量处理', '高质量输出']
    },
    {
      id: 'default_permanent',
      productCode: 'permanent',
      type: 'permanent',
      name: '永久会员',
      price: 0.02,
      originalPrice: 0.02,
      durationHours: null,
      description: '永久无限压缩',
      isActive: true,
      features: ['永久有效', '无限次压缩', '批量处理', '高质量输出', '优先客服']
    }
  ];
}

/**
 * 刷新商品列表
 * @returns {Promise<Array>} 最新的商品列表
 */
export async function refreshProductList() {
  console.log('🔄 强制刷新商品列表...');
  clearProductCache();
  return await getProductList(true);
}

/**
 * 获取商品的显示价格
 * @param {Object} product 商品对象
 * @returns {string} 格式化的价格字符串
 */
export function getProductDisplayPrice(product) {
  if (!product || typeof product.price !== 'number') {
    return '价格未知';
  }
  
  return `¥${product.price.toFixed(2)}`;
}

/**
 * 获取商品的折扣信息
 * @param {Object} product 商品对象
 * @returns {Object} 折扣信息
 */
export function getProductDiscount(product) {
  if (!product || !product.originalPrice || product.originalPrice <= product.price) {
    return { hasDiscount: false, discount: 0, discountText: '' };
  }
  
  const discount = Math.round((1 - product.price / product.originalPrice) * 100);
  return {
    hasDiscount: true,
    discount,
    discountText: `${discount}折`
  };
}

/**
 * 检查商品是否可用
 * @param {Object} product 商品对象
 * @returns {boolean} 商品是否可用
 */
export function isProductAvailable(product) {
  return product && product.isActive !== false;
}

/**
 * 获取日卡商品信息
 * @param {boolean} forceRefresh 是否强制刷新
 * @returns {Promise<Object|null>} 日卡商品信息
 */
export async function getDayCardProduct(forceRefresh = false) {
  return await getProductByType('day_card', forceRefresh);
}

/**
 * 获取永久会员商品信息
 * @param {boolean} forceRefresh 是否强制刷新
 * @returns {Promise<Object|null>} 永久会员商品信息
 */
export async function getPermanentProduct(forceRefresh = false) {
  return await getProductByType('permanent', forceRefresh);
}
