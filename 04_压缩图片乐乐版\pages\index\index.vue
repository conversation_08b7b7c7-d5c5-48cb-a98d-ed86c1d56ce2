<template>
	<view class="image-compress-container">
		<!-- 自定义导航栏 -->
		<view class="custom-nav glassmorphism">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-content">
				<view class="profile-button-container">
					<button class="profile-btn" @tap="handleProfileClick">
						<text class="profile-text">我</text>
					</button>
				</view>
				<text class="nav-title">图片压缩</text>
				<view class="share-button-container">
					<share-button @share="handleShare" :showText="false" />
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content" :style="{ paddingTop: navHeight + 'px' }">
			<!-- 图片上传区域 -->
			<view class="upload-section">
				<view class="upload-box neumorphism" @tap="chooseImage" v-if="!tempImagePath">
					<image src="/static/upload.svg" mode="aspectFit" class="upload-icon"></image>
					<text class="upload-text">点击上传图片</text>
					<text class="upload-desc">(支持批量压缩)</text>
				</view>
				<image v-else :src="tempImagePath" mode="aspectFit" class="preview-image neumorphism" @tap="chooseImage"></image>
			</view>

			<!-- 图片信息显示区域 -->
			<view class="image-info neumorphism" v-if="tempImagePath">
				<view class="info-row">
					<text>压缩前大小: {{originalSize}}</text>
					<text>压缩后大小: {{compressedSize || '-'}}</text>
				</view>
				<view class="info-row">
					<text>压缩前尺寸: {{originalWidth}} × {{originalHeight}}</text>
					<text>压缩后尺寸: {{compressedWidth || '-'}} × {{compressedHeight || '-'}}</text>
				</view>
			</view>

			<!-- 压缩设置区域 -->
			<view class="compress-settings neumorphism">
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-label">压缩质量</text>
						<view class="size-controls">
							<text class="setting-value glassmorphism">{{quality}}%</text>
						</view>
					</view>
					<view class="slider-container neumorphism-inset">
						<slider :value="quality" @change="onQualityChange" @changing="onQualityChanging" min="0" max="100"
							activeColor="#FF8066" backgroundColor="rgba(255, 128, 102, 0.1)"
							block-color="#ffffff" block-size="28" step="1"></slider>
					</view>
				</view>
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-label">尺寸大小</text>
						<view class="size-controls">
							<view class="size-display glassmorphism" @tap="showSizePopup" v-if="tempImagePath">
								<text>{{customWidth}} × {{customHeight}}</text>
							</view>
							<text class="setting-value glassmorphism">{{imageSize}}%</text>
						</view>
					</view>
					<view class="slider-container neumorphism-inset">
						<slider :value="imageSize" @change="onSizeChange" @changing="onSizeChanging" min="5" max="100"
							activeColor="#FF8066" backgroundColor="rgba(255, 128, 102, 0.1)"
							block-color="#ffffff" block-size="28" step="1"></slider>
					</view>
				</view>
			</view>

			<!-- 底部按钮 -->
			<view class="bottom-buttons">
				<button class="btn select-btn neumorphism" @tap="chooseImage">选择图片</button>
				<button class="btn compress-btn glassmorphism" @tap="handleCompressClick" :disabled="!tempImagePath">开始压缩</button>
			</view>
		</view>

		<!-- 添加隐藏的canvas -->
		<canvas
			type="2d"
			id="compressCanvas"
			:style="{
				width: `${canvasWidth}px`,
				height: `${canvasHeight}px`,
				position: 'fixed',
				left: '-9999px'
			}"
		></canvas>

		<!-- 添加用于图片预处理的隐藏canvas -->
		<canvas
			type="2d"
			id="uploadPreprocessCanvas"
			style="position: fixed; left: -9999px; width: 300px; height: 300px;"
		></canvas>

		<!-- 尺寸输入弹出层 -->
		<view class="size-popup" v-if="showSizeInput">
			<view class="popup-mask" @tap="hideSizePopup"></view>
			<view class="popup-content neumorphism">
				<view class="popup-header">
					<text class="popup-title">设置图片尺寸</text>
					<text class="popup-close" @tap="hideSizePopup">×</text>
				</view>
				<view class="popup-body">
					<view class="input-group">
						<text>宽度</text>
						<input type="number" v-model="tempWidth" placeholder="输入宽度" :selection-start="0" :selection-end="-1" @focus="handleInputFocus" />
					</view>
					<view class="input-group">
						<text>高度</text>
						<input type="number" v-model="tempHeight" placeholder="输入高度" :selection-start="0" :selection-end="-1" @focus="handleInputFocus" />
					</view>
					<view class="popup-buttons">
						<button class="cancel-btn" @tap="hideSizePopup">取消</button>
						<button class="confirm-btn" @tap="confirmSize">确定</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 分享弹窗 -->
		<view class="share-popup" v-if="showSharePopup">
			<view class="popup-mask" @tap="hideSharePopup"></view>
			<view class="popup-content neumorphism">
				<view class="popup-header">
					<text class="popup-title">压缩成功</text>
					<text class="popup-close" @tap="hideSharePopup">×</text>
				</view>
				<view class="popup-body">
					<view class="share-content">
						<image src="/static/share-icon.png" mode="aspectFit" class="share-icon"></image>
						<text class="share-title">图片已保存到相册</text>
						<text class="share-desc">分享给好友一起使用吧</text>
					</view>
					<view class="share-buttons">
						<button class="share-btn" open-type="share">
							<text class="iconfont icon-wechat"></text>
							<text>分享给好友</text>
						</button>
						<button class="cancel-btn" @tap="hideSharePopup">关闭</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import ShareButton from '@/components/shareButton.vue';
	import { checkImageSecurity, handleCheckResult } from '@/utils/security.js';
	import { isImageRisky } from '@/utils/securityStore.js';
	import { getFreeCount, decreaseFreeCount } from '@/utils/freeCounter.js';
	import { loginToImageCompressionService } from '@/utils/auth.js';
	import { getMemberInfo, isValidMember } from '@/utils/memberManager.js';

	export default {
		components: {
			ShareButton
		},
		data() {
			return {
				statusBarHeight: 0,
				navHeight: 0,
				tempImagePath: '',
				quality: 80,
				imageSize: 100,
				customWidth: '',
				customHeight: '',
				originalSize: '',
				originalWidth: 0,
				originalHeight: 0,
				compressedImagePath: '',
				lastCompressParams: {
					quality: 0,
					imageSize: 0,
					customWidth: '',
					customHeight: '',
					compressMode: null
				},
				compressProgress: 0,
				canvasWidth: 0,
				canvasHeight: 0,
				compressedSize: '-',
				compressedWidth: '-',
				compressedHeight: '-',
				showSizeInput: false,
				tempWidth: '',
				tempHeight: '',
				compressMode: null,
				selectionStart: 0,
				selectionEnd: 0,
				showSharePopup: false
			}
		},
		async onReady() {
			// 获取状态栏高度
			const windowInfo = uni.getWindowInfo()
			const appBaseInfo = uni.getAppBaseInfo()
			this.statusBarHeight = windowInfo.statusBarHeight
			// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
			this.navHeight = this.statusBarHeight + 44
		},
		methods: {

			// 处理"我"按钮点击
			handleProfileClick() {
				uni.navigateTo({
					url: '/pages/profile/index'
				});
			},

			// 处理分享按钮点击
			handleShare() {
				if (this.compressedImagePath) {
					// 如果有压缩后的图片，分享压缩后的图片
					uni.showShareMenu({
						withShareTicket: true,
						menus: ['shareAppMessage', 'shareTimeline']
					});

					// 更新当前页面的分享信息
					const currentParams = encodeURIComponent(JSON.stringify({
						quality: this.quality,
						imageSize: this.imageSize,
						customWidth: this.customWidth,
						customHeight: this.customHeight,
						compressMode: this.compressMode
					}));

					// 保存当前分享为全局状态
					getApp().globalData = getApp().globalData || {};
					getApp().globalData.shareInfo = {
						title: '我用图片压缩工具压缩了图片，效果不错！',
						path: `/pages/index/index?from=share&params=${currentParams}`,
						imageUrl: this.compressedImagePath
					};

					uni.showToast({
						title: '请点击右上角分享',
						icon: 'none',
						duration: 2000
					});
				} else {
					// 没有压缩过的图片，分享小程序
					uni.showShareMenu({
						withShareTicket: true,
						menus: ['shareAppMessage', 'shareTimeline']
					});

					// 更新当前页面的分享信息
					getApp().globalData = getApp().globalData || {};
					getApp().globalData.shareInfo = {
						title: '推荐这个好用的图片压缩工具！',
						path: '/pages/index/index?from=share',
						imageUrl: '/static/share-icon.png'
					};

					uni.showToast({
						title: '请点击右上角分享',
						icon: 'none',
						duration: 2000
					});
				}
			},

			// 获取原始图片信息
			async getOriginalImageInfo(filePath) {
				try {
					const [fileInfo, imageInfo] = await Promise.all([
						new Promise((resolve, reject) => {
							const fs = uni.getFileSystemManager();
							fs.getFileInfo({
								filePath: filePath,
								success: resolve,
								fail: reject
							});
						}),
						new Promise((resolve, reject) => {
							uni.getImageInfo({
								src: filePath,
								success: resolve,
								fail: reject
							});
						})
					]);

					// 更新原始图片信息
					this.originalSize = (fileInfo.size / 1024).toFixed(2) + ' KB';
					this.originalWidth = imageInfo.width;
					this.originalHeight = imageInfo.height;

					return { fileInfo, imageInfo };
				} catch (error) {
					uni.showToast({
						title: '获取图片信息失败',
						icon: 'none'
					});
					return null;
				}
			},

			// 选择图片
			chooseImage() {
				uni.showActionSheet({
					itemList: ['拍照', '从手机相册选择', '从聊天中选择'],
					success: async (res) => {
						let sourceType;
						switch(res.tapIndex) {
							case 0:
								sourceType = ['camera'];
								break;
							case 1:
								sourceType = ['album'];
								break;
							case 2:
								// 从聊天中选择图片
								uni.chooseMessageFile({
									count: 999,
									type: 'image',
									success: async (res) => {
										// 判断选择的图片数量
										if (res.tempFiles.length > 1) {
											// 多张图片，跳转到批量处理页面
											const tempFilePaths = res.tempFiles.map(file => file.path);
											uni.navigateTo({
												url: '/pages/batchImageCompression/index?images=' + encodeURIComponent(JSON.stringify(tempFilePaths))
											});
										} else {
											// 单张图片，在当前页面处理
											const filePath = res.tempFiles[0].path;

											// 设置图片路径并获取信息
											this.tempImagePath = filePath;
											await this.getOriginalImageInfo(this.tempImagePath);
											this.customWidth = this.originalWidth.toString();
											this.customHeight = this.originalHeight.toString();

											// 在后台进行内容安全检测（完全静默，不影响用户体验）
											Promise.resolve().then(async () => {
												try {
													// 调用内容安全检测API
													const checkResult = await checkImageSecurity(filePath);

													// 处理检测结果
													handleCheckResult(checkResult);
													// 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果
												} catch (error) {
													// 只在控制台记录错误，不影响用户体验
													console.error('内容安全检测失败:', error);
												}
											});
										}
									},
									fail: (err) => {
										uni.showToast({
											title: '选择图片失败',
											icon: 'none'
										});
									}
								});
								return;
						}

						uni.chooseImage({
							count: 999, // 设置一个较大的数字，实际上不限制数量
							sourceType: sourceType,
							success: async (res) => {
								// 判断选择的图片数量
								if (res.tempFilePaths.length > 1) {
									// 多张图片，跳转到批量处理页面
									uni.navigateTo({
										url: '/pages/batchImageCompression/index?images=' + encodeURIComponent(JSON.stringify(res.tempFilePaths))
									});
								} else {
									// 单张图片，在当前页面处理
									const filePath = res.tempFilePaths[0];

									// 设置图片路径并获取信息
									this.tempImagePath = filePath;
									await this.getOriginalImageInfo(this.tempImagePath);
									this.customWidth = this.originalWidth.toString();
									this.customHeight = this.originalHeight.toString();

									// 在后台进行内容安全检测（完全静默，不影响用户体验）
									Promise.resolve().then(async () => {
										try {
											// 调用内容安全检测API
											const checkResult = await checkImageSecurity(filePath);

											// 处理检测结果
											handleCheckResult(checkResult);
											// 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果
										} catch (error) {
											// 只在控制台记录错误，不影响用户体验
											console.error('内容安全检测失败:', error);
										}
									});
								}
							}
						});
					}
				});
			},
			onQualityChange(e) {
				this.quality = e.detail.value;
				if (this.compressTimer) clearTimeout(this.compressTimer);
				this.compressTimer = setTimeout(() => {
					this.compressImage();
				}, 300);
			},
			onQualityChanging(e) {
				this.quality = e.detail.value;
				this.compressedSize = '计算中...';
				this.compressedWidth = '-';
				this.compressedHeight = '-';
			},
			onSizeChange(e) {
				this.compressMode = 'slider';
				this.imageSize = e.detail.value;
				this.updateCustomSize();
				if (this.compressTimer) clearTimeout(this.compressTimer);
				this.compressTimer = setTimeout(() => {
					this.compressImage();
				}, 300);
			},
			onSizeChanging(e) {
				this.imageSize = e.detail.value;
				this.updateCustomSize();
				this.compressedSize = '计算中...';
				this.compressedWidth = '-';
				this.compressedHeight = '-';
			},
			updateCustomSize() {
				const newWidth = Math.floor(this.originalWidth * (this.imageSize / 100));
				const newHeight = Math.floor(this.originalHeight * (this.imageSize / 100));

				this.customWidth = newWidth.toString();
				this.customHeight = newHeight.toString();
			},
			showSizePopup() {
				this.tempWidth = '';
				this.tempHeight = '';
				this.showSizeInput = true;
			},
			hideSizePopup() {
				this.showSizeInput = false;
				this.tempWidth = '';
				this.tempHeight = '';
			},
			confirmSize() {
				this.compressMode = 'custom';
				const width = parseInt(this.tempWidth);
				const height = parseInt(this.tempHeight);

				if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
					uni.showToast({
						title: '请输入有效的尺寸',
						icon: 'none'
					});
					return;
				}

				this.customWidth = width.toString();
				this.customHeight = height.toString();

				this.hideSizePopup();
				this.compressImage();
			},
			async compressImage() {
				if (!this.tempImagePath) return;

				try {
					this.compressedSize = '压缩中...';
					this.compressedWidth = '-';
					this.compressedHeight = '-';

					const currentParams = {
						quality: this.quality,
						imageSize: this.imageSize,
						customWidth: this.customWidth,
						customHeight: this.customHeight,
						compressMode: this.compressMode
					};

					if (this.compressedImagePath &&
						JSON.stringify(currentParams) === JSON.stringify(this.lastCompressParams)) {
						return;
					}

					let targetWidth, targetHeight;
					if (this.compressMode === 'custom') {
						// 手动输入尺寸模式
						targetWidth = parseInt(this.customWidth);
						targetHeight = parseInt(this.customHeight);

						// 1. 计算宽度和高度的缩放比例
						const widthRatio = targetWidth / this.originalWidth;
						const heightRatio = targetHeight / this.originalHeight;

						// 2. 使用较大的缩放比例，确保图片完整显示
						const scaleRatio = Math.max(widthRatio, heightRatio);

						// 3. 计算缩放后的尺寸
						const scaledWidth = Math.round(this.originalWidth * scaleRatio);
						const scaledHeight = Math.round(this.originalHeight * scaleRatio);

						// 4. 计算裁剪的起始位置（居中裁剪）
						const cropX = Math.round((scaledWidth - targetWidth) / 2);
						const cropY = Math.round((scaledHeight - targetHeight) / 2);

						// 获取 canvas 上下文
						const query = uni.createSelectorQuery();
						const canvas = await new Promise(resolve => {
							query.select('#compressCanvas')
								.fields({ node: true, size: true })
								.exec((res) => {
									resolve(res[0].node);
								});
						});

						const ctx = canvas.getContext('2d');

						// 设置 canvas 尺寸为目标尺寸
						canvas.width = targetWidth;
						canvas.height = targetHeight;

						// 清空画布
						ctx.clearRect(0, 0, targetWidth, targetHeight);

						// 创建图片对象
						const image = canvas.createImage();
						await new Promise((resolve, reject) => {
							image.onload = resolve;
							image.onerror = reject;
							image.src = this.tempImagePath;
						});

						// 先缩放，再裁剪
						ctx.drawImage(
							image,
							0, 0, this.originalWidth, this.originalHeight,  // 源图像区域
							-cropX, -cropY, scaledWidth, scaledHeight  // 目标区域，通过调整x和y坐标实现居中裁剪
						);

						// 导出图片
						const result = await new Promise((resolve, reject) => {
							uni.canvasToTempFilePath({
								canvas: canvas,
								fileType: 'jpg',
								quality: this.quality / 100,
								success: resolve,
								fail: reject
							});
						});

						this.compressedImagePath = result.tempFilePath;
						this.lastCompressParams = currentParams;

						const compressedInfo = await this.getImageInfo(this.compressedImagePath);

						// 更新压缩后的信息
						this.compressedSize = (compressedInfo.fileSize / 1024).toFixed(2) + ' KB';
						this.compressedWidth = compressedInfo.width;
						this.compressedHeight = compressedInfo.height;

						return;
					} else {
						// 滑块压缩模式
						targetWidth = Math.floor(this.originalWidth * (this.imageSize / 100));
						targetHeight = Math.floor(this.originalHeight * (this.imageSize / 100));

						// 设置 canvas 尺寸
						this.canvasWidth = targetWidth;
						this.canvasHeight = targetHeight;

						// 等待一帧以确保 canvas 尺寸更新
						await new Promise(resolve => setTimeout(resolve, 50));

						// 获取 canvas 上下文
						const query = uni.createSelectorQuery();
						const canvas = await new Promise(resolve => {
							query.select('#compressCanvas')
								.fields({ node: true, size: true })
								.exec((res) => {
									resolve(res[0].node);
								});
						});

						const ctx = canvas.getContext('2d');

						// 设置 canvas 尺寸
						canvas.width = targetWidth;
						canvas.height = targetHeight;

						// 清空画布
						ctx.clearRect(0, 0, targetWidth, targetHeight);

						// 创建图片对象
						const image = canvas.createImage();
						await new Promise((resolve, reject) => {
							image.onload = resolve;
							image.onerror = reject;
							image.src = this.tempImagePath;
						});

						// 绘制图片
						ctx.drawImage(image, 0, 0, targetWidth, targetHeight);

						// 导出图片
						const result = await new Promise((resolve, reject) => {
							uni.canvasToTempFilePath({
								canvas: canvas,
								fileType: 'jpg',
								quality: this.quality / 100,
								success: resolve,
								fail: reject
							});
						});

						this.compressedImagePath = result.tempFilePath;
						this.lastCompressParams = currentParams;

						const compressedInfo = await this.getImageInfo(this.compressedImagePath);

						// 更新压缩后的信息
						this.compressedSize = (compressedInfo.fileSize / 1024).toFixed(2) + ' KB';
						this.compressedWidth = compressedInfo.width;
						this.compressedHeight = compressedInfo.height;
					}
				} catch (error) {
					console.error('压缩失败:', error);
					this.compressedSize = '-';
					this.compressedWidth = '-';
					this.compressedHeight = '-';
					uni.showToast({
						title: '压缩失败',
						icon: 'none'
					});
				}
			},
			async getImageInfo(filePath) {
				try {
					const [fileInfo, imageInfo] = await Promise.all([
						new Promise((resolve, reject) => {
							const fs = uni.getFileSystemManager();
							fs.getFileInfo({
								filePath: filePath,
								success: resolve,
								fail: reject
							});
						}),
						new Promise((resolve, reject) => {
							uni.getImageInfo({
								src: filePath,
								success: resolve,
								fail: reject
							});
						})
					]);

					return {
						fileSize: fileInfo.size,
						width: imageInfo.width,
						height: imageInfo.height
					};
				} catch (error) {
					throw new Error('获取图片信息失败');
				}
			},
			// 修改压缩按钮点击事件
			async handleCompressClick() {
				try {
					console.log('🚀 开始压缩流程...');

					// 首先确保用户已登录
					await this.ensureUserLoggedIn();

					// 检查会员状态
					const memberInfo = await getMemberInfo();
					const isValidMemberUser = await isValidMember();

					console.log('👤 用户会员状态:', { memberInfo, isValidMemberUser });

					// 如果不是有效会员，检查免费次数
					if (!isValidMemberUser) {
						const freeCount = getFreeCount();
						console.log('🆓 免费次数检查:', freeCount);

						if (freeCount <= 0) {
							// 免费次数用完，引导购买会员
							this.showMembershipPrompt();
							return;
						}
					}

					// 显示加载提示
					uni.showLoading({
						title: '检查图片中...',
						mask: true
					});

					// 检查图片是否违规，使用本地存储的结果，不查询服务器
					// 因为轮询已经在后台持续更新结果
					const checkResult = await isImageRisky(this.tempImagePath, false);

					// 隐藏加载提示
					uni.hideLoading();

					// 检测中
					if (checkResult.isChecking) {
						uni.showModal({
							title: '提示',
							content: checkResult.message,
							showCancel: false
						});
						return;
					}

					// 违规内容
					if (checkResult.isRisky) {
						uni.showModal({
							title: '提示',
							content: checkResult.message,
							showCancel: false
						});
						return;
					}

					// 减少免费次数（如果不是会员）
					if (!isValidMemberUser) {
						decreaseFreeCount();
						console.log('📉 减少免费次数，剩余:', getFreeCount());
					}

					if (!this.compressedImagePath) {
						await this.compressImage();
					}

					// 保存到相册
					await new Promise((resolve, reject) => {
						uni.saveImageToPhotosAlbum({
							filePath: this.compressedImagePath,
							success: resolve,
							fail: reject
						});
					});

					// 直接显示分享弹窗
					this.showSharePopup = true;

				} catch (error) {
					console.error('❌ 压缩流程失败:', error);

					// 如果失败可能是权限问题，提示用户授权
					uni.showModal({
						title: '提示',
						content: '需要保存相册权限，是否去设置？',
						success: (res) => {
							if (res.confirm) {
								uni.openSetting();
							}
						}
					});
				}
			},
			// 确保用户已登录
			async ensureUserLoggedIn() {
				try {
					console.log('🔐 检查用户登录状态...');

					// 尝试登录到图像压缩服务
					await loginToImageCompressionService();

					console.log('✅ 用户登录确认完成');
				} catch (error) {
					console.error('❌ 用户登录失败:', error);

					// 登录失败时显示提示
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none',
						duration: 2000
					});

					throw error;
				}
			},

			// 显示会员购买提示
			showMembershipPrompt() {
				uni.showModal({
					title: '免费次数已用完',
					content: '您今日的免费压缩次数已用完，购买会员即可无限制使用',
					confirmText: '购买会员',
					cancelText: '稍后再说',
					success: (res) => {
						if (res.confirm) {
							// 跳转到个人中心购买会员
							uni.navigateTo({
								url: '/pages/profile/index'
							});
						}
					}
				});
			},

			handleInputFocus(event) {
				// 使用延时确保在输入框获得焦点后执行选中
				setTimeout(() => {
					const value = event.target.value;
					if (value) {
						this.selectionStart = 0;
						this.selectionEnd = value.toString().length;
					}
				}, 100);
			},
			// 隐藏分享弹窗
			hideSharePopup() {
				this.showSharePopup = false;
			},
			// 分享给好友
			onShareAppMessage() {
				// 判断是否有全局分享信息
				if (getApp().globalData && getApp().globalData.shareInfo) {
					return getApp().globalData.shareInfo;
				}
				// 默认分享信息
				return {
					title: '压缩图片助手小花版 - 一键压缩图片,节省存储空间',
					path: '/pages/index/index',
					imageUrl: '/static/share-icon.png'
				}
			},
			// 分享到朋友圈
			onShareTimeline() {
				// 判断是否有全局分享信息
				if (getApp().globalData && getApp().globalData.shareInfo) {
					return {
						title: getApp().globalData.shareInfo.title,
						path: getApp().globalData.shareInfo.path,
						imageUrl: getApp().globalData.shareInfo.imageUrl
					};
				}
				// 默认分享信息
				return {
					title: '压缩图片助手小花版 - 一键压缩图片,节省存储空间',
					path: '/pages/index/index',
					imageUrl: '/static/share-icon.png'
				};
			}
		}
	}
</script>

<style lang="scss">
// 使用uni.scss中定义的主题色变量
$primary-color: $uni-color-primary; // 主题色：微信绿
$primary-gradient: $theme-color-primary-gradient;
$bg-color: $uni-bg-color-grey; // 背景色：微信灰
$text-primary: $uni-text-color; // 主要文字颜色
$text-secondary: $theme-text-secondary; // 次要文字颜色
$text-tertiary: $uni-text-color-grey; // 辅助文字颜色
$link-color: $theme-color-link; // 链接/高亮文字颜色
$border-color: $uni-border-color; // 边框颜色
$shadow-dark: $theme-shadow-dark;
$shadow-light: $theme-shadow-light;

page {
	background-color: $bg-color;
}

// 新拟物风格的混入
@mixin neumorphism {
	background: $bg-color;
	box-shadow: 12px 12px 24px $shadow-dark,
				-8px -8px 20px $shadow-light,
				inset 2px 2px 4px rgba(255, 255, 255, 0.5),
				inset -2px -2px 4px rgba(0, 0, 0, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

@mixin neumorphism-inset {
	background: $bg-color;
	box-shadow: inset 6px 6px 12px $shadow-dark,
				inset -6px -6px 12px $shadow-light;
}

// 磨砂玻璃风格的混入
@mixin glassmorphism {
	background: rgba($bg-color, 0.98);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.neumorphism {
	@include neumorphism;
}

.neumorphism-inset {
	@include neumorphism-inset;
}

.glassmorphism {
	@include glassmorphism;
}

.image-compress-container {
	padding: 30rpx;

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;
		padding: 0 30rpx;

		.nav-content {
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.profile-button-container {
				min-width: 40px;
				display: flex;
				justify-content: flex-start;
				margin-left: 0;
				padding: 2px;

				.profile-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 32px;
					height: 32px;
					border-radius: 50%;
					background: rgba(255, 255, 255, 0.95);
					border: 0.5px solid rgba(0, 0, 0, 0.05);
					box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
					transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
					position: relative;
					flex-shrink: 0;

					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						border-radius: 50%;
						background: rgba(255, 255, 255, 0.8);
						opacity: 0;
						transition: opacity 0.2s ease;
					}

					&:active {
						transform: scale(0.96);

						&::before {
							opacity: 1;
						}
					}

					.profile-text {
						font-size: 13px;
						color: #333333;
						font-weight: 500;
						line-height: 1;
						position: relative;
						z-index: 1;
					}
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: $text-primary;
				font-weight: 500;
			}

			.share-button-container {
				min-width: 36px;
				display: flex;
				justify-content: flex-end;
				margin-right: 180rpx;
			}
		}
	}

	.main-content {
		position: relative;
		width: 100%;
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom));

		.upload-section {
			margin: 20rpx 0 40rpx;

			.upload-box {
				height: 400rpx;
				border-radius: 30rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				transition: all 0.3s ease;
				background: linear-gradient(145deg, #ffffff, #f0f0f0);
				box-shadow: 20px 20px 40px rgba(0, 0, 0, 0.15),
							-12px -12px 24px rgba(255, 255, 255, 0.95),
							inset 2px 2px 4px rgba(255, 255, 255, 0.9),
							inset -2px -2px 4px rgba(0, 0, 0, 0.05);
				border: 1px solid rgba(255, 255, 255, 0.8);

				&:active {
					transform: scale(0.98);
					background: linear-gradient(145deg, #f0f0f0, #ffffff);
					box-shadow: inset 10px 10px 20px rgba(0, 0, 0, 0.1),
								inset -10px -10px 20px rgba(255, 255, 255, 0.95);
				}

				.upload-icon {
					width: 120rpx;
					height: 120rpx;
					margin-bottom: 20rpx;
					opacity: 0.8;
					filter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.1));
				}

				.upload-text {
					color: $text-primary;
					font-size: 32rpx;
					margin-bottom: 10rpx;
					font-weight: 500;
					text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
				}

				.upload-desc {
					color: $text-secondary;
					font-size: 24rpx;
					text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);
				}
			}

			.preview-image {
				width: 100%;
				height: 400rpx;
				border-radius: 30rpx;
				box-shadow: 20px 20px 40px rgba(0, 0, 0, 0.15),
							-12px -12px 24px rgba(255, 255, 255, 0.95),
							inset 2px 2px 4px rgba(255, 255, 255, 0.9),
							inset -2px -2px 4px rgba(0, 0, 0, 0.05);
				border: 1px solid rgba(255, 255, 255, 0.8);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: inset 10px 10px 20px rgba(0, 0, 0, 0.1),
								inset -10px -10px 20px rgba(255, 255, 255, 0.95);
				}
			}
		}

		.image-info {
			margin: 20rpx 0;
			padding: 20rpx;
			border-radius: 20rpx;

			.info-row {
				display: flex;
				justify-content: space-between;
				margin-bottom: 10rpx;
				font-size: 24rpx;
				color: $text-secondary;

				&:last-child {
					margin-bottom: 0;
				}

				text {
					flex: 1;
					padding: 0 10rpx;
				}
			}
		}

		.compress-settings {
			padding: 40rpx;
			border-radius: 30rpx;

			.setting-item {
				margin-bottom: 40rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.setting-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;

					.setting-label {
						font-size: 28rpx;
						color: $text-primary;
						font-weight: 500;
					}

					.size-controls {
						display: flex;
						align-items: center;
						gap: 10rpx;

						.size-display {
							padding: 4rpx 16rpx;
							border-radius: 12rpx;
							font-size: 28rpx;
							color: $primary-color;
							font-weight: 500;
							display: flex;
							align-items: center;
							justify-content: center;
							min-width: 120rpx;

							&:active {
								opacity: 0.8;
							}
						}

						.setting-value {
							min-width: 60rpx;
							text-align: center;
							font-size: 28rpx;
							color: $primary-color;
							font-weight: 500;
							padding: 4rpx 16rpx;
							border-radius: 12rpx;
						}
					}
				}

				.slider-container {
					padding: 30rpx 20rpx;
					border-radius: 20rpx;

					::v-deep .uni-slider {
						margin: 0;
					}

					::v-deep .uni-slider-handle {
						width: 56rpx;
						height: 56rpx;
						border-radius: 50%;
						box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
					}

					::v-deep .uni-slider-track {
						height: 4rpx;
					}
				}
			}
		}

		.bottom-buttons {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			display: flex;
			justify-content: space-between;
			padding: 20rpx 40rpx calc(20rpx + env(safe-area-inset-bottom));

			.btn {
				width: 45%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				border-radius: 44rpx;
				font-size: 32rpx;
				font-weight: 500;
				transition: all 0.3s ease;
				border: none;

				&.select-btn {
					color: $primary-color;
					border: 2rpx solid rgba(255, 128, 102, 0.3);
					background: $bg-color;
					box-shadow: 8px 8px 16px $shadow-dark,
								-6px -6px 12px $shadow-light,
								inset 1px 1px 2px rgba(255, 255, 255, 0.5);

					&:active {
						transform: scale(0.98);
						box-shadow: inset 6px 6px 12px $shadow-dark,
									inset -6px -6px 12px $shadow-light;
					}
				}

				&.compress-btn {
					background: $primary-gradient;
					color: #fff;
					box-shadow: 8px 8px 16px rgba(255, 128, 102, 0.2),
								-4px -4px 12px rgba(255, 255, 255, 0.8),
								inset 1px 1px 2px rgba(255, 255, 255, 0.3);

					&:active {
						transform: scale(0.98);
						box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.2);
					}
				}

				&[disabled] {
					opacity: 0.5;
					background: #E5E5E5;
					color: #999999;
				}
			}
		}
	}
}

.size-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;

	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.4);
		backdrop-filter: blur(4px);
	}

	.popup-content {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 80%;
		max-width: 600rpx;
		background: $bg-color;
		border-radius: 30rpx;
		overflow: hidden;

		.popup-header {
			padding: 20rpx;
			text-align: center;
			position: relative;
			border-bottom: 1px solid rgba(0, 0, 0, 0.05);

			.popup-title {
				font-size: 32rpx;
				font-weight: 500;
				color: $text-primary;
			}

			.popup-close {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transform: translateY(-50%);
				font-size: 40rpx;
				color: $text-secondary;
				padding: 10rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&:active {
					opacity: 0.7;
				}
			}
		}

		.popup-body {
			padding: 30rpx;

			.input-group {
				margin-bottom: 20rpx;

				text {
					display: block;
					font-size: 28rpx;
					color: $text-secondary;
					margin-bottom: 8rpx;
				}

				input {
					width: 100%;
					height: 80rpx;
					background: #fff;
					border-radius: 16rpx;
					padding: 0 24rpx;
					font-size: 32rpx;
					color: $text-primary;
					box-sizing: border-box;
					box-shadow: inset 2rpx 2rpx 5rpx rgba(0, 0, 0, 0.05);
					transition: all 0.3s ease;

					&:focus {
						box-shadow: inset 2rpx 2rpx 5rpx rgba(255, 128, 102, 0.1);
					}
				}
			}

			.popup-buttons {
				display: flex;
				gap: 20rpx;
				margin-top: 30rpx;

				button {
					flex: 1;
					height: 80rpx;
					border-radius: 40rpx;
					font-size: 30rpx;
					border: none;
					transition: all 0.3s ease;

					&.cancel-btn {
						background: rgba(0, 0, 0, 0.03);
						color: $text-secondary;

						&:active {
							opacity: 0.8;
						}
					}

					&.confirm-btn {
						background: $primary-gradient;
						color: #fff;

						&:active {
							opacity: 0.9;
						}
					}
				}
			}
		}
	}
}

.share-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;

	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.4);
		backdrop-filter: blur(4px);
	}

	.popup-content {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 80%;
		max-width: 600rpx;
		background: $bg-color;
		border-radius: 30rpx;
		overflow: hidden;

		.popup-header {
			padding: 20rpx;
			text-align: center;
			position: relative;
			border-bottom: 1px solid rgba(0, 0, 0, 0.05);

			.popup-title {
				font-size: 32rpx;
				font-weight: 500;
				color: $text-primary;
			}

			.popup-close {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transform: translateY(-50%);
				font-size: 40rpx;
				color: $text-secondary;
				padding: 10rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&:active {
					opacity: 0.7;
				}
			}
		}

		.popup-body {
			padding: 30rpx;

			.share-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-bottom: 30rpx;

				.share-icon {
					width: 120rpx;
					height: 120rpx;
					margin-bottom: 20rpx;
				}

				.share-title {
					font-size: 32rpx;
					font-weight: 500;
					color: $text-primary;
					margin-bottom: 10rpx;
				}

				.share-desc {
					font-size: 24rpx;
					color: $text-secondary;
				}
			}

			.share-buttons {
				display: flex;
				gap: 20rpx;

				.share-btn {
					flex: 1;
					height: 80rpx;
					border-radius: 40rpx;
					font-size: 30rpx;
					background: $primary-gradient;
					color: #fff;
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 10rpx;
					border: none;
					transition: all 0.3s ease;

					&:active {
						opacity: 0.9;
					}

					.iconfont {
						font-size: 36rpx;
					}
				}

				.cancel-btn {
					flex: 1;
					height: 80rpx;
					border-radius: 40rpx;
					font-size: 30rpx;
					background: rgba(0, 0, 0, 0.03);
					color: $text-secondary;
					border: none;
					transition: all 0.3s ease;

					&:active {
						opacity: 0.8;
					}
				}
			}
		}
	}
}
</style>
