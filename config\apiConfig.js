
// --- 环境配置 ---
// 微信小程序的发布环境有 develop, trial, release
// 我们将其映射到两个配置环境：dev 和 prod
const envMap = {
  'develop': 'dev',  // 开发版
  'trial':   'trial',  // 体验版
  'release': 'prod'  // 正式版
};

// 获取当前运行环境
const accountInfo = wx.getAccountInfoSync();
const currentEnv = envMap[accountInfo.miniProgram.envVersion] || 'prod';


// --- 通用配置 (所有环境共享) ---
const commonConfig = {
  // 全局超时配置
  timeout: {
    previewImage: 10000, // 简历预览图片生成超时时间（毫秒）
    generatePDF: 10000,  // PDF生成超时时间（毫秒）
    default: 5000        // 普通API请求超时时间（毫秒）
  },

  // API 路径 (这些路径在不同环境下通常是固定的)
  generatePDFUrl:                 '/resumerender/export-pdf',
  exportJpegUrl:                  '/resumerender/export-jpeg',
  loginUrl:                       '/auth/login',
  userInfoUrl:                    '/auth/user',
  updateUserInfoUrl:              '/auth/user',
  feedbackUrl:                    '/feedback/submit',
  errorReportUrl:                 '/error/report',
  freeTemplatesUrl:               '/free-templates',
  resumeStylesUrl:                '/free-templates/styles',
  resumeStyleDetailUrl:           '/free-templates/styles',
  idPhotoHealthUrl:               '/idphoto/health',
  idPhotoGenerateUrl:             '/idphoto/generate',
  idPhotoGenerateTransparentUrl:  '/idphoto/generate_transparent',
  idPhotoGenerateAddColorUrl:     '/idphoto/generate_add_color',
  idPhotoSizesUrl:                '/idphoto/sizes',
  idPhotoColorsUrl:               '/idphoto/colors',
  
  wxAppName:                      '个人简历制作花花版'
};


// --- 环境特定配置 ---
const environmentConfig = {
  // 开发环境配置
  dev: {
    // baseUrl:                'https://resume.gbw8848.cn', // 开发和测试服务器
    // idPhotoBaseUrl:         'https://resume.gbw8848.cn'
    baseUrl:                'http://192.168.1.218:18080', // 开发和测试服务器
    idPhotoBaseUrl:         'http://192.168.1.218:18080'
  },
  
  // 体验环境配置
  trial: {
    baseUrl:                'https://resume.gbw8848.cn', 
    idPhotoBaseUrl:         'https://resume.gbw8848.cn'
  },

  // 生产环境配置
  prod: {
    baseUrl:                'https://resume.gbw8848.cn', // 【请替换为你的生产环境API地址】
    idPhotoBaseUrl:         'https://resume.gbw8848.cn' // 【请替换为你的生产环境证件照API地址】
  }
};


// --- 合并并导出配置 ---
// 使用当前环境的配置覆盖通用配置
const finalConfig = {
  ...commonConfig,
  ...environmentConfig[currentEnv]
};

console.log(`当前环境: ${currentEnv}`);
// console.log('最终API配置:', finalConfig);

module.exports = finalConfig;