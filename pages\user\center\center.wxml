<!-- pages/user/center/center.wxml -->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/pages/index/images/touXiang.png'}}" mode="aspectFill"></image>
      <view class="user-detail">
        <text class="nickname">{{userInfo.nickName || '微信用户'}}</text>
        <view class="membership-status">
          <text class="status-text {{isMember ? 'member' : 'normal'}}">{{membershipStatus}}</text>
          <button wx:if="{{!isMember}}" class="upgrade-btn" bindtap="upgradeMembership">升级会员</button>
          <button wx:if="{{isMember}}" class="refresh-btn" bindtap="refreshMemberStatus">刷新状态</button>
        </view>
        <!-- 会员详细信息 -->
        <view wx:if="{{membershipInfo}}" class="membership-detail">
          <text class="detail-text">OpenID: {{membershipInfo.openid || '未获取'}}</text>
          <text class="detail-text">最后更新: {{membershipInfo.lastUpdated ? membershipInfo.lastUpdated : '未知'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="goToMyResumes">
      <view class="menu-icon resume-icon"></view>
      <text class="menu-text">我的简历</text>
      <view class="menu-arrow"></view>
    </view>
    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-icon settings-icon"></view>
      <text class="menu-text">账号设置</text>
      <view class="menu-arrow"></view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <button class="logout-btn" bindtap="logout">退出登录</button>
</view>
