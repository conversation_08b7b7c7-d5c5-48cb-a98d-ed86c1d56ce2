<!-- pages/user/center/center.wxml -->
<view class="profile-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav glassmorphism">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="nav-content">
      <view class="back-button-container">
        <button class="back-btn" bindtap="goBack">
          <text class="back-icon">‹</text>
        </button>
      </view>
      <text class="nav-title">我的</text>
      <view class="nav-right-buttons">
        <!-- 移除自定义按钮，使用微信官方按钮 -->
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" style="padding-top: {{navHeight}}px;">
    <!-- 用户信息和会员卡片 -->
    <view class="user-info-card neumorphism">
      <!-- 用户基本信息 -->
      <view class="user-header">
        <view class="user-avatar">
          <image src="./images/user-avatar.svg" mode="aspectFit" class="avatar-image"></image>
        </view>
        <view class="user-details">
          <view class="user-name">微信用户</view>
          <view class="member-badge member-{{memberInfo.type}}">{{memberTypeText}}</view>
        </view>
        <view class="member-icon">
          <image src="./images/crown.svg" mode="aspectFit" class="crown-image"></image>
        </view>
      </view>

      <!-- 会员特权信息 -->
      <view class="member-section">
        <view class="member-header">
          <text class="member-title">{{memberTypeText}}</text>
          <text class="member-subtitle">尊享会员特权</text>
        </view>
        <!-- 分隔线 -->
        <view class="divider-line"></view>
        <view class="privileges-grid">
          <view class="privilege-item">
            <view class="check-icon">
              <text class="check-mark">✓</text>
            </view>
            <text class="privilege-text">无限次图片压缩</text>
          </view>
          <view class="privilege-item">
            <view class="check-icon">
              <text class="check-mark">✓</text>
            </view>
            <text class="privilege-text">批量压缩处理</text>
          </view>
          <view class="privilege-item">
            <view class="check-icon">
              <text class="check-mark">✓</text>
            </view>
            <text class="privilege-text">高级压缩算法</text>
          </view>
          <view class="privilege-item">
            <view class="check-icon">
              <text class="check-mark">✓</text>
            </view>
            <text class="privilege-text">专属会员服务</text>
          </view>
        </view>
        <!-- 分隔线 -->
        <view class="divider-line"></view>
        <view class="member-validity">
          <text>会员有效期: {{memberExpireText}}</text>
        </view>
        <button class="renew-btn heartbeat-animation" bindtap="handleRenewMember">
          {{memberButtonText}}
        </button>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item neumorphism" bindtap="handleMenuClick" data-type="order">
        <view class="menu-icon">
          <image src="./images/order.svg" mode="aspectFit" class="icon-image"></image>
        </view>
        <text class="menu-text">订单查询</text>
      </view>
      <view class="menu-item neumorphism" bindtap="handleMenuClick" data-type="feedback">
        <view class="menu-icon">
          <image src="./images/feedback.svg" mode="aspectFit" class="icon-image"></image>
        </view>
        <text class="menu-text">联系我们</text>
      </view>
      <view class="menu-item neumorphism" bindtap="handleMenuClick" data-type="about">
        <view class="menu-icon">
          <image src="./images/about.svg" mode="aspectFit" class="icon-image"></image>
        </view>
        <text class="menu-text">关于我们</text>
      </view>
    </view>
  </view>
</view>
