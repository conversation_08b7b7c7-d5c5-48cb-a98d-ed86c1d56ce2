/**
 * 内容安全检测结果存储
 * 用于在前端存储和管理内容安全检测结果
 *
 * 简化版：只存储当前处理的图片的检测结果，不保存历史记录
 */

// 检测结果存储 - 使用对象而不是Map，只存储当前图片的检测结果
// 格式: { filePath: { traceId, status, timestamp } }
const currentResults = {};

/**
 * 记录检测请求
 * @param {string} filePath 文件路径
 * @param {string} traceId 检测请求ID
 */
export function recordCheckRequest(filePath, traceId) {
  currentResults[filePath] = {
    traceId: traceId,
    status: 'checking', // checking, pass, risky, review
    timestamp: Date.now()
  };
  console.log(`[安全检测] 记录检测请求: ${filePath} -> ${traceId}`);
}

/**
 * 更新检测结果
 * @param {string} traceId 检测请求ID
 * @param {string} status 检测状态 (pass, risky, review)
 * @param {number} label 标签ID
 */
export function updateCheckResult(traceId, status, label) {
  console.log(`[安全检测] 收到检测结果更新: trace_id=${traceId}, status=${status}, label=${label}`);

  // 查找对应的文件路径
  for (const filePath in currentResults) {
    if (currentResults[filePath].traceId === traceId) {
      currentResults[filePath].status = status;
      currentResults[filePath].label = label;
      currentResults[filePath].updateTime = Date.now();
      console.log(`[安全检测] 成功更新检测结果: ${filePath} -> ${status}, 标签: ${label}`);
      return true;
    }
  }

  console.warn(`[安全检测] 未找到对应的检测请求: ${traceId}`);
  return false;
}

/**
 * 获取文件的检测状态
 * @param {string} filePath 文件路径
 * @returns {object|null} 检测结果对象或null
 */
export function getCheckResult(filePath) {
  const result = currentResults[filePath] || null;

  if (result) {
    console.log(`[安全检测] 找到图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);
  } else {
    console.log(`[安全检测] 未找到图片 ${filePath} 的检测结果`);
  }

  return result;
}

/**
 * 检查图片是否违规
 * @param {string} filePath 图片路径
 * @param {boolean} checkLatest 是否查询最新结果
 * @returns {Promise<object>} 检测结果对象 {isRisky, isChecking, message}
 */
export async function isImageRisky(filePath, checkLatest = false) {
  console.log(`[安全检测] 正在查询图片 ${filePath} 的检测结果`);

  // 获取本地存储的检测结果
  let result = getCheckResult(filePath);

  // 如果需要查询最新结果
  if (checkLatest && result && result.traceId) {
    try {
      console.log(`[安全检测] 查询最新检测结果: ${result.traceId}`);

      // 发起查询请求
      const res = await uni.request({
        url: 'https://media-check.gbw8848.cn/api/check-result',
        method: 'POST',
        data: { trace_id: result.traceId }
      });

      // 处理响应
      if (res.statusCode === 200 && res.data.code === 0) {
        const { status, label } = res.data.data;

        // 如果检测完成，更新本地状态
        if (status !== 'checking') {
          updateCheckResult(result.traceId, status, label);

          // 重新获取更新后的结果
          result = getCheckResult(filePath);
        }
      }
    } catch (error) {
      console.error('[安全检测] 查询最新结果失败:', error);
      // 出错时继续使用本地结果
    }
  }

  // 未找到检测结果
  if (!result) {
    console.log(`[安全检测] 未找到图片 ${filePath} 的检测结果，可能尚未完成检测`);
    return {
      isRisky: false,
      isChecking: false,
      message: '未找到检测结果'
    };
  }

  console.log(`[安全检测] 图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);

  // 检测中
  if (result.status === 'checking') {
    console.log(`[安全检测] 图片 ${filePath} 正在检测中，请稍候`);
    return {
      isRisky: false,
      isChecking: true,
      message: '内容安全检测尚未完成，请稍等片刻'
    };
  }

  // 违规内容
  if (result.status === 'risky') {
    console.log(`[安全检测] 图片 ${filePath} 被检测为违规内容，将拦截压缩`);
    return {
      isRisky: true,
      isChecking: false,
      message: '当前图片无法处理，请尝试其他图片'
    };
  }

  // 需要人工审核的内容
  if (result.status === 'review') {
    console.log(`[安全检测] 图片 ${filePath} 需要人工审核，暂时拦截压缩`);
    return {
      isRisky: true,
      isChecking: false,
      message: '当前图片需要人工审核，暂时无法处理，请尝试其他图片'
    };
  }

  // 检测通过
  console.log(`[安全检测] 图片 ${filePath} 检测通过，允许压缩`);
  return {
    isRisky: false,
    isChecking: false,
    message: '检测通过'
  };
}

export default {
  recordCheckRequest,
  updateCheckResult,
  getCheckResult,
  isImageRisky
};
