/**
 * 模板相关API接口
 */
const request = require('./request');
const apiConfig = require('../../config/apiConfig');

/**
 * 获取模板列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.pageSize 每页数量
 * @param {string} params.type 模板类型 (word/online)
 * @param {string} params.category 模板分类 (可选)
 * @param {string} params.keyword 搜索关键词 (可选)
 * @returns {Promise<Object>} 模板列表响应
 */
function getTemplateList(params = {}) {
  const queryParams = {
    page: params.page || 1,
    page_size: params.pageSize || 10,
    type: params.type || 'word',
    category: params.category || '',
    keyword: params.keyword || ''
  };

  // 构建查询字符串
  const queryString = Object.keys(queryParams)
    .filter(key => queryParams[key] !== '')
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  console.log('=== 获取模板列表API请求 ===');
  console.log('查询参数:', queryParams);

  return request.request({
    url: `/templates?${queryString}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 模板列表不需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取模板详情
 * @param {string} templateId 模板ID
 * @returns {Promise<Object>} 模板详情响应
 */
function getTemplateDetail(templateId) {
  if (!templateId) {
    return Promise.reject(new Error('模板ID不能为空'));
  }

  console.log('=== 获取模板详情API请求 ===');
  console.log('模板ID:', templateId);

  return request.request({
    url: `/templates/${templateId}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 记录模板下载行为
 * @param {string} templateId 模板ID
 * @returns {Promise<Object>} 记录响应
 */
function recordDownload(templateId) {
  if (!templateId) {
    return Promise.reject(new Error('模板ID不能为空'));
  }

  console.log('=== 记录模板下载API请求 ===');
  console.log('模板ID:', templateId);

  return request.request({
    url: `/templates/${templateId}/download`,
    method: 'POST',
    data: {
      timestamp: Date.now(),
      source: 'miniprogram'
    },
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 下载记录不需要认证，但会记录用户信息（如果有的话）
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取模板分类列表
 * @returns {Promise<Object>} 分类列表响应
 */
function getTemplateCategories() {
  console.log('=== 获取模板分类API请求 ===');

  return request.request({
    url: '/templates/categories',
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 搜索模板
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 搜索关键词
 * @param {string} params.type 模板类型
 * @param {string} params.category 模板分类
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @returns {Promise<Object>} 搜索结果响应
 */
function searchTemplates(params = {}) {
  const queryParams = {
    keyword: params.keyword || '',
    type: params.type || 'word',
    category: params.category || '',
    page: params.page || 1,
    page_size: params.pageSize || 10
  };

  const queryString = Object.keys(queryParams)
    .filter(key => queryParams[key] !== '')
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  console.log('=== 搜索模板API请求 ===');
  console.log('搜索参数:', queryParams);

  return request.request({
    url: `/templates/search?${queryString}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取热门模板
 * @param {number} limit 限制数量，默认10
 * @returns {Promise<Object>} 热门模板响应
 */
function getPopularTemplates(limit = 10) {
  console.log('=== 获取热门模板API请求 ===');
  console.log('限制数量:', limit);

  return request.request({
    url: `/templates/popular?limit=${limit}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

module.exports = {
  getTemplateList,
  getTemplateDetail,
  recordDownload,
  getTemplateCategories,
  searchTemplates,
  getPopularTemplates
};
