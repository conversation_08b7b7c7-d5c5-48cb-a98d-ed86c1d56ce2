#!/usr/bin/env node

/**
 * 商户配置验证工具
 * 验证所有商户配置的完整性和正确性
 */

// 加载环境变量
require('dotenv').config();
const MerchantManager = require('../services/MerchantManager');

function validateAllMerchants() {
  console.log('🔍 图像压缩服务器 - 商户配置验证');
  console.log('================================\n');

  const allMerchants = MerchantManager.getAllMerchants();
  
  if (allMerchants.length === 0) {
    console.log('⚠️  没有找到任何商户配置');
    return;
  }

  let validCount = 0;
  let errorCount = 0;
  let warningCount = 0;

  allMerchants.forEach((config, index) => {
    console.log(`${index + 1}. 验证商户: ${config.businessName} (${config.merchantId})`);
    
    const validation = MerchantManager.validateMerchant(config.merchantId);
    
    if (validation.valid) {
      console.log('   ✅ 配置有效');
      validCount++;
    } else {
      console.log('   ❌ 配置无效');
      errorCount++;
      validation.errors.forEach(error => {
        console.log(`      - ${error}`);
      });
    }

    if (validation.warnings && validation.warnings.length > 0) {
      warningCount++;
      validation.warnings.forEach(warning => {
        console.log(`   ⚠️  ${warning}`);
      });
    }

    console.log('');
  });

  // 显示汇总
  console.log('📊 验证汇总:');
  console.log(`   ✅ 有效配置: ${validCount}`);
  console.log(`   ❌ 无效配置: ${errorCount}`);
  console.log(`   ⚠️  警告数量: ${warningCount}`);
  console.log('');

  // 检查AppID重复
  console.log('🔍 检查AppID重复:');
  const appIdMap = new Map();
  let duplicateFound = false;

  allMerchants.forEach(config => {
    if (config.appId && config.appId !== 'wx_default_app_id') {
      if (appIdMap.has(config.appId)) {
        console.log(`   ❌ 重复AppID: ${config.appId}`);
        console.log(`      商户1: ${appIdMap.get(config.appId)}`);
        console.log(`      商户2: ${config.merchantId}`);
        duplicateFound = true;
      } else {
        appIdMap.set(config.appId, config.merchantId);
      }
    }
  });

  if (!duplicateFound) {
    console.log('   ✅ 没有发现重复的AppID');
  }

  console.log('');

  // 检查商品配置
  console.log('🛍️  检查商品配置:');
  allMerchants.forEach(config => {
    if (!config.products || Object.keys(config.products).length === 0) {
      console.log(`   ❌ ${config.merchantId}: 缺少商品配置`);
    } else {
      const products = Object.keys(config.products);
      if (!products.includes('dayCard') || !products.includes('permanent')) {
        console.log(`   ⚠️  ${config.merchantId}: 商品配置不完整 (${products.join(', ')})`);
      } else {
        console.log(`   ✅ ${config.merchantId}: 商品配置完整`);
      }
    }
  });

  console.log('');

  if (errorCount > 0) {
    console.log('❌ 发现配置错误，请修复后重新验证');
    process.exit(1);
  } else if (warningCount > 0) {
    console.log('⚠️  配置基本正确，但有警告项需要注意');
  } else {
    console.log('✅ 所有商户配置验证通过！');
  }
}

function validateSingleMerchant(merchantId) {
  console.log(`🔍 验证商户: ${merchantId}`);
  console.log('========================\n');

  const config = MerchantManager.getMerchantById(merchantId);
  if (!config) {
    console.log(`❌ 商户 ${merchantId} 不存在或未激活`);
    return;
  }

  console.log(`商户名称: ${config.businessName}`);
  console.log(`AppID: ${config.appId}`);
  console.log(`状态: ${config.isActive ? '激活' : '停用'}`);
  console.log('');

  const validation = MerchantManager.validateMerchant(merchantId);
  
  if (validation.valid) {
    console.log('✅ 配置验证通过');
  } else {
    console.log('❌ 配置验证失败:');
    validation.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }

  if (validation.warnings && validation.warnings.length > 0) {
    console.log('⚠️  警告:');
    validation.warnings.forEach(warning => {
      console.log(`   - ${warning}`);
    });
  }

  // 显示商品配置
  console.log('\n🛍️  商品配置:');
  if (config.products) {
    Object.entries(config.products).forEach(([code, product]) => {
      console.log(`   ${code}: ${product.name} - ¥${product.price}`);
    });
  } else {
    console.log('   无商品配置');
  }
}

function main() {
  const merchantId = process.argv[2];
  
  try {
    if (merchantId) {
      validateSingleMerchant(merchantId);
    } else {
      validateAllMerchants();
    }
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { validateAllMerchants, validateSingleMerchant };
