Page({
  data: {
    awardsFormData: []
  },

  onLoad() {
    // 加载已保存的数据
    const savedData = wx.getStorageSync('resumeData') || {};
    if (savedData.awards) {
      this.setData({
        awardsFormData: savedData.awards
      });
    }
  },

  // 添加奖项
  addAward() {
    const awardsFormData = this.data.awardsFormData;
    awardsFormData.push({
      name: '',
      time: '',
      level: ''
    });
    this.setData({ awardsFormData });
    this.saveData();
  },

  // 删除奖项
  deleteAward(e) {
    const index = e.currentTarget.dataset.index;
    const awardsFormData = this.data.awardsFormData;
    awardsFormData.splice(index, 1);
    this.setData({ awardsFormData });
    this.saveData();
  },

  // 输入框内容变化时触发
  onInputChange(e) {
    const { field, index } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`awardsFormData[${index}].${field}`]: value
    });
    
    this.saveData();
  },

  // 保存数据到本地存储
  saveData() {
    const savedData = wx.getStorageSync('resumeData') || {};
    savedData.awards = this.data.awardsFormData;
    wx.setStorageSync('resumeData', savedData);
  },

  // 页面隐藏时保存数据
  onHide() {
    this.saveData();
  },

  // 页面卸载时保存数据
  onUnload() {
    this.saveData();
  }
}); 