const { exec } = require('child_process');

console.log('开始构建并压缩JS文件...');

// 设置环境变量为生产环境
process.env.NODE_ENV = 'production';

// 执行uni-app的构建命令（如果使用HBuilderX，这里的命令可能需要调整）
const buildCommand = 'npx vue-cli-service build';

exec(buildCommand, (error, stdout, stderr) => {
  if (error) {
    console.error(`构建失败: ${error}`);
    return;
  }
  
  console.log(`构建输出: ${stdout}`);
  
  if (stderr) {
    console.error(`构建警告/错误: ${stderr}`);
  }
  
  console.log('构建完成，JS文件已压缩');
}); 