# 微信内容安全检测API

这是一个用于处理微信小程序内容安全检测的Node.js API服务，帮助小程序开发者满足微信平台的内容安全要求。特别适用于图片压缩类小程序，可以检测用户上传的图片是否包含违规内容。

## 功能

- 接收微信服务器的验证请求
- 提供内容安全检测API接口给小程序调用
- 接收微信内容安全检测的回调结果
- 支持多个微信小程序同时使用
- 自动管理access_token
- 支持图片、音频、视频等多种媒体类型的内容检测
- 支持获取用户OpenID进行内容安全检测
- 在压缩操作前拦截违规内容

## 安装

```bash
# 安装依赖
npm install
# 或使用pnpm
pnpm install
```

## 配置

在`config/index.js`中修改以下配置：

```javascript
module.exports = {
  // 微信配置
  wechat: {
    token: 'your_token', // 微信服务器验证Token
    // 支持多个小程序配置
    apps: [
      {
        appId: 'your_appid_1', // 第一个微信小程序AppID
        appSecret: 'your_appsecret_1', // 第一个微信小程序AppSecret
        name: '小程序1' // 小程序名称（便于日志识别）
      },
      {
        appId: 'your_appid_2', // 第二个微信小程序AppID
        appSecret: 'your_appsecret_2', // 第二个微信小程序AppSecret
        name: '小程序2' // 小程序名称（便于日志识别）
      }
    ],
    // 保留默认配置，用于向后兼容和微信服务器验证
    appId: 'your_appid_1',
    appSecret: 'your_appsecret_1'
  },

  // 服务器配置
  server: {
    port: process.env.PORT || 8848,
    host: process.env.HOST || 'localhost'
  }
};
```

## 运行

```bash
# 开发模式运行
npm run dev
# 或
pnpm run dev

# 生产模式运行
npm start
# 或
pnpm start
```

## API接口

### 1. 内容安全检测

**请求**:
```
POST /api/security-check
Content-Type: application/json

{
  "mediaUrl": "https://example.com/image.jpg",
  "mediaType": 2,
  "appId": "your_appid" // 可选，指定使用哪个小程序的配置
}
```

**参数说明**:
- mediaUrl: 媒体文件的URL
- mediaType: 媒体类型（1:音频;2:图片;3:视频）
- appId: (可选) 小程序AppID，用于指定使用哪个小程序的配置。如果不提供，将使用默认配置

**响应**:
```json
{
  "code": 0,
  "data": {
    "errcode": 0,
    "errmsg": "ok",
    "trace_id": "xxxxxx"
  },
  "msg": "success"
}
```

### 2. 上传图片并进行内容安全检测

**请求**:
```
POST /api/upload-image-check
Content-Type: multipart/form-data

image: [图片文件]
appId: your_appid (可选)
openid: user_openid (必填，用户的OpenID)
```

**参数说明**:
- image: 要上传的图片文件
- appId: (可选) 小程序AppID，用于指定使用哪个小程序的配置。如果不提供，将使用默认配置
- openid: (必填) 用户的OpenID，用于内容安全检测API

**响应**:
```json
{
  "code": 0,
  "data": {
    "checkResult": {
      "errcode": 0,
      "errmsg": "ok",
      "trace_id": "xxxxxx"
    },
    "fileUrl": "http://your-server.com/uploads/1234567890.jpg",
    "fileName": "1234567890.jpg",
    "originalName": "example.jpg",
    "size": 12345
  },
  "msg": "success"
}
```

**注意**: 上传的文件将在24小时后自动删除

### 3. 获取用户OpenID

**请求**:
```
POST /api/get-openid
Content-Type: application/json

{
  "code": "wx_login_code",
  "appId": "your_appid" (可选)
}
```

**参数说明**:
- code: 微信登录时获取的code
- appId: (可选) 小程序AppID，用于指定使用哪个小程序的配置。如果不提供，将使用默认配置

**响应**:
```json
{
  "code": 0,
  "data": {
    "openid": "user_openid",
    "session_key": "session_key"
  },
  "msg": "success"
}
```

## 回调接口

微信服务器会将内容检测结果发送到以下接口：

```
POST /wechat/security-check-callback
```

## 多小程序支持

本API服务支持多个微信小程序同时使用内容安全检测功能。使用方法：

1. 在`config/index.js`中配置多个小程序的AppID和AppSecret
2. 在API请求中通过`appId`参数指定使用哪个小程序的配置
3. 如果不指定`appId`，将使用默认配置（`config.wechat.appId`和`config.wechat.appSecret`）

### 测试多小程序

可以使用以下命令测试所有配置的小程序：

```bash
node test-multi-app.js
```

## 微信官方整改建议

> 请在所有涉及用户自定义输入/上传/发布内容的相关业务板块接入平台内容安全接口，请参考接入文档：
> https://developers.weixin.qq.com/miniprogram/dev/framework/operation.html#%E5%86%85%E5%AE%B9%E5%AE%89%E5%85%A8%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88
>
> 建议开发者接入内容安全接口+增加人工审核，减少依赖安全接口，并安排人员不定期检测是否存在过滤不完整的问题，（需要屏蔽过滤色情，敏感，涉政等违规文本以及图片）
>
> 请开发者参考上述建议进行整改并提交代码审核，代码审核通过后发布新版本小程序

本API服务正是为了满足以上整改要求而开发的，它提供了一个完整的内容安全检测解决方案，帮助小程序开发者快速接入微信内容安全接口。

## 实施建议

为了全面满足微信官方的整改要求，建议采取以下措施：

1. **接入内容安全接口**：使用本API服务接入微信内容安全接口，对所有用户上传的内容进行检测

2. **增加人工审核**：
   - 设置内容审核队列，对自动检测结果为"疑似违规"的内容进行人工复核
   - 安排专人定期抽查已发布内容，确保没有违规内容漏检

3. **完善内容管理机制**：
   - 建立内容举报功能，允许用户举报违规内容
   - 设置敏感词过滤，对明显违规的内容直接拦截
   - 对重复违规的用户实施限制措施

4. **定期检查与优化**：
   - 定期检查内容过滤效果，及时调整过滤策略
   - 关注微信平台规则更新，及时调整内容审核标准

## 在图片压缩小程序中集成内容安全检测

本服务专为图片压缩类小程序设计，以下是在图片压缩小程序中集成内容安全检测的详细步骤：

### 1. 服务端配置

1. **复制整个 `media-check` 目录**到您的服务器
2. **修改配置文件**：
   ```javascript
   // config/index.js
   module.exports = {
     wechat: {
       token: 'mediaCheckToken', // 微信服务器验证Token
       apps: [
         {
           appId: 'wx76b4aebe290bf455', // 替换为您的小程序AppID
           appSecret: 'b91071d1fbe511489df25e3ed8940f10', // 替换为您的小程序AppSecret
           name: '压缩图片助手花花版' // 替换为您的小程序名称
         }
       ],
       appId: 'wx76b4aebe290bf455', // 替换为您的小程序AppID
       appSecret: 'b91071d1fbe511489df25e3ed8940f10' // 替换为您的小程序AppSecret
     },
     server: {
       port: process.env.PORT || 8848,
       host: process.env.HOST || 'localhost'
     }
   };
   ```

3. **安装依赖并启动服务**：
   ```bash
   cd media-check
   npm install
   # 使用PM2启动服务
   pm2 start app.js --name media-check
   ```

4. **配置域名**：
   - 确保您有一个可访问的域名，如 `media-check.gbw8848.cn`
   - 将该域名解析到您的服务器IP
   - 配置Nginx或其他Web服务器，将该域名的请求转发到 `localhost:8848`
   - 在微信小程序后台配置以下域名：
     - request合法域名：`https://media-check.gbw8848.cn`
     - uploadFile合法域名：`https://media-check.gbw8848.cn`
     - downloadFile合法域名：`https://media-check.gbw8848.cn`

   **实际配置示例**：
   在微信小程序后台的"开发"->"开发设置"->"服务器域名"中配置：
   - request合法域名：`https://media-check.gbw8848.cn`
   - uploadFile合法域名：`https://media-check.gbw8848.cn`
   - downloadFile合法域名：`https://media-check.gbw8848.cn`
   - 其他域名（socket、udp、tcp、DNS预解析、预连接域名）可以不填

5. **配置微信回调URL**：
   - 登录微信公众平台
   - 进入"开发"->"开发设置"->"消息推送"
   - 设置服务器地址为 `https://media-check.yourdomain.com/wechat`
   - 设置Token为您在配置文件中设置的 `token` 值

### 2. 小程序端集成

1. **添加工具函数**：

   创建 `utils/auth.js` 文件：
   ```javascript
   /**
    * 获取用户OpenID
    * @returns {Promise<string>} 用户OpenID
    */
   export async function getOpenId() {
     try {
       // 尝试从缓存获取OpenID
       const cachedOpenId = uni.getStorageSync('openid');
       if (cachedOpenId) {
         console.log('使用缓存的OpenID:', cachedOpenId);
         return cachedOpenId;
       }

       // 获取登录凭证
       const loginResult = await new Promise((resolve, reject) => {
         uni.login({
           success: resolve,
           fail: reject
         });
       });

       // 使用登录凭证获取OpenID
       const response = await new Promise((resolve, reject) => {
         uni.request({
           url: 'https://media-check.gbw8848.cn/api/get-openid',
           method: 'POST',
           data: {
             code: loginResult.code
           },
           success: resolve,
           fail: reject
         });
       });

       if (response.statusCode === 200 && response.data.code === 0) {
         const openid = response.data.data.openid;
         // 缓存OpenID
         uni.setStorageSync('openid', openid);
         console.log('获取新的OpenID:', openid);
         return openid;
       } else {
         throw new Error('获取OpenID失败: ' + (response.data.msg || '未知错误'));
       }
     } catch (error) {
       console.error('获取OpenID失败:', error);
       throw error;
     }
   }
   ```

   创建 `utils/security.js` 文件：
   ```javascript
   /**
    * 内容安全检测工具函数
    */
   import { getOpenId } from './auth.js';

   /**
    * 上传图片并进行内容安全检测
    * @param {string} filePath 图片路径
    * @returns {Promise<object>} 检测结果
    */
   export async function checkImageSecurity(filePath) {
     try {
       // 获取用户OpenID
       const openid = await getOpenId();

       // 上传图片并进行内容安全检测
       return new Promise((resolve, reject) => {
         uni.uploadFile({
           url: 'https://media-check.gbw8848.cn/api/upload-image-check',
           filePath: filePath,
           name: 'image',
           formData: {
             openid: openid
           },
           success: (res) => {
             if (res.statusCode === 200) {
               const data = JSON.parse(res.data);
               if (data.code === 0) {
                 resolve(data.data);
               } else {
                 reject(new Error(data.msg || '内容检测失败'));
               }
             } else {
               reject(new Error(`请求失败，状态码: ${res.statusCode}`));
             }
           },
           fail: (err) => {
             reject(err);
           }
         });
       });
     } catch (error) {
       console.error('内容安全检测失败:', error);
       throw error;
     }
   }

   /**
    * 处理内容检测结果
    * @param {object} result 检测结果
    * @returns {boolean} 是否通过检测
    */
   export function handleCheckResult(result) {
     // 检测是异步的，这里只是记录trace_id
     console.log('内容检测请求已发送，trace_id:', result.checkResult.trace_id);

     // 由于微信内容安全检测是异步的，这里默认返回通过
     return true;
   }
   ```

   创建 `utils/securityStore.js` 文件：
   ```javascript
   /**
    * 内容安全检测结果存储
    * 用于在前端存储和管理内容安全检测结果
    */

   // 检测结果存储
   const securityResults = new Map();

   /**
    * 记录检测请求
    * @param {string} filePath 文件路径
    * @param {string} traceId 检测请求ID
    */
   export function recordCheckRequest(filePath, traceId) {
     securityResults.set(filePath, {
       traceId: traceId,
       status: 'checking', // checking, pass, risky
       timestamp: Date.now()
     });
     console.log(`记录检测请求: ${filePath} -> ${traceId}`);
   }

   /**
    * 更新检测结果
    * @param {string} traceId 检测请求ID
    * @param {string} status 检测状态 (pass, risky)
    * @param {number} label 标签ID
    */
   export function updateCheckResult(traceId, status, label) {
     // 查找对应的文件路径
     for (const [filePath, result] of securityResults.entries()) {
       if (result.traceId === traceId) {
         result.status = status;
         result.label = label;
         result.updateTime = Date.now();
         console.log(`更新检测结果: ${filePath} -> ${status}, 标签: ${label}`);
         return true;
       }
     }
     console.warn(`未找到对应的检测请求: ${traceId}`);
     return false;
   }

   /**
    * 获取文件的检测状态
    * @param {string} filePath 文件路径
    * @returns {object|null} 检测结果对象或null
    */
   export function getCheckResult(filePath) {
     return securityResults.get(filePath) || null;
   }

   /**
    * 检查图片是否违规
    * @param {string} filePath 图片路径
    * @returns {boolean} 是否违规
    */
   export function isImageRisky(filePath) {
     const result = getCheckResult(filePath);
     if (result && result.status === 'risky') {
       return true;
     }
     return false;
   }

   /**
    * 清理过期的检测结果
    * @param {number} maxAge 最大保留时间(毫秒)，默认1小时
    */
   export function cleanupResults(maxAge = 3600000) {
     const now = Date.now();
     for (const [filePath, result] of securityResults.entries()) {
       if (now - result.timestamp > maxAge) {
         securityResults.delete(filePath);
       }
     }
   }

   // 每小时清理一次过期结果
   setInterval(cleanupResults, 3600000);

   export default {
     recordCheckRequest,
     updateCheckResult,
     getCheckResult,
     isImageRisky,
     cleanupResults
   };
   ```

2. **修改小程序入口文件**：

   修改 `App.vue`：
   ```javascript
   <script>
     import { setupCallbackHandler } from './utils/security.js';

     export default {
       onLaunch: function() {
         console.log('App Launch');
         // 设置内容安全检测回调处理器
         setupCallbackHandler();
       },
       onShow: function() {
         console.log('App Show');
       },
       onHide: function() {
         console.log('App Hide');
       }
     }
   </script>
   ```

3. **修改图片选择和压缩功能**：

   在选择图片后添加内容安全检测：
   ```javascript
   // 选择图片后
   try {
     // 进行内容安全检测（静默进行，不显示提示）
     const checkResult = await checkImageSecurity(filePath);

     // 处理检测结果
     handleCheckResult(checkResult);

     // 无论检测结果如何，都继续处理图片
     // 只有在用户点击压缩按钮时才会检查结果并拦截
     this.tempImagePath = filePath;
     await this.getOriginalImageInfo(this.tempImagePath);
     this.customWidth = this.originalWidth.toString();
     this.customHeight = this.originalHeight.toString();
   } catch (error) {
     console.error('内容安全检测失败:', error);

     // 检测失败，仍然允许用户继续使用
     this.tempImagePath = filePath;
     await this.getOriginalImageInfo(this.tempImagePath);
     this.customWidth = this.originalWidth.toString();
     this.customHeight = this.originalHeight.toString();
   }
   ```

   在压缩按钮点击时检查图片是否违规：
   ```javascript
   // 压缩按钮点击事件
   async handleCompressClick() {
     try {
       // 检查图片是否违规
       if (this.tempImagePath && isImageRisky(this.tempImagePath)) {
         // 图片违规，拦截压缩
         uni.showModal({
           title: '提示',
           content: '当前图片无法处理，请尝试其他图片',
           showCancel: false
         });
         return;
       }

       // 继续正常的压缩流程...
     } catch (error) {
       // 错误处理...
     }
   }
   ```

## 注意事项

- 需要在每个微信小程序的管理后台配置回调URL
- 系统会自动管理每个小程序的access_token，无需手动刷新
- 请确保服务器有公网IP或域名，以便微信服务器能够访问回调接口
- 每个小程序的内容安全检测配额是独立计算的
- 内容安全检测应与人工审核相结合，不应完全依赖自动检测
- 建议定期检查内容过滤效果，确保没有违规内容漏检
- 需要屏蔽过滤色情、敏感、涉政等各类违规文本及图片
- 上传的文件将在24小时后自动删除，清理任务每6小时运行一次
- 用户必须在近两小时内访问过小程序，否则内容安全检测API会返回61010错误
- 单个AppID调用上限为2000次/分钟，200,000次/天
- 必须在微信小程序后台的"开发"->"开发设置"->"服务器域名"中配置正确的域名，包括request合法域名、uploadFile合法域名和downloadFile合法域名
- 域名必须是备案过的域名，且必须使用HTTPS协议

## 内容安全检测详细流程

为了帮助您更好地理解内容安全检测的工作流程，以下是详细的步骤说明：

### 1. 服务端配置

1. **配置微信公众平台**：
   - 登录微信公众平台
   - 进入"开发"->"开发设置"->"消息推送"
   - 设置服务器地址为 `https://media-check.yourdomain.com/wechat`
   - 设置Token为您在配置文件中设置的 `token` 值
   - 设置消息加密方式为"明文模式"
   - 设置数据格式为"XML"
   - 点击"提交"按钮

2. **服务器代码配置**：
   - 确保 `app.js` 中已添加 XML 解析支持：
     ```javascript
     const xmlparser = require('express-xml-bodyparser');
     app.use(xmlparser()); // 解析XML请求体，用于微信消息
     ```
   - 确保 `package.json` 中已添加 `express-xml-bodyparser` 依赖：
     ```json
     "dependencies": {
       "express-xml-bodyparser": "^0.3.0"
     }
     ```
   - 确保 `routes/wechat.js` 中已实现对 `wxa_media_check` 事件的处理：
     ```javascript
     router.post('/', (req, res) => {
       try {
         const xml = req.body.xml || {};
         const event = xml.event && xml.event[0];

         if (event === 'wxa_media_check') {
           const traceId = xml.trace_id && xml.trace_id[0];
           const result = xml.result && xml.result[0];
           const suggest = result && result.suggest && result.suggest[0];
           const label = result && result.label && result.label[0];

           console.log(`内容检测结果: trace_id=${traceId}, suggest=${suggest}, label=${label}`);

           if (suggest === 'risky') {
             console.log(`发现违规内容: trace_id=${traceId}, label=${label}`);
           }
         }

         res.send('success');
       } catch (error) {
         console.error('处理微信消息出错:', error);
         res.send('success');
       }
     });
     ```

### 2. 小程序端实现

1. **上传图片时调用内容安全检测**：
   ```javascript
   // 选择图片后
   const filePath = res.tempFilePaths[0];

   // 设置图片路径并获取信息
   this.tempImagePath = filePath;
   await this.getOriginalImageInfo(this.tempImagePath);

   // 在后台进行内容安全检测（完全静默，不影响用户体验）
   Promise.resolve().then(async () => {
     try {
       // 调用内容安全检测API
       const checkResult = await checkImageSecurity(filePath);

       // 处理检测结果
       handleCheckResult(checkResult);
       // 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果
     } catch (error) {
       // 只在控制台记录错误，不影响用户体验
       console.error('内容安全检测失败:', error);
     }
   });
   ```

2. **压缩前检查图片是否违规**：
   ```javascript
   // 压缩按钮点击事件
   async handleCompressClick() {
     try {
       // 检查图片是否违规
       if (this.tempImagePath && isImageRisky(this.tempImagePath)) {
         // 图片违规，拦截压缩
         uni.showModal({
           title: '提示',
           content: '当前图片无法处理，请尝试其他图片',
           showCancel: false
         });
         return;
       }

       // 继续正常的压缩流程...
     } catch (error) {
       // 错误处理...
     }
   }
   ```

### 3. 内容安全检测结果存储

1. **记录检测请求**：
   ```javascript
   export function recordCheckRequest(filePath, traceId) {
     securityResults.set(filePath, {
       traceId: traceId,
       status: 'checking', // checking, pass, risky
       timestamp: Date.now()
     });
     console.log(`[安全检测存储] 记录检测请求: ${filePath} -> ${traceId}`);
     console.log(`[安全检测存储] 当前存储的检测结果数量: ${securityResults.size}`);
   }
   ```

2. **更新检测结果**：
   ```javascript
   export function updateCheckResult(traceId, status, label) {
     console.log(`[安全检测存储] 收到检测结果更新: trace_id=${traceId}, status=${status}, label=${label}`);

     // 查找对应的文件路径
     for (const [filePath, result] of securityResults.entries()) {
       if (result.traceId === traceId) {
         result.status = status;
         result.label = label;
         result.updateTime = Date.now();
         console.log(`[安全检测存储] 成功更新检测结果: ${filePath} -> ${status}, 标签: ${label}`);
         return true;
       }
     }

     console.warn(`[安全检测存储] 未找到对应的检测请求: ${traceId}`);
     return false;
   }
   ```

3. **查询检测结果**：
   ```javascript
   export function getCheckResult(filePath) {
     const result = securityResults.get(filePath) || null;

     if (result) {
       console.log(`[安全检测存储] 找到图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);
     } else {
       console.log(`[安全检测存储] 未找到图片 ${filePath} 的检测结果`);
     }

     return result;
   }
   ```

4. **检查图片是否违规**：
   ```javascript
   export function isImageRisky(filePath) {
     console.log(`[安全检测] 正在查询图片 ${filePath} 的检测结果`);
     const result = getCheckResult(filePath);

     if (!result) {
       console.log(`[安全检测] 未找到图片 ${filePath} 的检测结果，可能尚未完成检测`);
       return false;
     }

     console.log(`[安全检测] 图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);

     if (result.status === 'risky') {
       console.log(`[安全检测] 图片 ${filePath} 被检测为违规内容，将拦截压缩`);
       return true;
     }

     console.log(`[安全检测] 图片 ${filePath} 检测通过，允许压缩`);
     return false;
   }
   ```

5. **清理过期结果**：
   ```javascript
   export function cleanupResults(maxAge = 3600000) { // 默认1小时
     const now = Date.now();
     let cleanedCount = 0;

     for (const [filePath, result] of securityResults.entries()) {
       if (now - result.timestamp > maxAge) {
         securityResults.delete(filePath);
         cleanedCount++;
       }
     }

     if (cleanedCount > 0) {
       console.log(`[安全检测存储] 清理了 ${cleanedCount} 条过期检测结果，当前剩余 ${securityResults.size} 条`);
     }
   }

   // 每小时清理一次过期结果
   setInterval(cleanupResults, 3600000);
   ```

### 4. 优化建议

1. **调整清理频率**：
   - 可以将检测结果的保留时间从1小时调整为24小时，与上传文件的清理周期保持一致
   - 将清理频率从每小时一次调整为每6小时一次，减少不必要的CPU消耗
   ```javascript
   export function cleanupResults(maxAge = 86400000) { // 24小时 = 86400000毫秒
     // 清理逻辑...
   }

   // 每6小时清理一次过期结果
   setInterval(cleanupResults, 21600000); // 6小时 = 21600000毫秒
   ```

2. **添加记录数量限制**：
   - 设置一个最大记录数量限制，避免内存占用过大
   ```javascript
   // 如果记录数量超过1000条，清理最旧的记录
   if (securityResults.size > 1000) {
     const entries = Array.from(securityResults.entries());
     // 按时间戳排序
     entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
     // 清理最旧的记录，直到剩下800条
     const toDelete = entries.slice(0, entries.length - 800);
     for (const [filePath] of toDelete) {
       securityResults.delete(filePath);
     }
     console.log(`[安全检测存储] 记录数量超过限制，清理了 ${toDelete.length} 条最旧记录，当前剩余 ${securityResults.size} 条`);
   }
   ```

3. **使用持久化存储**：
   - 对于生产环境，可以考虑使用数据库或文件系统来持久化存储检测结果
   - 这样即使服务器重启，也不会丢失检测结果

## 微信内容安全检测API说明

微信提供了两种内容安全检测API：

1. **文本内容安全检测**：`security.msgSecCheck`
   - 用于检测文本内容是否包含违规内容
   - 同步API，调用后立即返回检测结果

2. **媒体内容安全检测**：`security.mediaCheckAsync`
   - 用于检测图片、音频、视频等媒体内容是否包含违规内容
   - 异步API，检测结果通过消息推送的方式返回

### 媒体内容安全检测API参数说明

调用 `security.mediaCheckAsync` API 需要提供以下参数：

- `media_url`：要检测的媒体文件URL
- `media_type`：媒体类型（1:音频;2:图片;3:视频）
- `version`：接口版本号（2.0版本为固定值2）
- `scene`：场景枚举值（1:资料;2:评论;3:论坛;4:社交日志）
- `openid`：用户的openid（用户需在近两小时访问过小程序）

### 检测结果说明

检测结果通过消息推送的方式返回，包含以下字段：

- `trace_id`：检测请求ID，用于关联请求和结果
- `result.suggest`：检测结果建议，取值为：
  - `pass`：通过
  - `risky`：可能违规
  - `review`：需要人工审核
- `result.label`：违规标签，取值为：
  - `100`：正常
  - `10001`：广告
  - `20001`：时政
  - `20002`：色情
  - `20003`：辱骂
  - `20006`：违法犯罪
  - `20008`：欺诈
  - `20012`：低俗
  - `20013`：版权
  - `21000`：其他

### 检测结果处理

在收到检测结果后，应根据 `result.suggest` 的值进行相应处理：

- `pass`：允许内容通过
- `risky`：拦截内容，显示提示信息
- `review`：可以根据业务需求决定是否拦截，或者进入人工审核流程

## 常见问题与解决方案

1. **检测结果未返回**：
   - 确保微信公众平台已正确配置消息推送URL
   - 确保服务器能够接收微信的消息推送
   - 检查服务器日志，确认是否有接收到消息推送的记录

2. **检测API返回61010错误**：
   - 这表明用户未在近两小时内访问过小程序
   - 解决方案：确保用户已登录并使用小程序

3. **检测结果未正确关联到图片**：
   - 检查 `recordCheckRequest` 和 `updateCheckResult` 函数的实现
   - 确保 `trace_id` 正确关联到图片路径

4. **内存占用过大**：
   - 调整检测结果的保留时间
   - 设置最大记录数量限制
   - 考虑使用持久化存储

5. **服务器重启后检测结果丢失**：
   - 考虑使用数据库或文件系统来持久化存储检测结果
   - 或者在服务器重启后，要求用户重新上传图片
