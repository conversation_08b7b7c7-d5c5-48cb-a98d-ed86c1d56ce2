const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const { query, transaction } = require('../config/database');
const AppManager = require('../services/MerchantManager');
const moment = require('moment');

/**
 * 微信支付统一下单
 */
router.post('/create', async (req, res) => {
  try {
    console.log('📨 收到支付创建请求:', req.body);

    const { openid, appId, productCode } = req.body;

    if (!openid || !appId || !productCode) {
      console.error('❌ 参数验证失败:', { openid, appId, productCode });
      return res.status(400).json({
        success: false,
        error: 'openid、appId和productCode不能为空'
      });
    }

    console.log('🛒 开始创建支付订单:', { openid, appId, productCode });

    // 获取小程序配置
    const appConfig = AppManager.getAppByAppId(appId);
    console.log('🔧 小程序配置:', appConfig);

    if (!appConfig || !appConfig.isActive) {
      console.error('❌ 小程序配置问题:', { appConfig: !!appConfig, isActive: appConfig?.isActive });
      return res.status(404).json({
        success: false,
        error: '小程序配置不存在或未激活'
      });
    }

    // 获取商品信息
    const products = await AppManager.getProductsByAppId(appId);
    const product = products.find(p => p.productCode === productCode);
    if (!product) {
      return res.status(404).json({
        success: false,
        error: '商品不存在'
      });
    }

    // 获取或创建用户
    const result = await transaction(async (connection) => {
      // 查找用户
      let [users] = await connection.execute(
        'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
        [openid, appId]
      );

      let userId;
      if (users.length === 0) {
        // 创建新用户
        const [insertResult] = await connection.execute(
          'INSERT INTO users (openid, app_id, created_at) VALUES (?, ?, NOW())',
          [openid, appId]
        );
        userId = insertResult.insertId;
      } else {
        userId = users[0].user_id;
      }

      // 生成订单ID
      const orderId = `IC${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

      // 创建订单
      await connection.execute(
        `INSERT INTO orders (
          order_id, user_id, openid, app_id,
          product_code, product_name, amount, expire_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderId,
          userId,
          openid,
          appId,
          productCode,
          product.productName,
          product.price,
          moment().add(30, 'minutes').toDate() // 30分钟后过期
        ]
      );

      return {
        orderId,
        userId,
        productName: product.productName,
        amount: parseFloat(product.price),
        appConfig
      };
    });

    // 调用微信支付统一下单API
    const paymentParams = await createWechatPayOrder({
      orderId: result.orderId,
      amount: Math.round(result.amount * 100), // 转换为分
      description: result.productName,
      openid: openid,
      appConfig: result.appConfig
    });

    console.log('✅ 支付订单创建成功:', result.orderId);

    res.json({
      success: true,
      data: {
        orderId: result.orderId,
        paymentParams,
        amount: result.amount,
        productName: result.productName
      },
      message: '支付订单创建成功'
    });

  } catch (error) {
    console.error('❌ 创建支付订单失败:', error);
    console.error('❌ 错误堆栈:', error.stack);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 微信支付回调通知
 */
router.post('/notify', async (req, res) => {
  try {
    console.log('📨 收到微信支付回调通知');
    
    // 获取请求头中的签名信息
    const signature = req.headers['wechatpay-signature'];
    const timestamp = req.headers['wechatpay-timestamp'];
    const nonce = req.headers['wechatpay-nonce'];
    const serial = req.headers['wechatpay-serial'];

    if (!signature || !timestamp || !nonce || !serial) {
      console.error('❌ 缺少必要的签名头信息');
      return res.status(400).send('FAIL');
    }

    // 获取原始请求体
    const body = JSON.stringify(req.body);
    
    // 验证签名（这里需要根据具体的小程序配置来验证）
    const isValid = await verifyWechatPaySignature({
      signature,
      timestamp,
      nonce,
      body,
      serial
    });

    if (!isValid) {
      console.error('❌ 微信支付回调签名验证失败');
      return res.status(400).send('FAIL');
    }

    // 解密回调数据
    const decryptedData = await decryptWechatPayData(req.body);
    
    if (decryptedData.trade_state === 'SUCCESS') {
      // 支付成功，更新订单状态
      await handlePaymentSuccess({
        orderId: decryptedData.out_trade_no,
        transactionId: decryptedData.transaction_id,
        paymentMethod: 'wechat'
      });
    }

    console.log('✅ 微信支付回调处理完成');
    res.send('SUCCESS');

  } catch (error) {
    console.error('❌ 处理微信支付回调失败:', error);
    res.status(500).send('FAIL');
  }
});

/**
 * 查询支付状态
 */
router.get('/query/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    const [orders] = await query(
      'SELECT * FROM orders WHERE order_id = ?',
      [orderId]
    );

    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        error: '订单不存在'
      });
    }

    const order = orders[0];

    res.json({
      success: true,
      data: {
        orderId: order.order_id,
        paymentStatus: order.payment_status,
        amount: parseFloat(order.amount),
        productName: order.product_name,
        createdAt: order.created_at,
        paidAt: order.paid_at
      }
    });

  } catch (error) {
    console.error('❌ 查询支付状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 调用微信支付统一下单API
 */
async function createWechatPayOrder({ orderId, amount, description, openid, appConfig }) {
  try {
    const fs = require('fs');
    const { Wechatpay } = require('wechatpay-axios-plugin');

    // 读取私钥和公钥
    const privateKey = fs.readFileSync(appConfig.private_key_path, 'utf8');
    const publicKey = fs.readFileSync(appConfig.public_key_path, 'utf8');

    // 初始化微信支付客户端（使用公钥验签方式）
    const wxpay = new Wechatpay({
      mchid: appConfig.mchid,
      serial: appConfig.merchant_serial, // 商户证书序列号
      privateKey: privateKey,
      certs: {
        [appConfig.public_key_id]: publicKey // 平台公钥ID: 平台公钥
      }
    });

    // 统一下单参数
    const params = {
      appid: appConfig.appid,
      mchid: appConfig.mchid,
      description: description,
      out_trade_no: orderId,
      notify_url: appConfig.notifyUrl,
      amount: {
        total: amount,
        currency: 'CNY'
      },
      payer: {
        openid: openid
      }
    };

    console.log('📤 调用微信支付统一下单:', params);

    // 调用统一下单API
    const response = await wxpay.v3.pay.transactions.jsapi.post(params);

    console.log('📥 微信支付统一下单响应:', response.data);

    // 生成小程序支付参数
    const paymentParams = {
      timeStamp: Math.floor(Date.now() / 1000).toString(),
      nonceStr: Math.random().toString(36).substr(2, 15),
      package: `prepay_id=${response.data.prepay_id}`,
      signType: 'RSA'
    };

    // 手动生成小程序支付签名
    const crypto = require('crypto');
    const signString = `${appConfig.appid}\n${paymentParams.timeStamp}\n${paymentParams.nonceStr}\n${paymentParams.package}\n`;

    const sign = crypto.createSign('RSA-SHA256');
    sign.update(signString);
    sign.end();

    paymentParams.paySign = sign.sign(privateKey, 'base64');

    console.log('💳 生成小程序支付参数:', paymentParams);
    return paymentParams;

  } catch (error) {
    console.error('❌ 微信支付统一下单失败:', error);
    throw new Error(`微信支付统一下单失败: ${error.message}`);
  }
}

/**
 * 验证微信支付回调签名
 */
async function verifyWechatPaySignature({ signature, timestamp, nonce, body, serial }) {
  try {
    console.log('🔐 验证微信支付签名:', { signature, timestamp, nonce, serial });

    // 根据serial查找对应的小程序配置
    const appConfig = findAppConfigBySerial(serial);
    if (!appConfig) {
      console.error('❌ 未找到对应的小程序配置:', serial);
      return false;
    }

    const fs = require('fs');
    const { Wechatpay } = require('wechatpay-axios-plugin');

    // 读取私钥和公钥
    const privateKey = fs.readFileSync(appConfig.private_key_path, 'utf8');
    const publicKey = fs.readFileSync(appConfig.public_key_path, 'utf8');

    // 初始化微信支付客户端（使用公钥验签方式）
    const wxpay = new Wechatpay({
      mchid: appConfig.mchid,
      serial: appConfig.merchant_serial, // 商户证书序列号
      privateKey: privateKey,
      certs: {
        [appConfig.public_key_id]: publicKey // 平台公钥ID: 平台公钥
      }
    });

    // 手动验证签名（按照微信支付APIv3规范）
    const crypto = require('crypto');

    // 构造签名字符串：timestamp + "\n" + nonce + "\n" + body + "\n"
    const signString = `${timestamp}\n${nonce}\n${body}\n`;

    console.log('🔐 签名字符串:', signString);
    console.log('🔐 使用公钥验证签名...');

    // 使用公钥验证签名
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(signString);
    verify.end();

    const isValid = verify.verify(publicKey, signature, 'base64');

    console.log('🔐 签名验证结果:', isValid);
    return isValid;

  } catch (error) {
    console.error('❌ 验证微信支付签名失败:', error);
    return false;
  }
}

/**
 * 解密微信支付回调数据
 */
async function decryptWechatPayData(encryptedData) {
  try {
    console.log('🔓 解密微信支付回调数据:', encryptedData);

    if (!encryptedData.resource) {
      throw new Error('缺少resource字段');
    }

    const resource = encryptedData.resource;
    const { ciphertext, nonce, associated_data } = resource;

    // 获取API V3密钥
    const apiV3Key = getApiV3KeyByEventType(encryptedData.event_type);
    if (!apiV3Key) {
      throw new Error('无法获取API V3密钥');
    }

    // 手动实现AES-256-GCM解密
    const crypto = require('crypto');

    // 将base64编码的密文转换为Buffer
    const ciphertextBuffer = Buffer.from(ciphertext, 'base64');

    // 提取认证标签（最后16字节）
    const authTag = ciphertextBuffer.slice(-16);
    const encryptedContent = ciphertextBuffer.slice(0, -16);

    // 创建解密器
    const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(apiV3Key, 'utf8'), Buffer.from(nonce, 'utf8'));
    decipher.setAAD(Buffer.from(associated_data, 'utf8'));
    decipher.setAuthTag(authTag);

    // 解密
    let decrypted = decipher.update(encryptedContent, null, 'utf8');
    decrypted += decipher.final('utf8');

    const paymentData = JSON.parse(decrypted);
    console.log('🔓 解密后的支付数据:', paymentData);

    return paymentData;

  } catch (error) {
    console.error('❌ 解密微信支付回调数据失败:', error);
    throw error;
  }
}

/**
 * 处理支付成功
 */
async function handlePaymentSuccess({ orderId, transactionId, paymentMethod }) {
  try {
    console.log('💰 处理支付成功:', { orderId, transactionId, paymentMethod });

    const result = await transaction(async (connection) => {
      // 获取订单信息
      const [orders] = await connection.execute(
        'SELECT * FROM orders WHERE order_id = ? AND payment_status = "pending"',
        [orderId]
      );

      if (orders.length === 0) {
        throw new Error('订单不存在或已支付');
      }

      const order = orders[0];

      // 检查订单是否过期
      if (order.expire_at && new Date() > new Date(order.expire_at)) {
        throw new Error('订单已过期');
      }

      // 更新订单状态
      await connection.execute(
        `UPDATE orders SET
         payment_status = 'paid',
         transaction_id = ?,
         payment_method = ?,
         paid_at = NOW()
         WHERE order_id = ?`,
        [transactionId, paymentMethod, orderId]
      );

      // 创建会员记录
      let expireTime = null;
      if (order.product_code === 'day_card') {
        expireTime = moment().add(24, 'hours').toDate();
      }

      await connection.execute(
        `INSERT INTO memberships (
          user_id, membership_type, start_time, expire_time, order_id
        ) VALUES (?, ?, NOW(), ?, ?)`,
        [order.user_id, order.product_code, expireTime, orderId]
      );

      return {
        orderId,
        membershipType: order.product_code,
        expireTime: expireTime ? moment(expireTime).toISOString() : null
      };
    });

    console.log('✅ 支付成功处理完成:', result);
    return result;

  } catch (error) {
    console.error('❌ 处理支付成功失败:', error);
    throw error;
  }
}



/**
 * 根据证书序列号查找小程序配置
 */
function findAppConfigBySerial(serial) {
  try {
    // 遍历所有小程序配置，查找匹配的证书序列号
    const allApps = AppManager.getAllApps();
    for (const app of allApps) {
      if (app.public_key_id === serial) {
        return app;
      }
    }
    return null;
  } catch (error) {
    console.error('❌ 查找小程序配置失败:', error);
    return null;
  }
}

/**
 * 根据事件类型获取API V3密钥
 */
function getApiV3KeyByEventType(eventType) {
  try {
    // 这里可以根据事件类型或其他信息获取对应的API V3密钥
    // 暂时返回默认配置的密钥
    const defaultApp = AppManager.getAppByAppId('wxdccd0d11f4fc6e93'); // 花花版
    return defaultApp ? defaultApp.api_v3_key : null;
  } catch (error) {
    console.error('❌ 获取API V3密钥失败:', error);
    return null;
  }
}

module.exports = router;
