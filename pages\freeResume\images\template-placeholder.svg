<svg width="280" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="280" height="400" fill="url(#bg)" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- 文档图标 -->
  <g transform="translate(140, 150)">
    <!-- 文档主体 -->
    <rect x="-30" y="-40" width="50" height="70" fill="#6c757d" rx="3"/>
    <!-- 文档折角 -->
    <polygon points="20,-40 20,-25 35,-25" fill="#495057"/>
    <!-- 文档线条 -->
    <line x1="-20" y1="-25" x2="10" y2="-25" stroke="white" stroke-width="2"/>
    <line x1="-20" y1="-15" x2="15" y2="-15" stroke="white" stroke-width="2"/>
    <line x1="-20" y1="-5" x2="15" y2="-5" stroke="white" stroke-width="2"/>
    <line x1="-20" y1="5" x2="10" y2="5" stroke="white" stroke-width="2"/>
  </g>
  
  <!-- 提示文字 -->
  <text x="140" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#6c757d">
    暂无图片
  </text>
  <text x="140" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#adb5bd">
    Template Placeholder
  </text>
</svg>
