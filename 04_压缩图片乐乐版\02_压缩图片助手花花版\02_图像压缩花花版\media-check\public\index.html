<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信内容安全检测API</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #07C160;
            border-bottom: 2px solid #07C160;
            padding-bottom: 10px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .endpoint {
            margin-bottom: 30px;
        }
        .method {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get {
            background-color: #61affe;
        }
        .post {
            background-color: #49cc90;
        }
    </style>
</head>
<body>
    <h1>微信内容安全检测API</h1>
    
    <div class="container">
        <p>这是一个用于处理微信小程序内容安全检测的API服务。</p>
        <p>服务状态：<strong style="color: #07C160;">运行中</strong></p>
    </div>
    
    <div class="container">
        <h2>API端点</h2>
        
        <div class="endpoint">
            <h3><span class="method post">POST</span> /api/security-check</h3>
            <p>提交媒体文件进行内容安全检测</p>
            <h4>请求体示例：</h4>
            <pre>{
  "mediaUrl": "https://example.com/image.jpg",
  "mediaType": 2
}</pre>
            <p><strong>参数说明：</strong></p>
            <ul>
                <li><code>mediaUrl</code>: 媒体文件的URL</li>
                <li><code>mediaType</code>: 媒体类型（1:音频;2:图片;3:视频）</li>
            </ul>
        </div>
        
        <div class="endpoint">
            <h3><span class="method get">GET</span> /wechat</h3>
            <p>微信服务器验证接口</p>
        </div>
        
        <div class="endpoint">
            <h3><span class="method post">POST</span> /wechat/security-check-callback</h3>
            <p>接收微信内容安全检测结果的回调接口</p>
        </div>
    </div>
    
    <div class="container">
        <h2>使用说明</h2>
        <p>1. 在微信小程序中调用 <code>/api/security-check</code> 接口提交媒体文件进行检测</p>
        <p>2. 微信服务器会异步处理检测请求，并将结果发送到回调接口</p>
        <p>3. 回调接口接收检测结果并进行相应处理</p>
    </div>
    
    <footer style="margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
        <p>© 2023 微信内容安全检测API服务</p>
    </footer>
</body>
</html>
