<view class="container">


  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载配置信息...</text>
  </view>

  <!-- 尺寸选择区域 -->
  <view wx:else class="size-section">
    <text class="section-title">选择证件照尺寸</text>
    <view class="size-grid">
      <view
        class="size-item {{selectedSize === item.value ? 'selected' : ''}}"
        wx:for="{{sizeOptions}}"
        wx:key="value"
        bindtap="selectSize"
        data-size="{{item.value}}"
      >
        <view class="size-name">{{item.name}}</view>
        <view class="size-desc">{{item.width}}×{{item.height}}像素</view>
        <view class="size-mm">{{item.print_size}}</view>
        <!--  <view class="size-description">{{item.description}}</view> -->
      </view>
    </view>
  </view>

  <!-- 处理状态提示 -->
  <view wx:if="{{processing}}" class="processing-section">
    <view class="processing-spinner"></view>
    <text class="processing-text">正在处理照片，请稍候...</text>
  </view>

  <!-- 使用说明 -->
  <view class="tips-section">
    <text class="tips-title">使用说明</text>
    <view class="tips-list">
      <text class="tip-item">• 请选择清晰的正面照片</text>
      <text class="tip-item">• 系统将自动进行人脸识别和抠图处理</text>
    </view>
  </view>
</view>
