/**
 * 内容安全检测结果存储
 * 用于在前端存储和管理内容安全检测结果
 */

// 检测结果存储
const securityResults = new Map();

/**
 * 记录检测请求
 * @param {string} filePath 文件路径
 * @param {string} traceId 检测请求ID
 */
export function recordCheckRequest(filePath, traceId) {
  securityResults.set(filePath, {
    traceId: traceId,
    status: 'checking', // checking, pass, risky
    timestamp: Date.now()
  });
  console.log(`[安全检测存储] 记录检测请求: ${filePath} -> ${traceId}`);
  console.log(`[安全检测存储] 当前存储的检测结果数量: ${securityResults.size}`);
}

/**
 * 更新检测结果
 * @param {string} traceId 检测请求ID
 * @param {string} status 检测状态 (pass, risky)
 * @param {number} label 标签ID
 */
export function updateCheckResult(traceId, status, label) {
  console.log(`[安全检测存储] 收到检测结果更新: trace_id=${traceId}, status=${status}, label=${label}`);

  // 查找对应的文件路径
  for (const [filePath, result] of securityResults.entries()) {
    if (result.traceId === traceId) {
      result.status = status;
      result.label = label;
      result.updateTime = Date.now();
      console.log(`[安全检测存储] 成功更新检测结果: ${filePath} -> ${status}, 标签: ${label}`);
      return true;
    }
  }

  console.warn(`[安全检测存储] 未找到对应的检测请求: ${traceId}`);
  console.log(`[安全检测存储] 当前存储的检测结果数量: ${securityResults.size}`);
  if (securityResults.size > 0) {
    console.log('[安全检测存储] 当前存储的所有检测结果:');
    for (const [path, data] of securityResults.entries()) {
      console.log(`  - 路径: ${path}, 状态: ${data.status}, traceId: ${data.traceId}`);
    }
  }

  return false;
}

/**
 * 获取文件的检测状态
 * @param {string} filePath 文件路径
 * @returns {object|null} 检测结果对象或null
 */
export function getCheckResult(filePath) {
  const result = securityResults.get(filePath) || null;

  // 打印当前存储的所有检测结果，帮助调试
  console.log(`[安全检测存储] 当前存储的检测结果数量: ${securityResults.size}`);
  if (securityResults.size > 0) {
    console.log('[安全检测存储] 当前存储的所有检测结果:');
    for (const [path, data] of securityResults.entries()) {
      console.log(`  - 路径: ${path}, 状态: ${data.status}, traceId: ${data.traceId}`);
    }
  }

  if (result) {
    console.log(`[安全检测存储] 找到图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);
  } else {
    console.log(`[安全检测存储] 未找到图片 ${filePath} 的检测结果`);
  }

  return result;
}

/**
 * 清理过期的检测结果
 * @param {number} maxAge 最大保留时间(毫秒)，默认1小时
 */
export function cleanupResults(maxAge = 3600000) {
  const now = Date.now();
  for (const [filePath, result] of securityResults.entries()) {
    if (now - result.timestamp > maxAge) {
      securityResults.delete(filePath);
    }
  }
}

// 每小时清理一次过期结果
setInterval(cleanupResults, 3600000);

export default {
  recordCheckRequest,
  updateCheckResult,
  getCheckResult,
  cleanupResults
};
