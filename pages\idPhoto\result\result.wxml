<view class="container">


  <!-- 结果展示区域 -->
  <view class="result-section">
    <!-- 证件照预览 -->
    <view class="photo-preview">
      <!-- 内联加载动画 -->
      <view wx:if="{{loading}}" class="inline-loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{loadingText}}</text>
      </view>
      <!-- 图片显示容器 - 支持背景预览 -->
      <view wx:else class="image-container">
        <!-- 背景预览层 -->
        <view
          class="background-layer"
          style="{{currentBackgroundStyle}}; width: {{ 600 }}rpx; height: {{ 600 * resultInfo.dimensions.height / resultInfo.dimensions.width}}rpx;"
        ></view>
        <!-- 透明背景图片层 -->
        <image
          class="result-image"
          src="{{currentImage}}"
          mode="aspectFit"
          show-menu-by-longpress="{{true}}"
          binderror="onImageError"
          bindload="onImageLoad"
          style="width: {{ 600 }}rpx; height: {{600 * resultInfo.dimensions.height / resultInfo.dimensions.width}}rpx;"
        ></image>
      </view>
    </view>

    <!-- 底色选择 -->
    <view class="background-section">
      <text class="section-title">选择底色</text>
      <view class="color-options">
        <view
          class="color-square {{selectedBackground === item.value ? 'selected' : ''}}"
          wx:for="{{backgroundOptions}}"
          wx:key="value"
          bindtap="selectBackground"
          data-color="{{item.value}}"
        >
          <view class="color-fill" style="{{item.cssStyle}}"></view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="save-btn" bindtap="saveToAlbum" disabled="{{saving}}">
        <text wx:if="{{!saving}}">保 存 到 相 册</text>
        <text wx:else>保 存 中 ...</text>
      </button>
    </view>

    <!-- 移除高清版本提示，简化界面 -->
  </view>


</view>
