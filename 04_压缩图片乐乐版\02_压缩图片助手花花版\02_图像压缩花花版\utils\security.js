/**
 * 内容安全检测工具函数
 */
import { getOpenId } from './auth.js';
import { recordCheckRequest, updateCheckResult, getCheckResult, isImageRisky } from './securityStore.js';
import {
  checkNetworkStatus,
  uploadFileWithRetry,
  preprocessImageForUpload,
  requestQueue
} from './networkUtils.js';

/**
 * 上传图片并进行内容安全检测
 * @param {string} filePath 图片路径
 * @returns {Promise<object>} 检测结果
 */
export async function checkImageSecurity(filePath) {
  try {
    // 检查网络状态
    await checkNetworkStatus();

    // 获取用户OpenID
    const openid = await getOpenId();

    // 预处理图片（压缩），减小上传体积
    const processedFilePath = await preprocessImageForUpload(filePath, {
      maxWidth: 1280,
      maxHeight: 1280,
      quality: 0.8,
      fileType: 'jpg'
    });

    // 使用请求队列管理并发请求，在后台静默进行
    return await requestQueue.add(() => {
      // 上传图片并进行内容安全检测（带重试机制），不显示加载提示
      return new Promise((resolve, reject) => {
        uploadFileWithRetry({
          url: 'https://media-check.gbw8848.cn/api/upload-image-check',
          filePath: processedFilePath,
          name: 'image',
          formData: {
            openid: openid,
            appId: 'wxdccd0d11f4fc6e93'  // 指定使用图像压缩花花版小程序的配置
          },
          success: (res) => {
            if (res.statusCode === 200) {
              try {
                const data = JSON.parse(res.data);
                if (data.code === 0) {
                  // 记录检测请求
                  if (data.data && data.data.checkResult && data.data.checkResult.trace_id) {
                    const traceId = data.data.checkResult.trace_id;
                    recordCheckRequest(filePath, traceId);

                    // 启动轮询检测结果
                    startPollingCheckResult(traceId);
                  }
                  resolve(data.data);
                } else {
                  // 服务器返回错误，但不向用户显示
                  console.error('[安全检测] 内容检测服务器返回错误:', data.msg);
                  const error = new Error(data.msg || '内容检测失败');
                  error.code = data.code;
                  error.serverError = true;
                  reject(error);
                }
              } catch (parseError) {
                // JSON解析错误，但不向用户显示
                console.error('[安全检测] 解析内容检测响应数据失败:', parseError);
                const error = new Error('解析响应数据失败');
                error.originalError = parseError;
                reject(error);
              }
            } else {
              // HTTP状态码错误，但不向用户显示
              console.error(`[安全检测] 内容检测请求失败，状态码: ${res.statusCode}`);
              const error = new Error(`请求失败，状态码: ${res.statusCode}`);
              error.statusCode = res.statusCode;
              reject(error);
            }
          },
          fail: (err) => {
            // 请求失败，但不向用户显示
            console.error('[安全检测] 内容检测请求失败:', err);
            reject(err);
          }
        }, 2, 30000); // 最多重试2次，超时时间30秒
      });
    });
  } catch (error) {
    // 捕获错误但不显示给用户，只在控制台记录错误信息
    console.error('[安全检测] 内容安全检测失败:', error);

    // 不向用户显示任何提示，让检测在后台静默进行
    // 返回一个默认的成功结果，避免中断用户操作流程
    return {
      checkResult: {
        trace_id: 'error-' + Date.now(),
        suggest: 'pass' // 默认通过，避免阻止用户操作
      }
    };
  }
}

/**
 * 开始轮询检测结果
 * @param {string} traceId 检测请求ID
 */
function startPollingCheckResult(traceId) {
  // 轮询参数
  let attempts = 0;
  const maxAttempts = 1800; // 最多轮询1800次，相当于30分钟
  const interval = 1000; // 每1000毫秒查询一次，相当于每秒1次

  // 轮询函数
  const poll = async () => {
    attempts++;

    try {
      // 每次轮询都记录日志
      console.log(`[安全检测轮询] 第${attempts}次查询检测结果: ${traceId}`);

      // 发起查询请求
      const res = await uni.request({
        url: 'https://media-check.gbw8848.cn/api/check-result',
        method: 'POST',
        data: { trace_id: traceId }
      });

      // 处理响应
      if (res.statusCode === 200 && res.data.code === 0) {
        const { status, label } = res.data.data;

        // 打印每次轮询的结果
        console.log(`[安全检测轮询] 第${attempts}次查询结果: trace_id=${traceId}, status=${status}, label=${label || '无'}`);

        // 如果检测完成，更新本地状态并停止轮询
        if (status !== 'checking') {
          updateCheckResult(traceId, status, label);
          console.log(`[安全检测轮询] 检测完成，停止轮询: ${traceId}，共查询${attempts}次`);
          return;
        }
      } else {
        // 打印请求失败的情况
        console.log(`[安全检测轮询] 第${attempts}次查询失败: 状态码=${res.statusCode}, 响应=${JSON.stringify(res.data)}`);
      }

      // 如果达到最大尝试次数，停止轮询
      if (attempts >= maxAttempts) {
        console.log(`[安全检测轮询] 达到最大尝试次数(${maxAttempts})，停止轮询: ${traceId}`);
        return;
      }

      // 继续轮询，固定间隔1秒
      setTimeout(poll, interval);
    } catch (error) {
      // 详细记录错误信息
      console.error(`[安全检测轮询] 第${attempts}次查询发生错误:`, error);
      console.log(`[安全检测轮询] 第${attempts}次查询错误详情: ${error.message || '未知错误'}`);

      // 如果达到最大尝试次数，停止轮询
      if (attempts >= maxAttempts) {
        console.log(`[安全检测轮询] 达到最大尝试次数(${maxAttempts})，停止轮询: ${traceId}`);
        return;
      }

      // 即使失败也继续轮询，短暂延迟后重试
      console.log(`[安全检测轮询] 第${attempts}次查询失败，将在${interval}毫秒后重试`);
      setTimeout(poll, interval);
    }
  };

  // 开始第一次轮询，延迟1秒，给服务器一些处理时间
  console.log(`[安全检测轮询] 将在1秒后开始轮询: ${traceId}`);
  setTimeout(poll, 1000);
}

/**
 * 处理内容检测结果
 * @param {object} result 检测结果
 * @returns {boolean} 是否通过检测
 */
export function handleCheckResult(result) {
  // 检测是异步的，这里只是记录trace_id
  if (result && result.checkResult && result.checkResult.trace_id) {
    console.log('[安全检测] 内容检测请求已发送，trace_id:', result.checkResult.trace_id);
  }

  // 由于微信内容安全检测是异步的，这里默认返回通过
  return true;
}

/**
 * 批量检查图片是否违规
 * @param {Array<string>} filePaths 图片路径数组
 * @param {boolean} checkLatest 是否查询最新结果
 * @returns {Promise<Array<object>>} 检测结果对象数组
 */
export async function batchCheckImages(filePaths, checkLatest = false) {
  if (!Array.isArray(filePaths) || filePaths.length === 0) {
    return [];
  }

  console.log(`[安全检测] 批量检查 ${filePaths.length} 张图片，checkLatest=${checkLatest}`);

  // 使用本地存储的结果，不再向服务器查询
  const results = [];
  for (const path of filePaths) {
    // 当用户点击"开始压缩"按钮时，使用本地存储的结果，不查询服务器
    // 因为轮询已经在后台持续更新结果
    const result = await isImageRisky(path, false);
    console.log(`[安全检测] 图片 ${path} 的检测结果: isRisky=${result.isRisky}, isChecking=${result.isChecking}`);
    results.push({
      path,
      ...result
    });
  }

  return results;
}

/**
 * 设置内容安全检测回调处理器
 * 这个函数在App.vue的onLaunch中调用
 * 简化版：不需要设置回调处理器，在用户操作时直接查询最新结果
 */
export function setupCallbackHandler() {
  console.log('[安全检测] 内容安全检测初始化完成');
}
