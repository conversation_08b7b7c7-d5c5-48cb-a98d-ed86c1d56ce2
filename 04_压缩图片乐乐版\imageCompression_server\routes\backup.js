const express = require('express');
const router = express.Router();
const cronJobs = require('../utils/cronJobs');

/**
 * 手动执行数据库备份
 * POST /api/backup/execute
 */
router.post('/execute', async (req, res) => {
  try {
    console.log('📋 收到手动备份请求');
    
    const result = await cronJobs.manualDatabaseBackup();
    
    res.json({
      success: true,
      message: '数据库备份执行成功',
      data: result
    });
  } catch (error) {
    console.error('手动备份失败:', error);
    res.status(500).json({
      success: false,
      message: '数据库备份执行失败',
      error: error.message
    });
  }
});

/**
 * 测试COS连接
 * GET /api/backup/test-cos
 */
router.get('/test-cos', async (req, res) => {
  try {
    console.log('🔍 测试COS连接');
    
    const isConnected = await cronJobs.testCOSConnection();
    
    res.json({
      success: true,
      message: isConnected ? 'COS连接正常' : 'COS连接失败',
      data: {
        connected: isConnected
      }
    });
  } catch (error) {
    console.error('COS连接测试失败:', error);
    res.status(500).json({
      success: false,
      message: 'COS连接测试失败',
      error: error.message
    });
  }
});

/**
 * 获取备份统计信息
 * GET /api/backup/stats
 */
router.get('/stats', async (req, res) => {
  try {
    console.log('📊 获取备份统计信息');
    
    const stats = await cronJobs.getBackupStats();
    
    res.json({
      success: true,
      message: '获取备份统计信息成功',
      data: stats
    });
  } catch (error) {
    console.error('获取备份统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取备份统计信息失败',
      error: error.message
    });
  }
});

/**
 * 获取定时任务状态
 * GET /api/backup/cron-status
 */
router.get('/cron-status', async (req, res) => {
  try {
    console.log('⏰ 获取定时任务状态');
    
    const status = cronJobs.getStatus();
    
    res.json({
      success: true,
      message: '获取定时任务状态成功',
      data: status
    });
  } catch (error) {
    console.error('获取定时任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取定时任务状态失败',
      error: error.message
    });
  }
});

/**
 * 获取系统统计信息
 * GET /api/backup/system-stats
 */
router.get('/system-stats', async (req, res) => {
  try {
    console.log('📈 获取系统统计信息');
    
    const stats = await cronJobs.getSystemStats();
    
    res.json({
      success: true,
      message: '获取系统统计信息成功',
      data: stats
    });
  } catch (error) {
    console.error('获取系统统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统统计信息失败',
      error: error.message
    });
  }
});

module.exports = router;
