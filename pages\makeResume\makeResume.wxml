<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
  </view>

  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 欢迎语 -->
    <view class="welcomeSection">
      <view class="welcomeTitle">欢迎使用，</view>
      <view class="welcomeDesc">Hi, 根据自身需求填写简历，简历模板只显示已填写内容，不填写不显示。填写完成后选择模板生成简历。</view>
    </view>

    <!-- 调试信息 -->
    <!-- <view style="background: #f0f0f0; padding: 10px; margin: 10px; font-size: 12px;">
      <text>调试信息：</text>
      <text>activeModules长度: {{activeModules.length}}</text>
      <text>availableModulesToAdd长度: {{availableModulesToAdd.length}}</text>
      <text>basicInfo.name: {{basicInfo.name}}</text>
    </view>
    -->

    <!-- 基本信息卡片 -->
    <view class="infoCard">
      <block wx:for="{{activeModules}}" wx:key="id">
        <!-- 基本信息 -->
        <block wx:if="{{item.type === 'basicInfo'}}">
          <view class="infoSection">
            <view class="sectionContent">
              <view class="mainInfo" bindtap="handleModuleClick" data-type="basicInfo">
                <text class="name {{!basicInfo.name ? 'placeholder' : ''}}">{{basicInfo.name || '姓名'}}</text>

                <!-- 紧凑的基本信息显示 -->
                <view class="compactInfo">
                  <!-- 第一行：性别、年龄、电话 -->
                  <text class="infoLine">
                    <text class="{{!basicInfo.gender ? 'placeholder' : ''}}">{{basicInfo.gender || '性别'}}</text>
                    <text> | </text>
                    <text class="{{!basicInfo.age ? 'placeholder' : ''}}">{{basicInfo.age || '年龄'}}</text>
                    <text> | </text>
                    <text class="{{!basicInfo.phone ? 'placeholder' : ''}}">{{basicInfo.phone || '联系方式'}}</text>
                  </text>

                  <!-- 第二行：邮箱 -->
                  <text class="infoLine">
                    <text>邮箱：</text>
                    <text class="{{!basicInfo.email ? 'placeholder' : ''}}">{{basicInfo.email || '请添加邮箱'}}</text>
                  </text>

                  <!-- 第三行：现居城市 -->
                  <text class="infoLine">
                    <text>现居：</text>
                    <text class="{{!basicInfo.city ? 'placeholder' : ''}}">{{basicInfo.city || '请添加城市'}}</text>
                  </text>
                </view>
              </view>
              <view class="addPhoto" bindtap="{{basicInfo.photoUrl ? 'showPhotoActionSheet' : 'chooseImage'}}">
                <block wx:if="{{!basicInfo.photoUrl}}">
                  <text class="addIcon">+</text>
                  <text class="addText">添加</text>
                  <text class="addText">证件照</text>
                </block>
                <image wx:else class="photo" src="{{basicInfo.photoUrl}}" mode="aspectFill"></image>
              </view>
            </view>
          </view>
        </block>

        <!-- 求职意向 -->
        <block wx:elif="{{item.type === 'jobIntention'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="jobIntention">
            <view class="sectionTitle">求职意向</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="subText">
                  <block wx:if="{{jobIntention.position || jobIntention.city || jobIntention.salary || jobIntention.status}}">
                    <text>{{jobIntention.position ? jobIntention.position + (jobIntention.city ? ' | ' : '') : ''}}</text>
                    <text>{{jobIntention.city ? jobIntention.city + (jobIntention.salary ? ' | ' : '') : ''}}</text>
                    <text>{{jobIntention.salary ? jobIntention.salary + (jobIntention.status ? ' | ' : '') : ''}}</text>
                    <text>{{jobIntention.status ? jobIntention.status : ''}}</text>
                  </block>
                  <block wx:else>点击添加</block>
                </text>
              </view>
            </view>
          </view>
        </block>

        <!-- 教育经历 -->
        <block wx:elif="{{item.type === 'education'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="education">
            <view class="sectionTitle">教育经历</view>
            <view class="sectionContent" wx:for="{{education}}" wx:key="index" wx:for-item="eduItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="school">{{eduItem.school}}</text>
                  <text class="dateText">{{eduItem.startDate}} - {{eduItem.endDate}}</text>
                </view>
                <text class="subText">{{eduItem.degree}} {{eduItem.major}}</text>
                <text class="description" wx:if="{{eduItem.description}}" user-select="text">{{eduItem.description}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 在校经历 -->
        <block wx:elif="{{item.type === 'school'}}">
          <view class="infoSection school-section" bindtap="handleModuleClick" data-type="school">
            <view class="sectionTitle">在校经历</view>
            <view class="sectionContent" wx:for="{{school}}" wx:key="index" wx:for-item="schoolItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="title">{{schoolItem.role}}</text>
                    <text class="dateText">{{schoolItem.startDate}} - {{schoolItem.endDate}}</text>
                </view>
                <text class="description" wx:if="{{schoolItem.content}}" user-select="text">{{schoolItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 实习经历 -->
        <block wx:elif="{{item.type === 'internship'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="internship">
            <view class="sectionTitle">实习经历</view>
            <view class="sectionContent" wx:for="{{internship}}" wx:key="index" wx:for-item="internItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="school">{{internItem.company}}</text>
                  <text class="dateText">{{internItem.startDate}} - {{internItem.endDate}}</text>
                </view>
                <text class="subText">{{internItem.position}}</text>
                <text class="description" wx:if="{{internItem.content}}" user-select="text">{{internItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 工作经历 -->
        <block wx:elif="{{item.type === 'work'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="work">
            <view class="sectionTitle">工作经历</view>
            <view class="sectionContent" wx:for="{{work}}" wx:key="index" wx:for-item="workItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="school">{{workItem.company}}</text>
                  <text class="dateText">{{workItem.startDate}} - {{workItem.endDate}}</text>
                </view>
                <text class="subText">{{workItem.position}}</text>
                <text class="description" wx:if="{{workItem.description}}" user-select="text">{{workItem.description}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 项目经历 -->
        <block wx:elif="{{item.type === 'project'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="project">
            <view class="sectionTitle">项目经历</view>
            <view class="sectionContent" wx:for="{{project}}" wx:key="index" wx:for-item="projectItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="school">{{projectItem.projectName}}</text>
                  <text class="dateText">{{projectItem.startDate}} - {{projectItem.endDate}}</text>
                </view>
                <text class="subText">{{projectItem.role}}</text>
                <text class="description" wx:if="{{projectItem.description}}" user-select="text">{{projectItem.description}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 职业技能 -->
        <block wx:elif="{{item.type === 'skills'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="skills">
            <view class="sectionTitle">技能特长</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <view class="skills">
                  <text class="skillItem" wx:for="{{skills}}" wx:key="index" wx:for-item="skillItem">{{skillItem}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 奖项证书 -->
        <block wx:elif="{{item.type === 'awards'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="awards">
            <view class="sectionTitle">奖项证书</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <view class="skills">
                  <text class="skillItem" wx:for="{{awards}}" wx:key="index" wx:for-item="awardItem">{{awardItem}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 兴趣爱好 -->
        <block wx:elif="{{item.type === 'interests'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="interests">
            <view class="sectionTitle">兴趣爱好</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <view class="skills">
                  <text class="skillItem" wx:for="{{interests}}" wx:key="index" wx:for-item="interestItem">{{interestItem}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 自我评价 -->
        <block wx:elif="{{item.type === 'evaluation'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="evaluation">
            <view class="sectionTitle">自我评价</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="description" user-select="text">{{evaluation[0].content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块一 -->
        <block wx:elif="{{item.type === 'custom1'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="custom1">
            <view class="sectionTitle">{{custom1[0].customName || '自定义'}}</view>
            <view class="sectionContent" wx:for="{{custom1}}" wx:key="index" wx:for-item="customItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="title">{{customItem.customName}}</text>
                  <text class="dateText">{{customItem.startDate}} - {{customItem.endDate}}</text>
                </view>
                <text class="roleText">{{customItem.role}}</text>
                <text class="description" wx:if="{{customItem.content}}" user-select="text">{{customItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块二 -->
        <block wx:elif="{{item.type === 'custom2'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="custom2">
            <view class="sectionTitle">{{custom2[0].customName || '自定义'}}</view>
            <view class="sectionContent" wx:for="{{custom2}}" wx:key="index" wx:for-item="customItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="title">{{customItem.customName}}</text>
                  <text class="dateText">{{customItem.startDate}} - {{customItem.endDate}}</text>
                </view>
                <text class="roleText">{{customItem.role}}</text>
                <text class="description" wx:if="{{customItem.content}}" user-select="text">{{customItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块三 -->
        <block wx:elif="{{item.type === 'custom3'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="custom3">
            <view class="sectionTitle">{{custom3[0].customName || '自定义'}}</view>
            <view class="sectionContent" wx:for="{{custom3}}" wx:key="index" wx:for-item="customItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="title">{{customItem.customName}}</text>
                  <text class="dateText">{{customItem.startDate}} - {{customItem.endDate}}</text>
                </view>
                <text class="roleText">{{customItem.role}}</text>
                <text class="description" wx:if="{{customItem.content}}" user-select="text">{{customItem.content}}</text>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>

    <!-- 添加更多模块 -->
    <view class="modulesSection">
      <view class="sectionTitle">添加更多模块</view>
      <view class="modulesGrid">
        <view class="moduleItem addItem"
              wx:for="{{availableModulesToAdd}}"
              wx:key="id"
              bindtap="addModule"
              data-type="{{item.type}}">
          <!-- <image class="moduleIcon" src="/images/add_icon.png"></image> -->
          <!-- <text class="addIcon">+</text> -->
          <view class="moduleName">{{item.name}}</view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottomBar">
      <view class="actionButtons">
        <view class="actionBtn" bindtap="handleModules">
          <image class="icon" src="./images/moduleManage.png" mode="aspectFit"></image>
          <text>模块管理</text>
        </view>
        <view class="actionBtn" bindtap="handleAI">
          <image class="icon" src="./images/aiRobot.png" mode="aspectFit"></image>
          <text>AI智能优化</text>
        </view>
        <view class="actionBtn" bindtap="generateResume">
          <image class="icon" src="./images/generate.png" mode="aspectFit"></image>
          <text>生成简历</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 隐藏的Canvas用于图片压缩 -->
  <canvas canvas-id="compressCanvas" style="position: fixed; top: -1000px; left: -1000px; width: 400px; height: 600px;"></canvas>

  <!-- 隐藏的Canvas用于智能裁剪 -->
  <canvas canvas-id="smartCropCanvas" style="position: fixed; top: -2000px; left: -1000px; width: 590rpx; height: 826rpx;"></canvas>

  <!-- 证件照操作浮窗 -->
  <view class="photo-action-sheet {{showPhotoActions ? 'show' : ''}}" bindtap="hidePhotoActionSheet">
    <view class="action-sheet-content" catchtap="">
      <view class="action-sheet-header">
        <text class="action-sheet-title">证件照操作</text>
        <view class="close-btn" bindtap="hidePhotoActionSheet">
          <text class="close-icon">×</text>
        </view>
      </view>
      <view class="action-sheet-body">
        <view class="action-item" bindtap="replacePhoto">
          <view class="action-icon replace">
            <text class="icon-text">📷</text>
          </view>
          <text class="action-text">替换证件照</text>
        </view>
        <view class="action-item delete" bindtap="deletePhoto">
          <view class="action-icon delete-icon">
            <text class="icon-text">🗑</text>
          </view>
          <text class="action-text">删除证件照</text>
        </view>
      </view>
      <view class="action-sheet-footer">
        <view class="action-item cancel" bindtap="hidePhotoActionSheet">
          <text class="action-text">取消</text>
        </view>
      </view>
    </view>
  </view>
</view>