#!/usr/bin/env node

/**
 * 一键部署脚本
 * 自动完成数据库初始化和商品数据插入
 */

// 加载环境变量
require('dotenv').config();

const { query } = require('../config/database');

/**
 * 初始化商品数据
 */
async function initProducts() {
  console.log('🚀 开始初始化商品数据...\n');

  const products = [
    // 图像压缩花花版
    ['wxdccd0d11f4fc6e93', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1],
    ['wxdccd0d11f4fc6e93', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, null, 2],
    
    // 压缩图片助手花花版
    ['wx_compress_assistant_huahua_002', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1],
    ['wx_compress_assistant_huahua_002', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, null, 2],
    
    // 图像压缩图片汪汪版
    ['wx_imagecomp_wangwang_003', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1],
    ['wx_imagecomp_wangwang_003', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, null, 2]
  ];

  try {
    // 检查是否已有商品数据
    const existingProducts = await query('SELECT COUNT(*) as count FROM products');
    
    if (existingProducts[0].count > 0) {
      console.log('⚠️  商品数据已存在，跳过初始化');
      console.log(`📊 当前商品数量: ${existingProducts[0].count}`);
      return;
    }

    // 插入商品数据
    for (const product of products) {
      await query(
        `INSERT INTO products (app_id, product_code, product_name, description, price, duration_hours, sort_order)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        product
      );
      
      console.log(`✅ 添加商品: ${product[0]} - ${product[2]} (¥${product[4]})`);
    }

    console.log('\n🎉 商品数据初始化完成！');
    
    // 显示统计信息
    const stats = await query(`
      SELECT app_id, COUNT(*) as count 
      FROM products 
      GROUP BY app_id
    `);
    
    console.log('\n📊 商品统计:');
    stats.forEach(stat => {
      console.log(`   ${stat.app_id}: ${stat.count} 个商品`);
    });

  } catch (error) {
    console.error('❌ 商品数据初始化失败:', error.message);
    throw error;
  }
}

/**
 * 显示部署状态
 */
async function showStatus() {
  try {
    console.log('\n📋 部署状态检查');
    console.log('==================\n');

    // 检查数据库连接
    await query('SELECT 1');
    console.log('✅ 数据库连接正常');

    // 检查表结构
    const tables = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE()
    `);
    
    console.log(`✅ 数据库表: ${tables.length} 个`);
    tables.forEach(table => {
      console.log(`   - ${table.table_name || table.TABLE_NAME}`);
    });

    // 检查商品数据
    const productCount = await query('SELECT COUNT(*) as count FROM products');
    console.log(`✅ 商品数据: ${productCount[0].count} 个`);

    // 检查小程序配置
    const appIds = await query('SELECT DISTINCT app_id FROM products');
    console.log(`✅ 配置的小程序: ${appIds.length} 个`);
    appIds.forEach(app => {
      console.log(`   - ${app.app_id}`);
    });

    console.log('\n🎉 部署检查完成！');

  } catch (error) {
    console.error('❌ 状态检查失败:', error.message);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'init':
        await initProducts();
        break;
        
      case 'status':
        await showStatus();
        break;
        
      case 'all':
        await initProducts();
        await showStatus();
        break;
        
      default:
        console.log('📖 部署脚本使用说明:');
        console.log('');
        console.log('命令:');
        console.log('  node scripts/deploy.js init     # 初始化商品数据');
        console.log('  node scripts/deploy.js status   # 检查部署状态');
        console.log('  node scripts/deploy.js all      # 初始化并检查状态');
        console.log('');
        break;
    }
  } catch (error) {
    console.error('❌ 部署失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  initProducts,
  showStatus
};
