/**
 * 内容安全检测工具函数
 */
import { getOpenId } from './auth.js';
import { recordCheckRequest, updateCheckResult, getCheckResult } from './securityStore.js';
import {
  checkNetworkStatus,
  uploadFileWithRetry,
  preprocessImageForUpload,
  requestQueue
} from './networkUtils.js';

/**
 * 上传图片并进行内容安全检测
 * @param {string} filePath 图片路径
 * @returns {Promise<object>} 检测结果
 */
export async function checkImageSecurity(filePath) {
  try {
    // 检查网络状态
    await checkNetworkStatus();

    // 获取用户OpenID
    const openid = await getOpenId();

    // 预处理图片（压缩），减小上传体积（针对6Mbps带宽优化）
    const processedFilePath = await preprocessImageForUpload(filePath, {
      maxWidth: 800,
      maxHeight: 800,
      quality: 0.6,
      fileType: 'jpg'
    });

    // 使用请求队列管理并发请求，在后台静默进行
    return await requestQueue.add(() => {
      // 上传图片并进行内容安全检测（带重试机制），不显示加载提示
      return new Promise((resolve, reject) => {
        uploadFileWithRetry({
          url: 'https://media-check.gbw8848.cn/api/upload-image-check',
          filePath: processedFilePath,
          name: 'image',
          formData: {
            openid: openid
          },
          success: (res) => {
            if (res.statusCode === 200) {
              try {
                const data = JSON.parse(res.data);
                if (data.code === 0) {
                  // 记录检测请求
                  const traceId = data.data.checkResult.trace_id;
                  recordCheckRequest(filePath, traceId);

                  // 启动轮询检测结果
                  startPollingCheckResult(traceId);

                  resolve(data.data);
                } else {
                  // 服务器返回错误，但不向用户显示
                  console.error('内容检测服务器返回错误:', data.msg);
                  const error = new Error(data.msg || '内容检测失败');
                  error.code = data.code;
                  error.serverError = true;
                  reject(error);
                }
              } catch (parseError) {
                // JSON解析错误，但不向用户显示
                console.error('解析内容检测响应数据失败:', parseError);
                const error = new Error('解析响应数据失败');
                error.originalError = parseError;
                reject(error);
              }
            } else {
              // HTTP状态码错误，但不向用户显示
              console.error(`内容检测请求失败，状态码: ${res.statusCode}`);
              const error = new Error(`请求失败，状态码: ${res.statusCode}`);
              error.statusCode = res.statusCode;
              reject(error);
            }
          },
          fail: (err) => {
            // 请求失败，但不向用户显示
            console.error('内容检测请求失败:', err);
            reject(err);
          }
        }, 2, 60000); // 最多重试2次，超时时间60秒（适应6Mbps带宽）
      });
    });
  } catch (error) {
    // 捕获错误但不显示给用户，只在控制台记录错误信息
    console.error('内容安全检测失败:', error);

    // 不向用户显示任何提示，让检测在后台静默进行
    // 返回一个默认的成功结果，避免中断用户操作流程
    return {
      checkResult: {
        trace_id: 'error-' + Date.now(),
        suggest: 'pass' // 默认通过，避免阻止用户操作
      }
    };
  }
}

/**
 * 处理内容检测结果
 * @param {object} result 检测结果
 * @returns {boolean} 是否通过检测
 */
export function handleCheckResult(result) {
  // 检测是异步的，这里只是记录trace_id
  // 实际结果会通过回调接口返回
  console.log('内容检测请求已发送，trace_id:', result.checkResult.trace_id);

  // 由于微信内容安全检测是异步的，这里默认返回通过
  // 实际应用中，可以根据业务需求决定是否等待检测结果
  return true;
}

/**
 * 检查图片是否违规
 * @param {string} filePath 图片路径
 * @param {boolean} checkLatest 是否查询最新结果
 * @returns {Promise<object>} 检测结果对象 {isRisky, isChecking, message}
 */
export async function isImageRisky(filePath, checkLatest = false) {
  console.log(`[安全检测] 正在查询图片 ${filePath} 的检测结果`);

  // 获取本地存储的检测结果
  let result = getCheckResult(filePath);

  // 如果需要查询最新结果
  if (checkLatest && result && result.traceId) {
    try {
      console.log(`[安全检测] 查询最新检测结果: ${result.traceId}`);

      // 发起查询请求
      const res = await uni.request({
        url: 'https://media-check.gbw8848.cn/api/check-result',
        method: 'POST',
        data: { trace_id: result.traceId }
      });

      // 处理响应
      if (res.statusCode === 200 && res.data.code === 0) {
        const { status, label } = res.data.data;

        // 如果检测完成，更新本地状态
        if (status !== 'checking') {
          updateCheckResult(result.traceId, status, label);

          // 重新获取更新后的结果
          result = getCheckResult(filePath);
        }
      }
    } catch (error) {
      console.error('[安全检测] 查询最新结果失败:', error);
      // 出错时继续使用本地结果
    }
  }

  // 未找到检测结果
  if (!result) {
    console.log(`[安全检测] 未找到图片 ${filePath} 的检测结果，可能尚未完成检测`);
    return {
      isRisky: false,
      isChecking: false,
      message: '未找到检测结果'
    };
  }

  console.log(`[安全检测] 图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);

  // 检测中
  if (result.status === 'checking') {
    console.log(`[安全检测] 图片 ${filePath} 正在检测中，请稍候`);
    return {
      isRisky: false,
      isChecking: true,
      message: '内容安全检测尚未完成，请稍等片刻'
    };
  }

  // 违规内容
  if (result.status === 'risky') {
    console.log(`[安全检测] 图片 ${filePath} 被检测为违规内容，将拦截压缩`);
    return {
      isRisky: true,
      isChecking: false,
      message: '当前图片无法处理，请尝试其他图片'
    };
  }

  // 检测通过
  console.log(`[安全检测] 图片 ${filePath} 检测通过，允许压缩`);
  return {
    isRisky: false,
    isChecking: false,
    message: '检测通过'
  };
}

/**
 * 开始轮询检测结果
 * @param {string} traceId 检测请求ID
 */
function startPollingCheckResult(traceId) {
  // 轮询参数
  let attempts = 0;
  const interval = 1000; // 每1000毫秒查询一次，相当于每秒1次

  // 轮询函数
  const poll = async () => {
    attempts++;

    try {
      console.log(`[安全检测轮询] 第${attempts}次查询检测结果: ${traceId}`);

      // 发起查询请求
      const res = await uni.request({
        url: 'https://media-check.gbw8848.cn/api/check-result',
        method: 'POST',
        data: { trace_id: traceId }
      });

      // 处理响应
      if (res.statusCode === 200 && res.data.code === 0) {
        const { status, label } = res.data.data;

        console.log(`[安全检测轮询] 收到检测结果: trace_id=${traceId}, status=${status}, label=${label}`);

        // 如果检测完成，更新本地状态并停止轮询
        if (status !== 'checking') {
          updateCheckResult(traceId, status, label);
          console.log(`[安全检测轮询] 检测完成，停止轮询: ${traceId}，共查询${attempts}次`);
          return;
        }
      }

      // 继续轮询，固定间隔500毫秒
      setTimeout(poll, interval);
    } catch (error) {
      console.error('[安全检测轮询] 查询失败:', error);

      // 即使失败也继续轮询，短暂延迟后重试
      setTimeout(poll, interval);
    }
  };

  // 开始第一次轮询，延迟1秒，给服务器一些处理时间
  console.log(`[安全检测轮询] 将在1秒后开始轮询: ${traceId}`);
  setTimeout(poll, 1000);
}

/**
 * 批量查询检测结果
 * @param {string[]} traceIds 检测请求ID数组
 * @returns {Promise<object>} 检测结果对象，key为traceId，value为检测结果
 */
export async function batchCheckResults(traceIds) {
  if (!traceIds || traceIds.length === 0) {
    return {};
  }

  try {
    console.log(`[安全检测] 批量查询${traceIds.length}个检测结果`);

    // 发起批量查询请求
    const res = await uni.request({
      url: 'https://media-check.gbw8848.cn/api/batch-check-result',
      method: 'POST',
      data: { trace_ids: traceIds }
    });

    // 处理响应
    if (res.statusCode === 200 && res.data.code === 0) {
      const results = res.data.data;

      // 更新本地状态
      for (const [traceId, result] of Object.entries(results)) {
        if (result.status !== 'checking') {
          // 直接更新检测结果，securityStore.js中会处理文件路径关联
          updateCheckResult(traceId, result.status, result.label);
        }
      }

      return results;
    }

    return {};
  } catch (error) {
    console.error('[安全检测] 批量查询失败:', error);
    return {};
  }
}

/**
 * 设置回调处理函数
 * 用于接收微信的回调消息
 */
export function setupCallbackHandler() {
  // 监听从服务器发来的检测结果
  // 这里可以使用WebSocket或轮询等方式
  // 由于小程序环境限制，我们使用一个简单的模拟实现

  // 模拟接收回调的函数
  global.handleSecurityCallback = function(traceId, suggest, label) {
    console.log(`收到内容检测回调: trace_id=${traceId}, suggest=${suggest}, label=${label}`);
    updateCheckResult(traceId, suggest === 'risky' ? 'risky' : 'pass', label);
  };

  console.log('内容安全检测回调处理器已设置');
}
