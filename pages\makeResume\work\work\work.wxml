<view class="container">
  <!-- 工作经历列表 -->
  <view class="workList" wx:if="{{workFormData.length > 0}}">
    <view class="workItem" 
          wx:for="{{workFormData}}" 
          wx:key="index"
          bindlongpress="handleLongPress"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd"
          data-index="{{index}}"
          style="{{currentIndex === index ? 'background: #f5f5f5; transform: scale(1.02);' : ''}}">
      <view class="itemContent" bindtap="editWork" data-index="{{index}}">
        <view class="mainInfo">
          <text class="company">{{item.company}}</text>
          <text class="subText">{{item.position}}</text>
        </view>
        <text class="dateText">{{item.startDate}} - {{item.endDate}}</text>
      </view>
      <view class="actionButtons">
        <view class="deleteBtn" catchtap="deleteWork" data-index="{{index}}">
          <text class="deleteIcon">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="emptyState" wx:else>
    <text class="emptyText">暂无工作经历，点击下方按钮添加</text>
  </view>

  <!-- 添加按钮 -->
  <view class="addBtn" bindtap="addWork">
    + 添加工作经历
  </view>
</view> 