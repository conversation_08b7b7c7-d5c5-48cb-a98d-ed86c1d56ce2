const express = require('express');
const axios = require('axios');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { getAccessToken } = require('../utils/access_token');
const config = require('../config');

const router = express.Router();

// 存储检测结果的Map
// key: trace_id, value: {suggest, label, timestamp}
const checkResults = new Map();

/**
 * 获取OpenID接口
 * POST /api/get-openid
 */
router.post('/get-openid', async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        code: -1,
        msg: '缺少code参数'
      });
    }

    // 确定使用哪个小程序的配置
    let appId = config.wechat.appId; // 默认使用配置文件中的appId
    let appSecret = config.wechat.appSecret;
    let appName = '默认小程序';

    // 如果请求中提供了appId，则查找对应的小程序配置
    if (req.body.appId) {
      const appConfig = config.wechat.apps.find(app => app.appId === req.body.appId);
      if (appConfig) {
        appId = appConfig.appId;
        appSecret = appConfig.appSecret;
        appName = appConfig.name;
        console.log(`使用小程序[${appName}]的配置获取OpenID`);
      } else {
        console.warn(`未找到appId为${req.body.appId}的小程序配置，使用默认配置`);
      }
    }

    // 调用微信接口获取OpenID
    const response = await axios.get(
      `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`
    );

    if (response.data.errcode) {
      throw new Error(`获取OpenID失败: ${response.data.errmsg}`);
    }

    // 返回OpenID
    res.json({
      code: 0,
      data: {
        openid: response.data.openid
      },
      msg: 'success'
    });
  } catch (error) {
    console.error('获取OpenID出错:', error);
    res.status(500).json({
      code: -1,
      msg: '获取OpenID失败',
      error: error.message
    });
  }
});

// 配置文件存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    // 确保上传目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

/**
 * 内容安全检测API
 * POST /api/security-check
 */
router.post('/security-check', async (req, res) => {
  try {
    const { mediaUrl, mediaType, appId, openid } = req.body;

    if (!mediaUrl || !mediaType) {
      return res.status(400).json({
        code: -1,
        msg: '缺少必要参数'
      });
    }

    if (!openid) {
      return res.status(400).json({
        code: -1,
        msg: '缺少openid参数，微信内容安全检测API需要用户的openid'
      });
    }

    // 确定使用哪个小程序的配置
    let useAppId = config.wechat.appId; // 默认使用配置文件中的appId
    let useAppSecret = config.wechat.appSecret;
    let appName = '默认小程序';

    // 如果请求中提供了appId，则查找对应的小程序配置
    if (appId) {
      const appConfig = config.wechat.apps.find(app => app.appId === appId);
      if (appConfig) {
        useAppId = appConfig.appId;
        useAppSecret = appConfig.appSecret;
        appName = appConfig.name;
      } else {
        console.warn(`未找到appId为${appId}的小程序配置，使用默认配置`);
      }
    }

    // 获取微信access_token
    const accessToken = await getAccessToken(useAppId, useAppSecret);

    // 调用微信内容安全检测API
    const response = await axios.post(
      `https://api.weixin.qq.com/wxa/media_check_async?access_token=${accessToken}`,
      {
        media_url: mediaUrl,
        media_type: mediaType,
        version: 2, // API版本，目前为2
        scene: 1, // 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
        openid: openid // 添加用户openid
      }
    );

    console.log(`小程序[${appName}]微信API响应:`, response.data);

    res.json({
      code: 0,
      data: response.data,
      msg: 'success'
    });
  } catch (error) {
    console.error('安全检测请求出错:', error);
    res.status(500).json({
      code: -1,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});

/**
 * 上传图片并进行内容安全检测
 * POST /api/upload-image-check
 */
router.post('/upload-image-check', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: -1,
        msg: '未上传图片'
      });
    }

    // 获取文件信息
    const file = req.file;
    const { appId, openid } = req.body;

    if (!openid) {
      return res.status(400).json({
        code: -1,
        msg: '缺少openid参数，微信内容安全检测API需要用户的openid'
      });
    }

    // 图片类型
    const mediaType = 2; // 2表示图片

    // 生成可访问的URL
    const baseUrl = `http://${req.headers.host}`;
    const fileUrl = `${baseUrl}/uploads/${file.filename}`;

    // 确定使用哪个小程序的配置
    let useAppId = config.wechat.appId; // 默认使用配置文件中的appId
    let useAppSecret = config.wechat.appSecret;
    let appName = '默认小程序';

    // 如果请求中提供了appId，则查找对应的小程序配置
    if (appId) {
      const appConfig = config.wechat.apps.find(app => app.appId === appId);
      if (appConfig) {
        useAppId = appConfig.appId;
        useAppSecret = appConfig.appSecret;
        appName = appConfig.name;
      } else {
        console.warn(`未找到appId为${appId}的小程序配置，使用默认配置`);
      }
    }

    // 获取微信access_token
    const accessToken = await getAccessToken(useAppId, useAppSecret);

    // 调用微信内容安全检测API
    const response = await axios.post(
      `https://api.weixin.qq.com/wxa/media_check_async?access_token=${accessToken}`,
      {
        media_url: fileUrl,
        media_type: mediaType,
        version: 2, // API版本，目前为2
        scene: 1, // 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
        openid: openid // 添加用户openid
      }
    );

    console.log(`小程序[${appName}]微信API响应:`, response.data);

    // 返回结果
    res.json({
      code: 0,
      data: {
        checkResult: response.data,
        fileUrl: fileUrl,
        fileName: file.filename,
        originalName: file.originalname,
        size: file.size
      },
      msg: 'success'
    });
  } catch (error) {
    console.error('上传和检测出错:', error);
    res.status(500).json({
      code: -1,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});

/**
 * 查询内容安全检测结果
 * POST /api/check-result
 */
router.post('/check-result', (req, res) => {
  try {
    const { trace_id } = req.body;

    if (!trace_id) {
      return res.status(400).json({
        code: -1,
        msg: '缺少trace_id参数'
      });
    }

    // 从Map中获取检测结果
    const result = checkResults.get(trace_id);

    if (result) {
      res.json({
        code: 0,
        data: {
          status: result.suggest === 'risky' ? 'risky' : 'pass',
          label: result.label,
          timestamp: result.timestamp
        },
        msg: 'success'
      });
    } else {
      // 如果没有找到结果，返回checking状态
      res.json({
        code: 0,
        data: {
          status: 'checking',
          label: null,
          timestamp: null
        },
        msg: 'checking'
      });
    }
  } catch (error) {
    console.error('查询检测结果出错:', error);
    res.status(500).json({
      code: -1,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});

/**
 * 批量查询内容安全检测结果
 * POST /api/batch-check-result
 */
router.post('/batch-check-result', (req, res) => {
  try {
    const { trace_ids } = req.body;

    if (!trace_ids || !Array.isArray(trace_ids) || trace_ids.length === 0) {
      return res.status(400).json({
        code: -1,
        msg: '缺少trace_ids参数或格式不正确'
      });
    }

    // 批量查询结果
    const results = {};

    trace_ids.forEach(trace_id => {
      const result = checkResults.get(trace_id);

      if (result) {
        results[trace_id] = {
          status: result.suggest === 'risky' ? 'risky' : 'pass',
          label: result.label,
          timestamp: result.timestamp
        };
      } else {
        results[trace_id] = {
          status: 'checking',
          label: null,
          timestamp: null
        };
      }
    });

    res.json({
      code: 0,
      data: results,
      msg: 'success'
    });
  } catch (error) {
    console.error('批量查询检测结果出错:', error);
    res.status(500).json({
      code: -1,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});

/**
 * 存储检测结果
 * @param {string} traceId 检测请求ID
 * @param {string} suggest 检测结果建议 (pass, risky)
 * @param {number} label 标签ID
 */
function storeCheckResult(traceId, suggest, label) {
  checkResults.set(traceId, {
    suggest,
    label,
    timestamp: Date.now()
  });

  // 设置结果过期时间（24小时后自动删除）
  setTimeout(() => {
    checkResults.delete(traceId);
  }, 24 * 60 * 60 * 1000);
}

// 导出路由和存储函数
module.exports = { router, storeCheckResult };