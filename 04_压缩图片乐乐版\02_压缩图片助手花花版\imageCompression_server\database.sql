-- 图像压缩小程序数据库初始化脚本
-- 适用于 MySQL 8.0.36+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS image_compression_service
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE image_compression_service;

-- 商户配置现在通过 miniapps/ 文件夹管理，不再需要 merchants 表

-- 1. 创建用户表
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信openid',
    app_id VARCHAR(64) NOT NULL COMMENT '小程序AppID',
    nickname VARCHAR(100) COMMENT '用户昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',

    UNIQUE KEY unique_user (openid, app_id),
    INDEX idx_openid (openid),
    INDEX idx_app_id (app_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户信息表';

-- 2. 创建商品配置表（按小程序AppID管理价格）
CREATE TABLE IF NOT EXISTS products (
    product_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    app_id VARCHAR(64) NOT NULL COMMENT '小程序AppID',
    product_code ENUM('day_card', 'permanent') NOT NULL COMMENT '商品代码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '价格（元）',
    duration_hours INT COMMENT '有效期小时数，永久为NULL',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY unique_app_product (app_id, product_code),
    INDEX idx_app_id (app_id),
    INDEX idx_product_code (product_code),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='商品配置表';

-- 3. 创建会员表
CREATE TABLE IF NOT EXISTS memberships (
    membership_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会员记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    membership_type ENUM('day_card', 'permanent') NOT NULL COMMENT '会员类型',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    expire_time TIMESTAMP NULL COMMENT '过期时间，永久为NULL',
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active' COMMENT '状态',
    order_id VARCHAR(32) COMMENT '关联订单ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_expire_time (expire_time),
    INDEX idx_membership_type (membership_type),
    INDEX idx_order_id (order_id)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户会员表';

-- 4. 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    order_id VARCHAR(32) PRIMARY KEY COMMENT '订单ID',
    user_id INT NOT NULL COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信openid',
    app_id VARCHAR(64) NOT NULL COMMENT '小程序AppID',
    product_code ENUM('day_card', 'permanent') NOT NULL COMMENT '商品代码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额（元）',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending' COMMENT '支付状态',
    payment_method VARCHAR(50) COMMENT '支付方式',
    transaction_id VARCHAR(100) COMMENT '微信支付交易号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
    paid_at TIMESTAMP NULL COMMENT '支付完成时间',
    expire_at TIMESTAMP NULL COMMENT '订单过期时间',

    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_app_id (app_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at),
    INDEX idx_paid_at (paid_at)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='订单表';

-- 插入默认商品配置（按AppID管理）
INSERT INTO products (app_id, product_code, product_name, description, price, duration_hours, sort_order) VALUES
-- 1. 图像压缩花花版
('wx_imagecomp_huahua_001', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('wx_imagecomp_huahua_001', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2),

-- 2. 压缩图片助手花花版
('wx_compress_assistant_huahua_002', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('wx_compress_assistant_huahua_002', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2),

-- 3. 图像压缩图片汪汪版
('wx_imagecomp_wangwang_003', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('wx_imagecomp_wangwang_003', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2),

-- 4. 图片压缩乐乐版
('wx_imagecomp_lele_004', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('wx_imagecomp_lele_004', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2);

-- 会员状态检查的存储过程
DELIMITER //
CREATE PROCEDURE CheckMembershipStatus(IN user_openid VARCHAR(64), IN user_app_id VARCHAR(64))
BEGIN
    SELECT
        u.user_id,
        u.openid,
        u.app_id,
        CASE
            WHEN m.membership_id IS NOT NULL THEN TRUE
            ELSE FALSE
        END as has_active_membership,
        m.membership_type,
        m.expire_time
    FROM users u
    LEFT JOIN memberships m ON u.user_id = m.user_id
        AND m.status = 'active'
        AND (m.expire_time IS NULL OR m.expire_time > NOW())
    WHERE u.openid = user_openid AND u.app_id = user_app_id
    ORDER BY m.created_at DESC
    LIMIT 1;
END //
DELIMITER ;

-- 创建检查和更新会员状态的存储过程
DELIMITER //
CREATE PROCEDURE UpdateMembershipStatus()
BEGIN
    -- 更新过期的会员记录
    UPDATE memberships
    SET status = 'expired'
    WHERE status = 'active'
    AND expire_time IS NOT NULL
    AND expire_time < NOW();

    SELECT ROW_COUNT() as expired_memberships;
END //
DELIMITER ;

-- 查看创建的表
SHOW TABLES;

-- 查看表结构
DESCRIBE merchants;
DESCRIBE users;
DESCRIBE products;
DESCRIBE memberships;
DESCRIBE orders;
