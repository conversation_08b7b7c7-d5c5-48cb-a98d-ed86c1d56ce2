/**
 * 全局错误监控和上报系统
 * 用于收集和上报小程序运行时错误
 */

const request = require('../api/request');
const apiConfig = require('../../config/apiConfig');

class ErrorReporter {
  constructor() {
    this.isEnabled = true;
    this.reportQueue = [];
    this.isReporting = false;
    this.maxQueueSize = 50;

    // 简单的防重复机制
    this.lastReportTime = {}; // 记录每种错误类型的最后上报时间
    this.reportCooldown = 5000; // 同类型错误上报冷却时间（毫秒）

    // 初始化错误监听
    this.initErrorListeners();

    // 定期清理过期标记
    setInterval(() => {
      this.cleanupExpiredMarks();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 初始化错误监听器
   */
  initErrorListeners() {
    // 监听小程序错误
    wx.onError((error) => {
      this.handleError('miniprogram_error', error, {
        type: 'runtime_error'
      });
    });

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      this.handleError('unhandled_rejection', res.reason, {
        type: 'promise_rejection',
        promise: res.promise
      });
    });

    // 监听内存不足警告
    wx.onMemoryWarning((res) => {
      this.handleError('memory_warning', `内存不足警告: ${res.level}`, {
        type: 'memory_warning',
        level: res.level
      });
    });

    console.log('✅ 全局错误监控已启动');
  }



  /**
   * 处理错误
   * @param {string} errorType 错误类型
   * @param {any} error 错误信息
   * @param {Object} context 上下文信息
   */
  handleError(errorType, error, context = {}) {
    if (!this.isEnabled) {
      return;
    }

    // 防止频繁上报：检查冷却时间
    const now = Date.now();
    const lastReportTime = this.lastReportTime[errorType] || 0;
    if (now - lastReportTime < this.reportCooldown) {
      console.warn(`⚠️ 错误上报冷却中，跳过: ${errorType}`);
      return;
    }

    console.error(`🚨 [${errorType}] 错误捕获:`, error);

    // 记录上报时间
    this.lastReportTime[errorType] = now;

    // 构建错误报告
    const errorReport = this.buildErrorReport(errorType, error, context);

    // 添加到上报队列
    this.addToQueue(errorReport);

    // 尝试上报
    this.processQueue();
  }

  /**
   * 构建错误报告
   * @param {string} errorType 错误类型
   * @param {any} error 错误信息
   * @param {Object} context 上下文信息
   * @returns {Object} 错误报告对象
   */
  buildErrorReport(errorType, error, context) {
    // 获取系统信息
    const systemInfo = this.getSystemInfo();
    
    // 获取用户信息
    const userInfo = this.getUserInfo();
    
    // 处理错误信息
    let errorMessage = '';
    let errorStack = '';
    
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack || '';
    } else if (error && error.errMsg) {
      errorMessage = error.errMsg;
    } else {
      errorMessage = JSON.stringify(error);
    }

    return {
      // 错误基本信息
      error_type: errorType,
      error_message: errorMessage,
      error_stack: errorStack,
      
      // 时间信息
      timestamp: new Date().toISOString(),
      local_time: new Date().toLocaleString('zh-CN'),
      
      // 系统信息
      system_info: systemInfo,
      
      // 用户信息
      user_info: userInfo,
      
      // 上下文信息
      context: context,
      
      // 小程序信息
      app_info: {
        version: this.getAppVersion(),
        scene: this.getLaunchScene(),
        path: this.getCurrentPath()
      }
    };
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        language: systemInfo.language,
        wechat_version: systemInfo.version,
        sdk_version: systemInfo.SDKVersion
      };
    } catch (e) {
      return { error: '获取系统信息失败' };
    }
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    try {
      const userToken = wx.getStorageSync('userToken');
      const userId = wx.getStorageSync('userId');
      
      return {
        user_id: userId || 'anonymous',
        has_token: !!userToken,
        is_logged_in: !!(userToken && userId)
      };
    } catch (e) {
      return { error: '获取用户信息失败' };
    }
  }

  /**
   * 获取应用版本
   */
  getAppVersion() {
    try {
      const accountInfo = wx.getAccountInfoSync();
      return accountInfo.miniProgram.version || 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /**
   * 获取启动场景
   */
  getLaunchScene() {
    try {
      const app = getApp();
      return app.globalData?.launchScene || 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /**
   * 获取当前页面路径
   */
  getCurrentPath() {
    try {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        return currentPage.route || 'unknown';
      }
      return 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /**
   * 添加到上报队列
   * @param {Object} errorReport 错误报告
   */
  addToQueue(errorReport) {
    // 如果队列满了，移除最旧的错误
    if (this.reportQueue.length >= this.maxQueueSize) {
      this.reportQueue.shift();
    }

    this.reportQueue.push({
      ...errorReport,
      created_at: Date.now()
    });
  }

  /**
   * 处理上报队列
   */
  async processQueue() {
    if (this.isReporting || this.reportQueue.length === 0) {
      return;
    }

    this.isReporting = true;

    while (this.reportQueue.length > 0) {
      const errorReport = this.reportQueue[0];

      try {
        await this.reportError(errorReport);
        // 上报成功，移除队列
        this.reportQueue.shift();
        console.log('✅ 错误上报成功');
      } catch (error) {
        console.error('❌ 错误上报失败:', error);
        // 上报失败直接移除，不重试
        this.reportQueue.shift();
        console.warn('⚠️ 错误上报失败，已丢弃');
      }
    }

    this.isReporting = false;
  }

  /**
   * 上报错误到服务器
   * @param {Object} errorReport 错误报告
   */
  async reportError(errorReport) {
    return request.request({
      url: apiConfig.errorReportUrl,
      method: 'POST',
      data: errorReport,
      showLoading: false,
      showError: false,
      needAuth: false, // 错误上报不需要认证，避免认证失败导致无法上报
      timeout: 5000,
      maxRetries: 0 // 错误上报不重试
    });
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 手动上报错误
   * @param {string} errorType 错误类型
   * @param {any} error 错误信息
   * @param {Object} context 上下文信息
   */
  reportManualError(errorType, error, context = {}) {
    this.handleError(errorType, error, {
      ...context,
      manual_report: true
    });
  }

  /**
   * 启用/禁用错误上报
   * @param {boolean} enabled 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`${enabled ? '✅' : '❌'} 错误上报已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      queue_length: this.reportQueue.length,
      is_reporting: this.isReporting,
      is_enabled: this.isEnabled
    };
  }

  /**
   * 清空队列
   */
  clearQueue() {
    this.reportQueue = [];
    console.log('🗑️ 错误上报队列已清空');
  }

  /**
   * 清理过期的上报标记（防止内存泄漏）
   */
  cleanupExpiredMarks() {
    const now = Date.now();
    const expireTime = 60000; // 1分钟过期时间

    // 清理过期的上报时间记录
    Object.keys(this.lastReportTime).forEach(errorType => {
      if (now - this.lastReportTime[errorType] > expireTime) {
        delete this.lastReportTime[errorType];
      }
    });
  }
}

// 创建全局实例
const errorReporter = new ErrorReporter();

module.exports = errorReporter;
