const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

// 导入配置和工具
const database = require('./config/database');
const cronJobs = require('./utils/cronJobs');

// 导入路由
const userRoutes = require('./routes/user');
const membershipRoutes = require('./routes/membership');
const orderRoutes = require('./routes/order');
const merchantRoutes = require('./routes/merchant');
const productsRoutes = require('./routes/products');
const paymentRoutes = require('./routes/payment');
const backupRoutes = require('./routes/backup');

const app = express();
const PORT = process.env.PORT || 8851;

// 中间件配置
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 路由配置
app.use('/api/user', userRoutes);
app.use('/api/membership', membershipRoutes);
app.use('/api/order', orderRoutes);
app.use('/api/merchant', merchantRoutes);
app.use('/api/products', productsRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/backup', backupRoutes);

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '图像压缩服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '图像压缩小程序后端服务',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      user: '/api/user',
      membership: '/api/membership',
      order: '/api/order',
      merchant: '/api/merchant',
      payment: '/api/payment'
    }
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在',
    path: req.originalUrl
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('全局错误:', error);
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
  });
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    await database.testConnection();
    console.log('✅ 数据库连接成功');

    // 初始化数据库表
    await database.initDatabase();
    console.log('✅ 数据库表检查完成');

    // 启动定时任务
    cronJobs.start();
    console.log('✅ 定时任务启动成功');

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`🚀 图像压缩服务器启动成功`);
      console.log(`📡 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 健康检查: http://localhost:${PORT}/health`);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  cronJobs.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  cronJobs.stop();
  process.exit(0);
});

// 启动服务器
startServer();

module.exports = app;
