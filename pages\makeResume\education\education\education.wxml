<view class="container">
  <!-- 教育经历列表 -->
  <view class="educationList" wx:if="{{educationFormData.length > 0}}">
    <view class="educationItem" 
          wx:for="{{educationFormData}}" 
          wx:key="index"
          bindlongpress="handleLongPress"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd"
          data-index="{{index}}"
          style="{{currentIndex === index ? 'background: #f5f5f5; transform: scale(1.02);' : ''}}">
      <view class="itemContent" bindtap="editEducation" data-index="{{index}}">
        <view class="mainInfo">
          <text class="school">{{item.school}}</text>
          <text class="subText">{{item.degree}} {{item.major}}</text>
        </view>
        <text class="dateText">{{item.startDate}} - {{item.endDate}}</text>
      </view>
      <view class="actionButtons">
        <view class="deleteBtn" catchtap="deleteEducation" data-index="{{index}}">
          <text class="deleteIcon">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="emptyState" wx:else>
    <text class="emptyText">暂无教育经历，点击下方按钮添加</text>
  </view>

  <!-- 添加按钮 -->
  <view class="addBtn" bindtap="addEducation">
    + 添加教育经历
  </view>
</view> 