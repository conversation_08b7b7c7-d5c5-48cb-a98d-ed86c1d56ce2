// pages/user/resumes/resumes.js
const resumeManager = require('../../../utils/resume/resumeManager');
const app = getApp();

Page({
  data: {
    resumeList: [],
    isLoading: false,
    isEmpty: false,
    currentResumeId: null
  },

  onLoad() {
    // 加载简历列表
    this.loadResumeList();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadResumeList();
  },

  // 加载简历列表
  loadResumeList() {
    this.setData({ isLoading: true });

    try {
      // 从本地存储获取简历列表
      const resumeList = resumeManager.getResumesList();
      const currentResumeId = resumeManager.getCurrentResumeId();

      // 格式化时间显示
      const formattedList = resumeList.map(resume => ({
        ...resume,
        createTimeText: this.formatTime(resume.createTime),
        updateTimeText: this.formatTime(resume.updateTime),
        isCurrent: resume.id === currentResumeId
      }));

      this.setData({
        resumeList: formattedList,
        isEmpty: formattedList.length === 0,
        currentResumeId: currentResumeId
      });

      // 记录用户行为
      // if (app.trackUserAction) {
      //   app.trackUserAction('view_resume_list');
      // }
    } catch (error) {
      console.error('加载简历列表失败:', error);
      wx.showToast({
        title: '加载简历列表失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
      wx.stopPullDownRefresh();
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    // 小于1天
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }

    // 超过7天显示具体日期
    return date.getFullYear() + '/' +
           (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
           date.getDate().toString().padStart(2, '0');
  },

  // 创建新简历
  createNewResume() {
    wx.showModal({
      title: '创建新简历',
      content: '请输入简历名称',
      editable: true,
      placeholderText: '例如：前端工程师简历',
      success: (res) => {
        if (res.confirm) {
          const title = res.content.trim() || '新建简历';

          // 创建新简历
          const resumeInfo = resumeManager.createResume(title);
          if (resumeInfo) {
            // 设置为当前简历
            resumeManager.setCurrentResumeId(resumeInfo.id);

            // 记录用户行为
            // if (app.trackUserAction) {
            //   app.trackUserAction('create_resume', { resumeId: resumeInfo.id, title: title });
            // }

            wx.showToast({
              title: '创建成功',
              icon: 'success'
            });

            // 刷新列表
            this.loadResumeList();

            // 跳转到编辑页面
            setTimeout(() => {
              wx.navigateTo({
                url: '/pages/makeResume/makeResume'
              });
            }, 1500);
          } else {
            wx.showToast({
              title: '创建失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 编辑简历
  editResume(e) {
    const resumeId = e.currentTarget.dataset.id;

    // 设置为当前简历
    resumeManager.setCurrentResumeId(resumeId);

    // 记录用户行为
    // if (app.trackUserAction) {
    //   app.trackUserAction('edit_resume', { resumeId: resumeId });
    // }

    // 刷新列表显示当前简历状态
    this.loadResumeList();

    // 跳转到简历编辑页面
    wx.navigateTo({
      url: '/pages/makeResume/makeResume'
    });
  },

  // 预览简历
  previewResume(e) {
    const resumeId = e.currentTarget.dataset.id;

    // 记录用户行为
    // if (app.trackUserAction) {
    //   app.trackUserAction('preview_resume', { resumeId: resumeId });
    // }

    // 获取简历数据
    const resumeData = resumeManager.getResumeData(resumeId);
    if (resumeData) {
      // 将数据转换为查询字符串
      const queryString = encodeURIComponent(JSON.stringify(resumeData));

      // 跳转到预览页面
      wx.navigateTo({
        url: `/pages/makeCreateResume/makeCreateResume?resumeData=${queryString}`
      });
    } else {
      wx.showToast({
        title: '简历数据不存在',
        icon: 'none'
      });
    }
  },

  // 删除简历
  deleteResume(e) {
    const resumeId = e.currentTarget.dataset.id;
    const resumeTitle = e.currentTarget.dataset.title;

    wx.showModal({
      title: '删除简历',
      content: `确定要删除"${resumeTitle}"吗？删除后无法恢复。`,
      success: (res) => {
        if (res.confirm) {
          const success = resumeManager.deleteResume(resumeId);

          if (success) {
            // 记录用户行为
            // if (app.trackUserAction) {
            //   app.trackUserAction('delete_resume', { resumeId: resumeId, title: resumeTitle });
            // }

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 重新加载列表
            this.loadResumeList();
          } else {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 重命名简历
  renameResume(e) {
    const resumeId = e.currentTarget.dataset.id;
    const currentTitle = e.currentTarget.dataset.title;

    wx.showModal({
      title: '重命名简历',
      content: '请输入新的简历名称',
      editable: true,
      placeholderText: currentTitle,
      success: (res) => {
        if (res.confirm) {
          const newTitle = res.content.trim();
          if (newTitle && newTitle !== currentTitle) {
            const success = resumeManager.renameResume(resumeId, newTitle);

            if (success) {
              // 记录用户行为
              // if (app.trackUserAction) {
              //   app.trackUserAction('rename_resume', {
              //     resumeId: resumeId,
              //     oldTitle: currentTitle,
              //     newTitle: newTitle
              //   });
              // }

              wx.showToast({
                title: '重命名成功',
                icon: 'success'
              });

              // 重新加载列表
              this.loadResumeList();
            } else {
              wx.showToast({
                title: '重命名失败',
                icon: 'none'
              });
            }
          }
        }
      }
    });
  },

  // 复制简历
  duplicateResume(e) {
    const resumeId = e.currentTarget.dataset.id;
    const originalTitle = e.currentTarget.dataset.title;

    wx.showModal({
      title: '复制简历',
      content: '请输入新简历的名称',
      editable: true,
      placeholderText: originalTitle + ' - 副本',
      success: (res) => {
        if (res.confirm) {
          const newTitle = res.content.trim() || (originalTitle + ' - 副本');

          const newResumeInfo = resumeManager.duplicateResume(resumeId, newTitle);

          if (newResumeInfo) {
            // 记录用户行为
            // if (app.trackUserAction) {
            //   app.trackUserAction('duplicate_resume', {
            //     originalId: resumeId,
            //     newId: newResumeInfo.id,
            //     originalTitle: originalTitle,
            //     newTitle: newTitle
            //   });
            // }

            wx.showToast({
              title: '复制成功',
              icon: 'success'
            });

            // 重新加载列表
            this.loadResumeList();
          } else {
            wx.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 显示更多操作
  showMoreActions(e) {
    const resumeId = e.currentTarget.dataset.id;
    const resumeTitle = e.currentTarget.dataset.title;

    wx.showActionSheet({
      itemList: ['重命名', '复制', '删除'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 重命名
            this.renameResume(e);
            break;
          case 1: // 复制
            this.duplicateResume(e);
            break;
          case 2: // 删除
            this.deleteResume(e);
            break;
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadResumeList();
  }
})
