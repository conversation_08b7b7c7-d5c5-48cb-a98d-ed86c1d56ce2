# 压缩图片助手花花版 - 广告方案

## 方案概述

本文档记录了压缩图片助手花花版小程序中实现的激励视频广告方案。该方案旨在平衡用户体验和应用收益，通过观看激励视频广告，用户可以获得更多的免费压缩机会。

## 广告机制

### 基本规则

1. **免费次数机制**：
   - 用户每天获得1次免费压缩机会
   - 观看一次激励视频广告后，当天可无限次压缩图片
   - 免费次数基于自然日（每天0点）重置，而非24小时计时

2. **广告触发时机**：
   - 用户第一次压缩图片时不显示广告
   - 当免费次数用完后，用户点击压缩按钮时，显示广告提示弹窗
   - 用户可以选择观看广告获取当天无限压缩权限，或稍后再说

3. **用户界面提示**：
   - 界面上显示剩余免费次数或"今日可无限压缩"状态，提高透明度
   - 当免费次数用完时，使用不同颜色提示用户

## 技术实现

### 核心组件

1. **广告计数器工具** (`utils/adCounter.js`)：
   - 管理用户的免费压缩次数
   - 提供获取、减少、增加和重置免费次数的方法
   - 实现基于自然日的免费次数重置机制

2. **广告初始化和显示**：
   - 在页面加载时初始化激励视频广告组件
   - 监听广告加载、错误和关闭事件
   - 用户完整观看广告后，奖励3次免费压缩机会

3. **压缩流程集成**：
   - 在压缩按钮点击事件中检查免费次数
   - 免费次数用完时，显示广告提示弹窗
   - 用户观看完广告后，需要手动点击"开始压缩"按钮继续压缩流程

### 广告提示弹窗

当用户免费次数用完时，显示模态弹窗：
- 标题：免费次数已用完
- 内容：观看一个短视频，今日可无限压缩
- 按钮：观看广告 / 稍后再说

## 代码示例

### 广告计数器工具

```javascript
// utils/adCounter.js
const FREE_COUNT_KEY = 'free_compression_count'; // 免费次数
const LAST_DATE_KEY = 'last_compression_date'; // 上次使用日期
const AD_WATCHED_KEY = 'ad_watched_today'; // 当天是否已观看广告

// 获取用户剩余免费次数
export function getFreeCount() {
  // 检查是否需要重置（新的一天）
  checkAndResetDaily();

  // 如果当天已观看过广告，则返回一个较大的数值表示"无限"使用
  if (hasWatchedAdToday()) {
    return 999; // 返回一个大数值表示当天无限使用
  }

  // 获取当前免费次数
  const freeCount = uni.getStorageSync(FREE_COUNT_KEY) || 0;
  return freeCount;
}

// 减少免费次数
export function decreaseFreeCount() {
  // 先检查是否需要重置
  checkAndResetDaily();

  // 如果当天已观看过广告，则不减少次数
  if (hasWatchedAdToday()) {
    return 999; // 返回一个大数值表示当天无限使用
  }

  // 获取当前免费次数
  let freeCount = uni.getStorageSync(FREE_COUNT_KEY) || 0;

  // 如果有免费次数，则减少
  if (freeCount > 0) {
    freeCount--;
    uni.setStorageSync(FREE_COUNT_KEY, freeCount);
  }

  return freeCount;
}

// 增加免费次数（看完广告后）
export function increaseFreeCount(count = 3) {
  // 标记当天已观看广告
  markAdWatchedToday();

  // 获取当前免费次数
  let freeCount = uni.getStorageSync(FREE_COUNT_KEY) || 0;

  // 增加免费次数（虽然已经无限使用了，但仍保留这个逻辑以兼容旧代码）
  freeCount += count;
  uni.setStorageSync(FREE_COUNT_KEY, freeCount);

  return 999; // 返回一个大数值表示当天无限使用
}

// 标记当天已观看广告
export function markAdWatchedToday() {
  const today = getTodayDateString();
  uni.setStorageSync(AD_WATCHED_KEY, today);
}

// 检查当天是否已观看广告
export function hasWatchedAdToday() {
  const today = getTodayDateString();
  const adWatchedDate = uni.getStorageSync(AD_WATCHED_KEY) || '';
  return today === adWatchedDate;
}

// 检查并根据日期重置免费次数
function checkAndResetDaily() {
  const today = getTodayDateString();
  const lastDate = uni.getStorageSync(LAST_DATE_KEY) || '';

  // 如果日期不同，说明是新的一天
  if (today !== lastDate) {
    resetFreeCount(1); // 重置为1次免费机会
    uni.removeStorageSync(AD_WATCHED_KEY); // 重置广告观看状态
  }
}
```

### 广告初始化

```javascript
// 初始化激励视频广告
initRewardedVideoAd() {
  // 仅在支持的环境下初始化广告
  if (wx.createRewardedVideoAd) {
    this.videoAd = wx.createRewardedVideoAd({
      adUnitId: 'adunit-93daac9babe5854e'
    });

    // 监听加载事件
    this.videoAd.onLoad(() => {
      console.log('激励视频广告加载成功');
    });

    // 监听错误事件
    this.videoAd.onError((err) => {
      console.error('激励视频广告加载失败', err);
    });

    // 监听关闭事件
    this.videoAd.onClose((res) => {
      // 用户完整观看广告
      if (res && res.isEnded) {
        // 标记用户当天已观看广告，可以无限使用
        const newCount = increaseFreeCount(3); // 这个函数内部会调用 markAdWatchedToday()
        this.freeCount = newCount;

        uni.showToast({
          title: '今日可无限压缩，请点击开始压缩',
          icon: 'success',
          duration: 2000
        });

        // 不再自动触发压缩，让用户手动点击按钮
        // 移除了自动调用 continueCompression() 的代码
      } else {
        // 用户提前关闭广告，不给予奖励
        uni.showToast({
          title: '需完整观看广告才能获得奖励',
          icon: 'none'
        });
      }
    });
  }
}
```

### 广告提示弹窗

```javascript
// 显示广告提示弹窗
showAdPrompt() {
  uni.showModal({
    title: '免费次数已用完',
    content: '观看一个短视频，今日可无限压缩',
    confirmText: '观看视频',
    cancelText: '稍后再说',
    success: (res) => {
      if (res.confirm) {
        // 用户点击"观看视频"
        this.showRewardedVideoAd();
      }
      // 用户点击"稍后再说"，不做任何操作
    }
  });
}
```

## 注意事项

1. **广告单元ID**：
   - 当前使用的广告单元ID为：`adunit-93daac9babe5854e`
   - 在正式环境中需要替换为您在微信小程序后台申请的正式广告单元ID

2. **测试环境**：
   - 在开发者工具中可能无法正常预览广告，需要在真机上测试
   - 可能需要切换开发者工具中的基础库版本

3. **用户体验优化**：
   - 确保广告加载失败时有适当的错误处理
   - 考虑在用户即将用完免费次数时给予提示
   - 可以考虑在特定场景（如首次使用）提供额外的免费次数

## 最近更新

1. **2024年更新**：
   - 修改了广告奖励机制，用户观看完广告后当天可无限次压缩图片
   - 更新了界面提示，当用户已观看广告时显示"今日可无限压缩"
   - 更新了广告提示弹窗文本，明确告知用户观看广告后当天可无限压缩
   - 这一改动提升了用户体验，减少了用户需要多次观看广告的情况

2. **2023年更新**：
   - 修改了广告观看后的行为，用户观看完广告后不再自动触发压缩
   - 用户需要手动点击"开始压缩"按钮来继续压缩流程
   - 更新了提示文本，明确告知用户需要点击按钮继续操作
   - 这一改动解决了用户观看广告后自动压缩可能导致的免费次数计算问题


