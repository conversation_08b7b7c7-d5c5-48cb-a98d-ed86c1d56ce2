// index.js
const membershipManager = require('../../utils/user/membershipManager');
const shareHelper = require('../../utils/share.js');

Page({
  data: {
    title: "欢迎使用小程序    haha",
    description: "这是一个测试页面  page",
  },

  onLoad() {

    // 等待登录完成后再查询会员状态
    this.waitForLoginAndQueryMemberStatus();
  },

  /**
   * 等待登录完成后查询会员状态
   */
  async waitForLoginAndQueryMemberStatus() {
    try {
      // 等待登录完成
      await this.waitForLogin();

      // 查询会员状态
      const membershipInfo = await membershipManager.queryMemberStatus(false);

    } catch (err) {
      console.error('主页会员状态查询失败:', err);
    }
  },

  /**
   * 等待登录完成
   */
  waitForLogin() {
    return new Promise((resolve) => {
      const checkLogin = () => {
        const app = getApp();
        if (app && app.globalData && app.globalData.userToken) {
          // 登录完成
          resolve();
        } else {
          // 继续等待，100ms后再检查
          setTimeout(checkLogin, 100);
        }
      };
      checkLogin();
    });
  },

  // 跳转到制作简历页面
  toMakeResume() {
    wx.navigateTo({
      url: '/pages/makeResume/makeResume'
    })
  },

  // 跳转到简历样式页面
  toResumeStyle() {
    wx.navigateTo({
      url: '/pages/resumeStyle/resumeStyle'
    })
  },

  // 跳转到证件照制作页面
  toIDPhoto() {
    wx.navigateTo({
      url: '/pages/idPhoto/idPhoto'
    })
  },

  // 跳转到免费模板页面
  toWordTemplate() {
    wx.navigateTo({
      url: '/pages/freeResume/index'
    })
  },

  /**
   * 页面分享 - 分享到好友
   */
  onShareAppMessage() {
    return shareHelper.getShareConfig({
      title: '个人简历模板简历制作',
      path: '/pages/index/index',
    });
  },

  /**
   * 页面分享 - 分享到朋友圈
   */
  onShareTimeline() {
    return shareHelper.getTimelineShareConfig({
      title: '个人简历模板简历制作',
    });
  }
})
