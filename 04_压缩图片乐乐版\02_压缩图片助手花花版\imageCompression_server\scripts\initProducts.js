#!/usr/bin/env node

/**
 * 商品数据初始化脚本
 * 为小程序初始化默认商品价格
 */

// 加载环境变量
require('dotenv').config();

const { query } = require('../config/database');
const AppManager = require('../services/MerchantManager');

/**
 * 为指定小程序初始化商品
 * @param {string} appId 小程序AppID
 * @param {Object} products 商品配置
 */
async function initProductsForApp(appId, products = null) {
  try {
    console.log(`🔄 正在为小程序 ${appId} 初始化商品...`);

    // 检查是否已存在商品
    const existingProducts = await query(
      'SELECT COUNT(*) as count FROM products WHERE app_id = ?',
      [appId]
    );

    if (existingProducts[0].count > 0) {
      console.log(`⚠️  小程序 ${appId} 已存在商品配置，跳过初始化`);
      return;
    }

    // 使用默认商品配置
    const defaultProducts = products || [
      {
        product_code: 'day_card',
        product_name: '日卡会员',
        description: '24小时内无限制压缩图片',
        price: 4.99,
        duration_hours: 24,
        sort_order: 1
      },
      {
        product_code: 'permanent',
        product_name: '永久会员',
        description: '永久无限制压缩图片',
        price: 19.99,
        duration_hours: null,
        sort_order: 2
      }
    ];

    // 插入商品
    for (const product of defaultProducts) {
      await query(
        `INSERT INTO products (app_id, product_code, product_name, description, price, duration_hours, sort_order)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          appId,
          product.product_code,
          product.product_name,
          product.description,
          product.price,
          product.duration_hours,
          product.sort_order
        ]
      );
      
      console.log(`✅ 添加商品: ${product.product_name} - ¥${product.price}`);
    }

    console.log(`🎉 小程序 ${appId} 商品初始化完成！`);

  } catch (error) {
    console.error(`❌ 初始化小程序 ${appId} 商品失败:`, error.message);
    throw error;
  }
}

/**
 * 为所有配置的小程序初始化商品
 */
async function initAllProducts() {
  try {
    console.log('🚀 开始为所有小程序初始化商品...\n');

    const allApps = AppManager.getAllApps(true); // 只获取激活的小程序
    
    if (allApps.length === 0) {
      console.log('⚠️  没有找到激活的小程序配置');
      return;
    }

    let successCount = 0;
    let skipCount = 0;

    for (const app of allApps) {
      try {
        const existingProducts = await query(
          'SELECT COUNT(*) as count FROM products WHERE app_id = ?',
          [app.appId]
        );

        if (existingProducts[0].count > 0) {
          console.log(`⏭️  ${app.businessName} (${app.appId}) - 已存在商品，跳过`);
          skipCount++;
          continue;
        }

        await initProductsForApp(app.appId);
        successCount++;
        console.log('');

      } catch (error) {
        console.error(`❌ 处理 ${app.businessName} 失败:`, error.message);
      }
    }

    console.log('📊 初始化汇总:');
    console.log(`   ✅ 成功初始化: ${successCount} 个小程序`);
    console.log(`   ⏭️  跳过: ${skipCount} 个小程序`);
    console.log(`   📱 总计: ${allApps.length} 个小程序`);

  } catch (error) {
    console.error('❌ 批量初始化失败:', error);
    process.exit(1);
  }
}

/**
 * 显示所有小程序的商品配置
 */
async function showAllProducts() {
  try {
    console.log('📋 所有小程序商品配置');
    console.log('========================\n');

    const allProducts = await query(
      `SELECT app_id, product_code, product_name, price, duration_hours, is_active
       FROM products 
       ORDER BY app_id, sort_order`
    );

    if (allProducts.length === 0) {
      console.log('⚠️  数据库中没有商品配置');
      return;
    }

    let currentAppId = '';
    allProducts.forEach(product => {
      if (product.app_id !== currentAppId) {
        currentAppId = product.app_id;
        console.log(`\n📱 ${product.app_id}:`);
      }
      
      const status = product.is_active ? '🟢' : '🔴';
      const duration = product.duration_hours ? `${product.duration_hours}小时` : '永久';
      
      console.log(`   ${status} ${product.product_name} - ¥${product.price} (${duration})`);
    });

  } catch (error) {
    console.error('❌ 获取商品配置失败:', error);
    process.exit(1);
  }
}

/**
 * 主函数
 */
async function main() {
  const command = process.argv[2];
  const appId = process.argv[3];

  try {
    switch (command) {
      case 'init':
        if (appId) {
          await initProductsForApp(appId);
        } else {
          await initAllProducts();
        }
        break;
        
      case 'show':
        await showAllProducts();
        break;
        
      default:
        console.log('📖 商品初始化脚本使用说明:');
        console.log('');
        console.log('命令:');
        console.log('  node scripts/initProducts.js init          # 为所有小程序初始化商品');
        console.log('  node scripts/initProducts.js init <appId>  # 为指定小程序初始化商品');
        console.log('  node scripts/initProducts.js show          # 显示所有商品配置');
        console.log('');
        console.log('示例:');
        console.log('  node scripts/initProducts.js init wx123456789');
        console.log('  node scripts/initProducts.js show');
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  initProductsForApp,
  initAllProducts,
  showAllProducts
};
