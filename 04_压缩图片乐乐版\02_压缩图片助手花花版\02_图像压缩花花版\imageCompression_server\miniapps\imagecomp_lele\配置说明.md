# 图片压缩乐乐版配置说明

## 📋 配置概览

- **小程序名称**: 图片压缩乐乐版
- **AppID**: `wx_imagecomp_lele_004`
- **文件夹**: `imagecomp_lele/`
- **状态**: 待配置（isActive: false）

## 🔧 需要完成的配置步骤

### 1. 微信小程序配置
在 `config.json` 中更新以下信息：

```json
{
  "appid": "实际的微信小程序AppID",
  "wechat_config": {
    "app_id": "实际的微信小程序AppID",
    "app_secret": "实际的微信小程序AppSecret"
  }
}
```

### 2. 微信支付配置
在 `config.json` 中更新以下信息：

```json
{
  "mchid": "实际的微信商户号",
  "api_v3_key": "实际的API_V3密钥（32位字符）",
  "merchant_serial": "实际的商户证书序列号",
  "public_key_id": "实际的公钥ID"
}
```

### 3. 证书文件
需要添加以下证书文件到当前目录：

- `apiclient_key.pem` - 商户API私钥
- `pub_key.pem` - 微信支付公钥

### 4. 数据库配置
执行SQL脚本添加商品配置：

```bash
# 在数据库中执行
mysql -u root -p image_compression_service < scripts/add_lele_products.sql
```

或者手动执行：

```sql
INSERT INTO products (app_id, product_code, product_name, description, price, duration_hours, sort_order) VALUES
('wx_imagecomp_lele_004', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('wx_imagecomp_lele_004', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2);
```

### 5. 激活配置
配置完成后，将 `config.json` 中的 `isActive` 设置为 `true`：

```json
{
  "isActive": true
}
```

### 6. 重启服务
```bash
pm2 restart image-compression-server
```

## ✅ 配置验证

### 检查配置是否生效
```bash
# 检查小程序配置
curl "https://image-compression.gbw8848.cn/api/merchant/check/wx_imagecomp_lele_004"

# 检查商品配置
curl "https://image-compression.gbw8848.cn/api/membership/products?appId=wx_imagecomp_lele_004"
```

### 预期返回结果
```json
{
  "success": true,
  "data": {
    "appid": "wx_imagecomp_lele_004",
    "isSupported": true,
    "isActive": true,
    "businessName": "图片压缩乐乐版"
  }
}
```

## 🚨 注意事项

1. **AppID更新**: 如果实际的微信小程序AppID不是 `wx_imagecomp_lele_004`，需要同时更新：
   - `config.json` 中的 `appid` 和 `wechat_config.app_id`
   - 数据库中的商品记录的 `app_id` 字段
   - 前端小程序代码中的AppID配置

2. **证书安全**: 
   - 证书文件权限设置为 600
   - 不要将证书文件提交到版本控制

3. **测试建议**:
   - 先在测试环境验证配置
   - 确认支付功能正常后再激活

## 📞 技术支持

如有配置问题，请联系技术负责人。
