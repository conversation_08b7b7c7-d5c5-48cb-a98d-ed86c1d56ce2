<view class="container">
  <!-- 项目经历列表 -->
  <view class="projectList" wx:if="{{projectFormData.length > 0}}">
    <view class="projectItem" 
          wx:for="{{projectFormData}}" 
          wx:key="index"
          bindlongpress="handleLongPress"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd"
          data-index="{{index}}"
          style="{{currentIndex === index ? 'background: #f5f5f5; transform: scale(1.02);' : ''}}">
      <view class="itemContent" bindtap="editProject" data-index="{{index}}">
        <view class="mainInfo">
          <text class="projectName">{{item.projectName}}</text>
          <text class="subText">{{item.role}}</text>
        </view>
        <text class="dateText">{{item.startDate}} - {{item.endDate}}</text>
      </view>
      <view class="actionButtons">
        <view class="deleteBtn" catchtap="deleteProject" data-index="{{index}}">
          <text class="deleteIcon">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="emptyState" wx:else>
    <text class="emptyText">暂无项目经历，点击下方按钮添加</text>
  </view>

  <!-- 添加按钮 -->
  <view class="addBtn" bindtap="addProject">
    + 添加项目经历
  </view>
</view> 