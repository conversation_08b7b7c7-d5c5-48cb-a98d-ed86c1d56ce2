#!/usr/bin/env node

/**
 * 数据库备份系统设置脚本
 * 用于初始化和测试数据库备份功能
 */

const DatabaseBackupService = require('../services/DatabaseBackupService');
const cronJobs = require('../utils/cronJobs');

async function setupBackup() {
  console.log('🚀 开始设置数据库备份系统...\n');

  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...');
    const { testConnection } = require('../config/database');
    await testConnection();
    console.log('✅ 数据库连接正常\n');

    // 2. 测试COS连接
    console.log('2️⃣ 测试腾讯云COS连接...');
    const backupService = new DatabaseBackupService();
    const cosConnected = await backupService.testCOSConnection();
    
    if (cosConnected) {
      console.log('✅ COS连接正常\n');
    } else {
      console.log('❌ COS连接失败，请检查配置\n');
      return;
    }

    // 3. 获取数据库统计信息
    console.log('3️⃣ 获取数据库统计信息...');
    const stats = await backupService.getBackupStats();
    if (stats) {
      console.log(`📊 数据库大小: ${stats.databaseSize} MB`);
      console.log(`📊 表数量: ${stats.tableCount} 个\n`);
    }

    // 4. 询问是否执行测试备份
    console.log('4️⃣ 是否执行测试备份？');
    console.log('⚠️  注意：这将创建一个完整的数据库备份并上传到COS');
    
    // 在生产环境中，你可以取消注释下面的代码来执行测试备份
    /*
    console.log('🚀 开始执行测试备份...');
    const result = await backupService.createDatabaseBackup();
    console.log('✅ 测试备份完成:', result);
    */
    
    console.log('⏭️  跳过测试备份（如需测试，请手动调用 /api/backup/execute）\n');

    // 5. 显示定时任务信息
    console.log('5️⃣ 定时任务配置信息:');
    console.log('📅 数据库备份: 每天凌晨3点执行 (0 3 * * *)');
    console.log('📅 会员状态检查: 每小时30分执行 (30 * * * *)');
    console.log('📅 过期订单清理: 每小时执行 (0 * * * *)');
    console.log('📅 旧数据清理: 每周日凌晨3点执行 (0 3 * * 0)\n');

    // 6. 显示API接口信息
    console.log('6️⃣ 可用的备份管理API:');
    console.log('🔗 POST /api/backup/execute - 手动执行备份');
    console.log('🔗 GET /api/backup/test-cos - 测试COS连接');
    console.log('🔗 GET /api/backup/stats - 获取备份统计');
    console.log('🔗 GET /api/backup/cron-status - 获取定时任务状态');
    console.log('🔗 GET /api/backup/system-stats - 获取系统统计\n');

    console.log('✅ 数据库备份系统设置完成！');
    console.log('💡 提示：服务启动后，定时任务将自动运行');

  } catch (error) {
    console.error('❌ 设置过程中出现错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupBackup().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('设置失败:', error);
    process.exit(1);
  });
}

module.exports = { setupBackup };
