/**
 * 配置文件
 * 压缩图片助手小程序配置
 */
module.exports = {
  // 微信配置
  wechat: {
    token: 'mediaCheckToken', // 微信服务器验证Token
    // 支持多个小程序配置
    apps: [
      {
        appId: 'wx76b4aebe290bf455', // 压缩图片助手小程序AppID
        appSecret: 'b91071d1fbe511489df25e3ed8940f10', // 压缩图片助手小程序AppSecret
        name: '压缩图片助手花花版' // 小程序名称（便于日志识别）
      },
      {
        appId: 'wx5f0b9fdfea4946ab', // 新增的小程序AppID
        appSecret: 'c7a6c791d92cd55deeea729f2a42646a', // 新增的小程序AppSecret
        name: '图像压缩图片汪汪版' // 小程序名称（便于日志识别）
      },
      {
        appId: 'wxdccd0d11f4fc6e93', // 图像压缩花花版小程序AppID
        appSecret: 'bdbd290586231cb3c4bc9a26b02e8718', // 图像压缩花花版小程序AppSecret
        name: '图像压缩花花版' // 小程序名称（便于日志识别）
      }
      // 可以添加更多小程序配置
    ],
    // 保留默认配置，用于向后兼容和微信服务器验证
    appId: 'wx76b4aebe290bf455',
    appSecret: 'b91071d1fbe511489df25e3ed8940f10'
  },

  // 服务器配置
  server: {
    port: process.env.PORT || 8848,
    host: process.env.HOST || 'localhost'
  },

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info'
  }
};