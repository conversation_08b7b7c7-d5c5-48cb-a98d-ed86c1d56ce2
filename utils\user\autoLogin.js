/**
 * 自动登录工具类
 * 用于在小程序启动时自动执行微信登录流程
 */
const userApi = require('../api/userApi');
const membershipManager = require('./membershipManager');
const tokenManager = require('../auth/tokenManager');

// 获取错误上报器（延迟加载避免循环依赖）
function getErrorReporter() {
  try {
    return require('../error/errorReporter');
  } catch (e) {
    console.warn('错误上报器未找到:', e);
    return null;
  }
}

/**
 * 执行自动登录
 * @returns {Promise<boolean>} 登录是否成功
 */
function performAutoLogin() {
  return new Promise((resolve, reject) => {
    console.log('开始执行自动登录...');

    // 检查是否已有有效的token
    const existingToken = wx.getStorageSync('userToken');
    const existingUserId = wx.getStorageSync('userId');

    if (existingToken && existingUserId) {
      console.log('检测到已有登录信息，验证token有效性...');
      // 验证现有token是否有效
      validateExistingToken(existingToken)
        .then((isValid) => {
          if (isValid) {
            console.log('现有token有效，更新全局状态...');

            // 更新全局状态
            const app = getApp();
            if (app) {
              app.globalData.userToken = existingToken;
              app.globalData.userId = existingUserId;
              app.globalData.hasUserInfo = true;

              // 如果有用户信息，也更新到全局状态
              const userInfo = wx.getStorageSync('userInfo');
              if (userInfo) {
                app.globalData.userInfo = userInfo;
                if (userInfo.hasOwnProperty('is_member')) {
                  app.globalData.isMember = userInfo.is_member;
                }
              }

              console.log('全局状态已更新:', app.globalData);
            }

            console.log('现有token有效，自动登录成功');
            resolve(true);
          } else {
            console.log('现有token无效，重新登录...');
            executeWxLogin().then(resolve).catch(reject);
          }
        })
        .catch(() => {
          console.log('token验证失败，重新登录...');
          executeWxLogin().then(resolve).catch(reject);
        });
    } else {
      console.log('未检测到登录信息，执行微信登录...');
      executeWxLogin().then(resolve).catch(reject);
    }
  });
}

/**
 * 验证现有token是否有效
 * @param {string} token - 用户token
 * @returns {Promise<boolean>} token是否有效
 */
function validateExistingToken(token) {
  return userApi.validateToken()
    .then(() => true)
    .catch(() => false);
}

/**
 * 执行微信登录流程
 * @returns {Promise<boolean>} 登录是否成功
 */
function executeWxLogin() {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('获取微信登录code成功:', loginRes.code);

          // 使用API模块发送登录请求
          userApi.login(loginRes.code)
            .then((res) => {
              console.log('自动登录成功:', res);

              // 根据服务端API文档格式解析响应
              const { access_token, user_info, expires_in, token_type } = res;
              const userId = user_info?.id || null;

              // 使用tokenManager保存token信息
              tokenManager.saveTokenInfo({
                access_token,
                expires_in,
                token_type
              });

              // 保存用户ID和用户信息
              wx.setStorageSync('userId', userId);
              if (user_info) {
                wx.setStorageSync('userInfo', user_info);

                // 如果用户信息中包含会员状态，也保存到会员信息中
                if (user_info.hasOwnProperty('is_member')) {
                  const membershipInfo = {
                    isMember: user_info.is_member,
                    lastUpdated: Date.now()
                  };
                  wx.setStorageSync('membershipInfo', membershipInfo);
                }
              }

              console.log('in auto login, 保存登录信息后, 全局用户数据:', access_token, userId, user_info);

              // 更新全局状态
              const app = getApp();
              if (app) {
                app.globalData.userToken = access_token;
                app.globalData.userId = userId;
                app.globalData.hasUserInfo = true;
                if (user_info) {
                  app.globalData.userInfo = user_info;
                  // 如果有会员状态信息，更新全局会员状态
                  if (user_info.hasOwnProperty('is_member')) {
                    app.globalData.isMember = user_info.is_member;
                  }
                }
              }

              console.log('after auto login, 全局用户数据:', app.globalData);

              // 登录成功后查询会员状态
              membershipManager.queryMemberStatusAfterLogin(true);

              resolve(true);
            })
            .catch((err) => {
              console.error('自动登录失败:', err);

              // 上报自动登录失败错误
              const errorReporter = getErrorReporter();
              if (errorReporter) {
                errorReporter.reportManualError('auto_login_failed', err, {
                  action: 'executeWxLogin',
                  wx_code: loginRes.code,
                  is_network_error: err.isNetworkError,
                  error_message: err.message
                });
              }

              // 如果是网络连接错误，提供更友好的提示
              if (err.isNetworkError) {
                wx.showToast({
                  title: '网络连接失败，请检查网络后重试',
                  icon: 'none',
                  duration: 3000
                });
              }

              resolve(false);
            });
        } else {
          console.error('获取微信登录code失败:', loginRes);
          resolve(false);
        }
      },
      fail: (err) => {
        console.error('wx.login调用失败:', err);

        // 上报微信登录调用失败错误
        const errorReporter = getErrorReporter();
        if (errorReporter) {
          errorReporter.reportManualError('wx_login_failed', err, {
            action: 'executeWxLogin',
            step: 'wx_login_call_failed',
            error_message: err.errMsg || err.message
          });
        }

        resolve(false);
      }
    });
  });
}

/**
 * 静默登录（不显示任何提示）
 * @returns {Promise<boolean>} 登录是否成功
 */
function silentLogin() {
  return performAutoLogin();
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
function checkLoginStatus() {
  const token = wx.getStorageSync('userToken');
  const userId = wx.getStorageSync('userId');
  return !!(token && userId);
}

/**
 * 获取用户ID
 * @returns {string|null} 用户ID
 */
function getUserId() {
  return wx.getStorageSync('userId') || null;
}

/**
 * 获取用户Token
 * @returns {string|null} 用户Token
 */
function getUserToken() {
  return wx.getStorageSync('userToken') || null;
}



module.exports = {
  performAutoLogin,
  silentLogin,
  checkLoginStatus,
  getUserId,
  getUserToken,
  validateExistingToken
};
