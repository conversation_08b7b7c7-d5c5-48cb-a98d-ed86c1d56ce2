/**
 * 简化的简历数据格式转换工具
 * 专门用于转换为服务端期望的格式
 */

/**
 * 将makeResume页面格式转换为服务端期望格式
 * @param {Object} pageData - makeResume页面格式的数据
 * @returns {Object} 服务端期望格式的数据
 */
function convertToServerFormat(pageData) {
  console.log('=== 数据格式转换：页面格式 → 服务端格式 ===');
  console.log('输入数据:', pageData);

  const serverData = {
    moduleOrders: pageData.moduleOrders || {},
    basicInfo: validateBasicInfo(pageData.basicInfo || {}),
    jobIntention: validateJobIntention(pageData.jobIntention || {}),
    education: validateEducationList(pageData.education || []),
    school: validateSchoolList(pageData.school || []),
    internship: validateInternshipList(pageData.internship || []),
    work: validateWorkList(pageData.work || []),
    project: validateProjectList(pageData.project || []),
    skills: validateSkillsList(pageData.skills || []),
    awards: validateAwardsList(pageData.awards || []),
    interests: validateInterestsList(pageData.interests || []),
    evaluation: validateEvaluationList(pageData.evaluation || []),
    custom1: validateCustomList(pageData.custom1 || []),
    custom2: validateCustomList(pageData.custom2 || []),
    custom3: validateCustomList(pageData.custom3 || [])
  };

  console.log('转换后数据:', serverData);
  return serverData;
}

// 验证基本信息
function validateBasicInfo(basicInfo) {
  return {
    name: basicInfo.name || '',
    gender: basicInfo.gender || '',
    phone: basicInfo.phone || '',
    photoUrl: basicInfo.photoUrl || '',
    city: basicInfo.city || '',
    email: basicInfo.email || '',
    wechat: basicInfo.wechat || '',
    age: basicInfo.age || '',
    birthday: basicInfo.birthday || '',
    marriage: basicInfo.marriage || '',
    politics: basicInfo.politics || '',
    nation: basicInfo.nation || '',
    hometown: basicInfo.hometown || '',
    height: basicInfo.height || '',
    weight: basicInfo.weight || '',
    educationLevel: basicInfo.educationLevel || '',
    customTitle1: basicInfo.customTitle1 || '',
    customContent1: basicInfo.customContent1 || '',
    customTitle2: basicInfo.customTitle2 || '',
    customContent2: basicInfo.customContent2 || ''
  };
}

// 验证求职意向
function validateJobIntention(jobIntention) {
  return {
    position: jobIntention.position || '',
    salary: jobIntention.salary || '',
    city: jobIntention.city || '',
    status: jobIntention.status || ''
  };
}

// 验证教育经历列表
function validateEducationList(educationList) {
  if (!Array.isArray(educationList)) return [];
  
  return educationList.map(item => ({
    school: item.school || '',
    major: item.major || '',
    degree: item.degree || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    description: item.description || ''
  }));
}

// 验证在校经历列表
function validateSchoolList(schoolList) {
  if (!Array.isArray(schoolList)) return [];
  
  return schoolList.map(item => ({
    role: item.role || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    content: item.content || ''
  }));
}

// 验证实习经历列表
function validateInternshipList(internshipList) {
  if (!Array.isArray(internshipList)) return [];
  
  return internshipList.map(item => ({
    company: item.company || '',
    position: item.position || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    content: item.content || ''
  }));
}

// 验证工作经历列表
function validateWorkList(workList) {
  if (!Array.isArray(workList)) return [];
  
  return workList.map(item => ({
    company: item.company || '',
    position: item.position || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    description: item.description || ''
  }));
}

// 验证项目经历列表
function validateProjectList(projectList) {
  if (!Array.isArray(projectList)) return [];
  
  return projectList.map(item => ({
    projectName: item.projectName || '',
    role: item.role || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    description: item.description || ''
  }));
}

// 验证技能特长列表
function validateSkillsList(skillsList) {
  if (!Array.isArray(skillsList)) return [];
  
  return skillsList.filter(skill => 
    typeof skill === 'string' && skill.trim() !== ''
  );
}

// 验证奖项证书列表
function validateAwardsList(awardsList) {
  if (!Array.isArray(awardsList)) return [];
  
  return awardsList.filter(award => 
    typeof award === 'string' && award.trim() !== ''
  );
}

// 验证兴趣爱好列表
function validateInterestsList(interestsList) {
  if (!Array.isArray(interestsList)) return [];
  
  return interestsList.filter(interest => 
    typeof interest === 'string' && interest.trim() !== ''
  );
}

// 验证自我评价列表
function validateEvaluationList(evaluationList) {
  if (!Array.isArray(evaluationList)) return [];
  
  return evaluationList.map(item => {
    if (typeof item === 'string') {
      return { content: item };
    }
    return {
      content: item.content || '',
    };
  });
}

// 验证自定义模块列表
function validateCustomList(customList) {
  if (!Array.isArray(customList)) return [];
  
  return customList.map(item => ({
    customName: item.customName || '',
    role: item.role || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    content: item.content || ''
  }));
}

module.exports = {
  convertToServerFormat
};
