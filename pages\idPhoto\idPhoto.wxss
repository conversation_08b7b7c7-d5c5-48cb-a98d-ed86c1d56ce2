.container {
  padding: 40rpx 20rpx 20rpx 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}



/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 尺寸选择区域 */
.size-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.size-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.size-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}


.size-item.selected {
  background: #4B8BF5;
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(75, 139, 245, 0.25);
}

.size-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}

.size-desc {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
  position: relative;
  z-index: 1;
}

.size-mm {
  font-size: 22rpx;
  opacity: 0.7;
  margin-bottom: 4rpx;
  position: relative;
  z-index: 1;
}



/* 处理状态 */
.processing-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.processing-spinner {
  width: 50rpx;
  height: 50rpx;
  border: 3rpx solid #e0e0e0;
  border-top: 3rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.processing-text {
  font-size: 28rpx;
  color: #666;
}

/* 使用说明 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tips-title {
  font-size: 25rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 25rpx;
  color: #666;
  line-height: 1.5;
}
