const express = require('express');
const bodyParser = require('body-parser');
const morgan = require('morgan');
const path = require('path');
const config = require('./config');
const FileCleaner = require('./utils/file_cleaner');
// 添加XML解析支持，用于处理微信消息推送
const xmlparser = require('express-xml-bodyparser');

// 导入路由和工具函数
const { router: apiRoutes, storeCheckResult } = require('./routes/api');
const wechatRoutes = require('./routes/wechat');

// 创建Express应用
const app = express();
const port = config.server.port;

// 中间件
app.use(morgan('dev')); // 日志
app.use(bodyParser.json()); // 解析JSON请求体
app.use(bodyParser.urlencoded({ extended: true })); // 解析URL编码的请求体
app.use(xmlparser()); // 解析XML请求体，用于微信消息推送

// 静态文件
app.use(express.static(path.join(__dirname, 'public')));
// 静态文件 - 添加上传目录
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 获取package.json中的版本信息
const packageInfo = require('./package.json');

// 首页路由
app.get('/', (req, res) => {
  res.send(`微信内容安全检测API服务 v${packageInfo.version} 正在运行`);
});

// 使用路由
app.use('/api', apiRoutes);
app.use('/wechat', wechatRoutes);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: -1,
    msg: '服务器内部错误'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: -1,
    msg: '请求的资源不存在'
  });
});

// 启动文件清理器 - 24小时后自动删除上传的文件
const uploadDir = path.join(__dirname, 'uploads');
const fileCleaner = new FileCleaner(uploadDir, 24).start();

// 优雅关闭
process.on('SIGINT', () => {
  console.log('正在关闭服务...');
  fileCleaner.stop();
  process.exit();
});

// 启动服务器
app.listen(port, () => {
  console.log(`服务器运行在 http://${config.server.host}:${port}`);
  console.log(`上传文件将在24小时后自动删除，清理频率: 每6小时`);
});
