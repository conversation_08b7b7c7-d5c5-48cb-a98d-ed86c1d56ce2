<template>
	<view class="image-compression">
		<!-- 自定义导航栏 -->
		<view class="custom-nav glassmorphism">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-content">
				<view class="back-btn" @tap="goBack">
					<view class="wx-nav-back"></view>
				</view>
				<text class="nav-title">批量压缩</text>
				<view class="share-button-container">
					<share-button @share="handleShare" :showText="false" />
				</view>
			</view>
		</view>

		<view class="main-content" :style="{ paddingTop: navHeight + 'px' }">
			<!-- 图片列表区域 -->
			<view class="image-list neumorphism">
				<view class="section-title">
					<text>批量压缩</text>
					<view class="clear-btn" @click="clearImages">
						<text>清空图片</text>
					</view>
				</view>

				<!-- 图片项 -->
				<view class="image-item" v-for="(item, index) in imagesList" :key="index">
					<image class="preview-image" :src="item.path" mode="aspectFit"></image>
					<view class="image-info">
						<view class="info-line">
							<text>压缩前：</text>
							<text>尺寸: {{item.width}} × {{item.height}} 大小: {{item.originalSize}} KB</text>
						</view>
						<view class="info-line">
							<text>压缩后：</text>
							<text v-if="item.compressedSize">尺寸: {{item.compressedWidth || item.width}} × {{item.compressedHeight || item.height}} 大小: {{item.compressedSize}} KB</text>
							<text v-else>尺寸: {{compressMode === 'custom' ? customWidth + ' × ' + customHeight : Math.floor(item.width * size / 100) + ' × ' + Math.floor(item.height * size / 100)}} 大小: -- KB</text>
						</view>
						<view class="progress-bar" v-if="compressing && currentIndex === index">
							<view class="progress-inner" :style="{width: '100%'}"></view>
						</view>
					</view>
					<view class="delete-btn" @click="deleteImage(index)">×</view>
				</view>

				<!-- 提示信息 -->
				<view class="tip-text" v-if="imagesList.length > 0">
					压缩在微信本地完成，您的照片不会被泄露
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="imagesList.length === 0" @tap="selectImages">
					<image src="/static/upload.svg" mode="aspectFit" class="upload-icon"></image>
					<text>请选择需要压缩的图片</text>
					<text class="click-hint">点击此处选择图片</text>
				</view>
			</view>

			<!-- 添加隐藏的canvas -->
			<canvas
				type="2d"
				id="compressCanvas"
				:style="{
					width: `${canvasWidth}px`,
					height: `${canvasHeight}px`,
					position: 'fixed',
					left: '-9999px'
				}"
			></canvas>

			<!-- 添加用于图片预处理的隐藏canvas -->
			<canvas
				type="2d"
				id="uploadPreprocessCanvas"
				style="position: fixed; left: -9999px; width: 300px; height: 300px;"
			></canvas>
		</view>

		<!-- 尺寸输入弹出层 -->
		<view class="size-popup" v-if="showSizeInput">
			<view class="popup-mask" @tap="hideSizePopup"></view>
			<view class="popup-content neumorphism">
				<view class="popup-header">
					<text class="popup-title">设置批量压缩尺寸</text>
					<text class="popup-close" @tap="hideSizePopup">×</text>
				</view>
				<view class="popup-body">
					<view class="input-group">
						<text>宽度</text>
						<input type="number" v-model="tempWidth" placeholder="输入宽度" :selection-start="0" :selection-end="-1" @focus="handleInputFocus" />
					</view>
					<view class="input-group">
						<text>高度</text>
						<input type="number" v-model="tempHeight" placeholder="输入高度" :selection-start="0" :selection-end="-1" @focus="handleInputFocus" />
					</view>
					<view class="popup-buttons">
						<button class="cancel-btn" @tap="hideSizePopup">取消</button>
						<button class="confirm-btn" @tap="confirmSize">确定</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 分享弹窗 -->
		<view class="share-popup" v-if="showSharePopup">
			<view class="popup-mask" @tap="hideSharePopup"></view>
			<view class="popup-content neumorphism">
				<view class="popup-header">
					<text class="popup-title">压缩成功</text>
					<text class="popup-close" @tap="hideSharePopup">×</text>
				</view>
				<view class="popup-body">
					<view class="share-content">
						<image src="/static/share-icon.png" mode="aspectFit" class="share-icon"></image>
						<text class="share-title">图片已保存到相册</text>
						<text class="share-desc">分享给好友一起使用吧</text>
					</view>
					<view class="share-buttons">
						<button class="share-btn" open-type="share">
							<text class="iconfont icon-wechat"></text>
							<text>分享给好友</text>
						</button>
						<button class="cancel-btn" @tap="hideSharePopup">关闭</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部固定区域 -->
		<view class="footer-area">
			<!-- 压缩设置 -->
			<view class="compress-settings glassmorphism">
				<!-- 压缩质量滑块 -->
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-label">压缩质量</text>
						<text class="setting-value glassmorphism">{{quality}}</text>
					</view>
					<view class="slider-container neumorphism-inset">
						<slider :value="quality" @change="onQualityChange" @changing="onQualityChanging" min="0" max="100"
							activeColor="#07C160" backgroundColor="rgba(7, 193, 96, 0.1)"
							block-color="#ffffff" block-size="28" step="1"></slider>
					</view>
				</view>

				<!-- 尺寸大小滑块 -->
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-label">尺寸大小</text>
						<view class="size-controls">
							<view class="size-display glassmorphism" @tap="showSizePopup" v-if="imagesList.length > 0">
								<text>{{customWidth}} × {{customHeight}}</text>
							</view>
							<text class="setting-value glassmorphism">{{size}}%</text>
						</view>
					</view>
					<view class="slider-container neumorphism-inset">
						<slider :value="size" @change="onSizeChange" @changing="onSizeChanging" min="5" max="100"
							activeColor="#07C160" backgroundColor="rgba(7, 193, 96, 0.1)"
							block-color="#ffffff" block-size="28" step="1"></slider>
					</view>
				</view>
			</view>

			<!-- 底部按钮区域 -->
			<view class="button-group">
				<button class="btn select-btn" @tap="selectImages">选择图片</button>
				<button class="btn compress-btn" @tap="startCompression" :disabled="imagesList.length === 0">开始压缩</button>
			</view>
		</view>
	</view>
</template>

<script>
import ShareButton from '@/components/shareButton.vue';
import { checkImageSecurity, handleCheckResult, batchCheckImages } from '@/utils/security.js';
import { isImageRisky } from '@/utils/securityStore.js';
import { getFreeCount, decreaseFreeCount } from '@/utils/freeCounter.js';
import { loginToImageCompressionService } from '@/utils/auth.js';
import { getMemberInfo, isValidMember } from '@/utils/memberManager.js';

export default {
	components: {
		ShareButton
	},
	data() {
		return {
			imagesList: [],
			quality: 80,
			size: 100,
			compressing: false,
			statusBarHeight: 0,
			navHeight: 0,
			canvasWidth: 0,
			canvasHeight: 0,
			compressTimer: null,
			currentIndex: -1,
			startTime: 0,
			endTime: 0,
			showSizeInput: false,
			tempWidth: '',
			tempHeight: '',
			customWidth: '',
			customHeight: '',
			compressMode: 'slider',
			selectionStart: 0,
			selectionEnd: 0,
			lastCompressParams: null,
			showSharePopup: false,
			isValidMemberUser: false // 保存当前用户的会员状态
		}
	},
	onLoad(options) {
		// 获取传入的图片列表
		if (options.images) {
			try {
				const tempFilePaths = JSON.parse(decodeURIComponent(options.images));
				if (Array.isArray(tempFilePaths) && tempFilePaths.length > 0) {
					this.processImages(tempFilePaths);
				}
			} catch (e) {
				console.error('解析传入图片数据失败:', e);
			}
		}

		// 获取状态栏高度
		const windowInfo = uni.getWindowInfo();
		this.statusBarHeight = windowInfo.statusBarHeight;
		// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
		this.navHeight = this.statusBarHeight + 44;
	},
	methods: {
		// 显示会员购买提示
		showMembershipPrompt() {
			uni.showModal({
				title: '免费次数已用完',
				content: '您今日的免费压缩次数已用完，购买会员即可无限制使用',
				confirmText: '购买会员',
				cancelText: '稍后再说',
				success: (res) => {
					if (res.confirm) {
						// 跳转到个人中心页面
						uni.navigateTo({
							url: '/pages/profile/index'
						});
					}
				}
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				delta: 1,
				fail: () => {
					// 如果无法返回上一页（例如直接打开此页面），则跳转到首页
					uni.redirectTo({
						url: '/pages/index/index'
					});
				}
			});
		},

		// 处理分享事件
		handleShare() {
			if (this.imagesList.length > 0) {
				// 如果有压缩后的图片，优先分享第一张压缩后的图片
				const hasCompressed = this.imagesList.some(item => item.compressedPath);

				if (hasCompressed) {
					// 查找第一个压缩后的图片
					const firstCompressedImage = this.imagesList.find(item => item.compressedPath);
					// 分享压缩后的图片
					uni.showShareMenu({
						withShareTicket: true,
						menus: ['shareAppMessage', 'shareTimeline']
					});

					// 更新当前页面的分享信息
					const currentParams = encodeURIComponent(JSON.stringify({
						quality: this.quality,
						size: this.size,
						mode: this.compressMode,
						customWidth: this.customWidth,
						customHeight: this.customHeight
					}));

					// 保存当前分享为全局状态
					getApp().globalData = getApp().globalData || {};
					getApp().globalData.shareInfo = {
						title: '我用图片压缩工具压缩了图片，效果不错！',
						path: `/pages/index/index?from=share&params=${currentParams}`,
						imageUrl: firstCompressedImage.compressedPath
					};

					uni.showToast({
						title: '请点击右上角分享',
						icon: 'none',
						duration: 2000
					});
				} else {
					// 分享小程序
					uni.showShareMenu({
						withShareTicket: true,
						menus: ['shareAppMessage', 'shareTimeline']
					});

					// 更新当前页面的分享信息
					getApp().globalData = getApp().globalData || {};
					getApp().globalData.shareInfo = {
						title: '推荐这个好用的图片压缩工具！',
						path: '/pages/index/index?from=share',
						imageUrl: '/static/share-icon.png'
					};

					uni.showToast({
						title: '请点击右上角分享',
						icon: 'none',
						duration: 2000
					});
				}
			} else {
				// 分享小程序
				uni.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline']
				});

				// 更新当前页面的分享信息
				getApp().globalData = getApp().globalData || {};
				getApp().globalData.shareInfo = {
					title: '推荐这个好用的图片压缩工具！',
					path: '/pages/index/index?from=share',
					imageUrl: '/static/share-icon.png'
				};

				uni.showToast({
					title: '请点击右上角分享',
					icon: 'none',
					duration: 2000
				});
			}
		},

		// 处理图片列表
		async processImages(tempFilePaths) {
			for (let i = 0; i < tempFilePaths.length; i++) {
				const path = tempFilePaths[i];
				try {
					// 在后台进行内容安全检测（完全静默，不影响用户体验）
					// 使用Promise.resolve().then()确保检测在后台进行，不阻塞UI
					Promise.resolve().then(async () => {
						try {
							// 调用内容安全检测API
							const checkResult = await checkImageSecurity(path);

							// 处理检测结果
							handleCheckResult(checkResult);
							// 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果
						} catch (error) {
							// 只在控制台记录错误，不影响用户体验
							console.error('内容安全检测失败，继续处理图片:', error);
						}
					});

					const [fileInfo, imageInfo] = await Promise.all([
						new Promise((resolve, reject) => {
							const fs = uni.getFileSystemManager();
							fs.getFileInfo({
								filePath: path,
								success: resolve,
								fail: reject
							});
						}),
						new Promise((resolve, reject) => {
							uni.getImageInfo({
								src: path,
								success: resolve,
								fail: reject
							});
						})
					]);

					this.imagesList.push({
						path: path,
						width: imageInfo.width,
						height: imageInfo.height,
						originalSize: Math.round(fileInfo.size / 1024),
						compressedSize: null
					});
				} catch (error) {
					console.error('获取图片信息失败:', error);
				}
			}

			// 处理完图片后，更新自定义尺寸显示
			this.updateCustomSize();

			// 加载完图片后进行预览压缩
			if (this.imagesList.length > 0) {
				this.previewCompression();
			}
		},

		// 选择图片
		selectImages() {
			uni.showActionSheet({
				itemList: ['拍照', '从手机相册选择', '从聊天中选择'],
				success: (res) => {
					let sourceType;
					switch(res.tapIndex) {
						case 0:
							sourceType = ['camera'];
							break;
						case 1:
							sourceType = ['album'];
							break;
						case 2:
							// 从聊天中选择图片
							uni.chooseMessageFile({
								count: 9,
								type: 'image',
								success: (res) => {
									const tempFilePaths = res.tempFiles.map(file => file.path);
									this.processImages(tempFilePaths);
								},
								fail: (err) => {
									uni.showToast({
										title: '选择图片失败',
										icon: 'none'
									});
								}
							});
							return;
					}

					uni.chooseImage({
						count: 9,
						sourceType: sourceType,
						success: (res) => {
							const tempFilePaths = res.tempFilePaths;
							this.processImages(tempFilePaths);
						}
					});
				}
			});
		},

		// 删除单张图片
		deleteImage(index) {
			this.imagesList.splice(index, 1);
		},

		// 清空所有图片
		clearImages() {
			this.imagesList = [];
		},

		// 压缩质量变化
		onQualityChange(e) {
			this.quality = e.detail.value;
			if (this.compressTimer) clearTimeout(this.compressTimer);
			this.compressTimer = setTimeout(() => {
				// 对第一张图片进行预览压缩
				this.previewCompression();
			}, 300);
		},

		// 压缩质量滑动中
		onQualityChanging(e) {
			this.quality = e.detail.value;
			// 更新所有图片项的压缩后信息状态
			this.updateCompressionStatus('计算中...');
		},

		// 尺寸大小变化
		onSizeChange(e) {
			this.compressMode = 'slider';
			this.size = e.detail.value;
			this.updateCustomSize();
			if (this.compressTimer) clearTimeout(this.compressTimer);
			this.compressTimer = setTimeout(() => {
				// 对第一张图片进行预览压缩
				this.previewCompression();
			}, 300);
		},

		// 尺寸滑动中
		onSizeChanging(e) {
			this.size = e.detail.value;
			this.updateCustomSize();
			// 更新所有图片项的压缩后信息状态
			this.updateCompressionStatus('计算中...');
		},

		// 更新所有图片项的压缩状态
		updateCompressionStatus(status) {
			for(let i = 0; i < this.imagesList.length; i++) {
				// 只更新UI显示，不实际压缩
				this.$set(this.imagesList[i], 'compressedSize', status);
			}
		},

		// 对第一张图片进行预览压缩
		async previewCompression() {
			if(this.imagesList.length === 0) return;
			if(this.compressing) return;

			try {
				// 更新状态显示为正在压缩
				this.updateCompressionStatus('计算中...');

				// 只压缩第一张作为预览
				const item = this.imagesList[0];

				let targetWidth, targetHeight;
				let canvas = null;
				let ctx = null;

				if (this.compressMode === 'custom') {
					// 自定义尺寸模式，使用自定义输入的尺寸
					targetWidth = parseInt(this.customWidth);
					targetHeight = parseInt(this.customHeight);

					// 1. 计算宽度和高度的缩放比例
					const widthRatio = targetWidth / item.width;
					const heightRatio = targetHeight / item.height;

					// 2. 使用较大的缩放比例，确保图片完整显示
					const scaleRatio = Math.max(widthRatio, heightRatio);

					// 3. 计算缩放后的尺寸
					const scaledWidth = Math.round(item.width * scaleRatio);
					const scaledHeight = Math.round(item.height * scaleRatio);

					// 4. 计算裁剪的起始位置（居中裁剪）
					const cropX = Math.round((scaledWidth - targetWidth) / 2);
					const cropY = Math.round((scaledHeight - targetHeight) / 2);

					// 设置 canvas 尺寸
					this.canvasWidth = targetWidth;
					this.canvasHeight = targetHeight;

					// 等待一帧以确保 canvas 尺寸更新
					await new Promise(resolve => setTimeout(resolve, 100));

					// 获取 canvas 上下文
					const query = uni.createSelectorQuery();
					try {
						canvas = await new Promise((resolve, reject) => {
							query.select('#compressCanvas')
								.fields({ node: true, size: true })
								.exec((res) => {
									if (res && res[0] && res[0].node) {
										resolve(res[0].node);
									} else {
										reject(new Error('找不到canvas节点'));
									}
								});
						});

						ctx = canvas.getContext('2d');

						// 设置 canvas 尺寸
						canvas.width = targetWidth;
						canvas.height = targetHeight;

						// 清空画布
						ctx.clearRect(0, 0, targetWidth, targetHeight);

						// 创建图片对象
						const image = canvas.createImage();
						await new Promise((resolve, reject) => {
							image.onload = resolve;
							image.onerror = reject;
							image.src = item.path;
						});

						// 先缩放，再裁剪
						ctx.drawImage(
							image,
							0, 0, item.width, item.height,  // 源图像区域
							-cropX, -cropY, scaledWidth, scaledHeight  // 目标区域，通过调整x和y坐标实现居中裁剪
						);
					} catch (error) {
						console.error('Canvas处理失败:', error);
						throw new Error('Canvas处理失败: ' + error.message);
					}
				} else {
					// 滑块压缩模式，根据百分比计算
					targetWidth = Math.floor(item.width * (this.size / 100));
					targetHeight = Math.floor(item.height * (this.size / 100));

					// 设置 canvas 尺寸
					this.canvasWidth = targetWidth;
					this.canvasHeight = targetHeight;

					// 等待一帧以确保 canvas 尺寸更新
					await new Promise(resolve => setTimeout(resolve, 100));

					// 获取 canvas 上下文
					const query = uni.createSelectorQuery();
					try {
						canvas = await new Promise((resolve, reject) => {
							query.select('#compressCanvas')
								.fields({ node: true, size: true })
								.exec((res) => {
									if (res && res[0] && res[0].node) {
										resolve(res[0].node);
									} else {
										reject(new Error('找不到canvas节点'));
									}
								});
						});

						ctx = canvas.getContext('2d');

						// 设置 canvas 尺寸
						canvas.width = targetWidth;
						canvas.height = targetHeight;

						// 清空画布
						ctx.clearRect(0, 0, targetWidth, targetHeight);

						// 创建图片对象
						const image = canvas.createImage();
						await new Promise((resolve, reject) => {
							image.onload = resolve;
							image.onerror = reject;
							image.src = item.path;
						});

						// 绘制图片
						ctx.drawImage(image, 0, 0, targetWidth, targetHeight);
					} catch (error) {
						console.error('Canvas处理失败:', error);
						throw new Error('Canvas处理失败: ' + error.message);
					}
				}

				// 确保canvas已定义
				if (!canvas) {
					throw new Error('Canvas未初始化');
				}

				// 导出图片
				const result = await new Promise((resolve, reject) => {
					try {
						uni.canvasToTempFilePath({
							canvas: canvas,
							fileType: 'jpg',
							quality: this.quality / 100,
							success: resolve,
							fail: reject
						});
					} catch (error) {
						reject(error);
					}
				});

				// 获取压缩后的图片信息
				const compressedInfo = await new Promise((resolve, reject) => {
					const fs = uni.getFileSystemManager();
					fs.getFileInfo({
						filePath: result.tempFilePath,
						success: resolve,
						fail: reject
					});
				});

				// 获取压缩后的图片尺寸信息
				const compressedImageInfo = await new Promise((resolve, reject) => {
					uni.getImageInfo({
						src: result.tempFilePath,
						success: resolve,
						fail: reject
					});
				});

				// 更新第一张图片的压缩后信息
				this.$set(this.imagesList[0], 'compressedSize',
					Math.round(compressedInfo.size / 1024));
				this.$set(this.imagesList[0], 'compressedPath', result.tempFilePath);
				this.$set(this.imagesList[0], 'compressedWidth', compressedImageInfo.width);
				this.$set(this.imagesList[0], 'compressedHeight', compressedImageInfo.height);

				// 更新所有其他图片的预估压缩后大小
				for(let i = 1; i < this.imagesList.length; i++) {
					const curItem = this.imagesList[i];
					// 使用第一张图片的压缩比例估算其他图片的压缩后大小
					const compressionRatio = this.imagesList[0].compressedSize / this.imagesList[0].originalSize;
					const estimatedSize = Math.round(curItem.originalSize * compressionRatio);
					this.$set(this.imagesList[i], 'compressedSize',
						`约 ${estimatedSize}`);

					if (this.compressMode === 'custom') {
						this.$set(this.imagesList[i], 'compressedWidth', targetWidth);
						this.$set(this.imagesList[i], 'compressedHeight', targetHeight);
					} else {
						this.$set(this.imagesList[i], 'compressedWidth',
							Math.floor(curItem.width * (this.size / 100)));
						this.$set(this.imagesList[i], 'compressedHeight',
							Math.floor(curItem.height * (this.size / 100)));
					}
				}

				// 记录当前的压缩参数，以便后续判断是否需要重新压缩
				this.lastCompressParams = {
					quality: this.quality,
					size: this.size,
					customWidth: this.customWidth,
					customHeight: this.customHeight,
					compressMode: this.compressMode
				};
			} catch (error) {
				console.error('预览压缩失败:', error);
				uni.showToast({
					title: '压缩失败: ' + (error.message || '未知错误'),
					icon: 'none',
					duration: 2000
				});
				this.updateCompressionStatus('--');
			}
		},

		// 根据滑块值更新自定义尺寸
		updateCustomSize() {
			// 如果没有图片，无需计算
			if(this.imagesList.length === 0) return;

			// 以第一张图片为基准计算预览尺寸
			const firstImage = this.imagesList[0];
			const newWidth = Math.floor(firstImage.width * (this.size / 100));
			const newHeight = Math.floor(firstImage.height * (this.size / 100));

			this.customWidth = newWidth.toString();
			this.customHeight = newHeight.toString();
		},

		// 显示尺寸设置弹窗
		showSizePopup() {
			this.tempWidth = '';
			this.tempHeight = '';
			this.showSizeInput = true;
		},

		// 关闭尺寸设置弹窗
		hideSizePopup() {
			this.showSizeInput = false;
			this.tempWidth = '';
			this.tempHeight = '';
		},

		// 确认自定义尺寸
		confirmSize() {
			this.compressMode = 'custom';
			const width = parseInt(this.tempWidth);
			const height = parseInt(this.tempHeight);

			if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
				uni.showToast({
					title: '请输入有效的尺寸',
					icon: 'none'
				});
				return;
			}

			this.customWidth = width.toString();
			this.customHeight = height.toString();

			this.hideSizePopup();

			// 确保图片列表不为空
			if (this.imagesList.length > 0) {
				// 清除之前的计时器，避免多次调用
				if (this.compressTimer) clearTimeout(this.compressTimer);

				// 显示计算中状态
				this.updateCompressionStatus('计算中...');

				// 添加一个短暂延迟，确保UI更新后再进行压缩
				this.compressTimer = setTimeout(() => {
					// 进行预览压缩
					this.previewCompression();
				}, 100);
			} else {
				uni.showToast({
					title: '请先选择图片',
					icon: 'none'
				});
			}
		},

		// 输入框获取焦点时全选文本
		handleInputFocus(event) {
			// 使用延时确保在输入框获得焦点后执行选中
			setTimeout(() => {
				const value = event.target.value;
				if (value) {
					this.selectionStart = 0;
					this.selectionEnd = value.toString().length;
				}
			}, 100);
		},

		// 开始压缩
		async startCompression() {
			if (this.compressing || this.imagesList.length === 0) return;

			try {
				console.log('🚀 开始批量压缩流程...');

				// 首先确保用户已登录
				await loginToImageCompressionService();

				// 检查会员状态
				const memberInfo = await getMemberInfo();
				this.isValidMemberUser = await isValidMember();

				console.log('👤 用户会员状态:', { memberInfo, isValidMemberUser: this.isValidMemberUser });

				// 如果不是有效会员，检查免费次数
				if (!this.isValidMemberUser) {
					const freeCount = getFreeCount();
					console.log('🆓 免费次数检查:', freeCount);

					if (freeCount <= 0) {
						// 免费次数用完，引导购买会员
						this.showMembershipPrompt();
						return;
					}
				}
			} catch (error) {
				console.error('❌ 会员状态检查失败:', error);
				// 如果检查失败，按非会员处理
				const freeCount = getFreeCount();
				if (freeCount <= 0) {
					this.showMembershipPrompt();
					return;
				}
			}

			// 显示加载提示
			uni.showLoading({
				title: '检查图片中...',
				mask: true
			});

			try {
				// 批量检查所有图片是否违规，使用本地存储的结果，不查询服务器
				// 因为轮询已经在后台持续更新结果
				const imagePaths = this.imagesList.map(item => item.path);
				const checkResults = await batchCheckImages(imagePaths, false);

				// 检查是否有正在检测中的图片
				const checkingImages = checkResults.filter(result => result.isChecking);
				if (checkingImages.length > 0) {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: '部分图片安全检测尚未完成，请稍后再试',
						showCancel: false
					});
					return;
				}

				// 检查是否有违规图片
				const riskyImages = checkResults.filter(result => result.isRisky);
				if (riskyImages.length > 0) {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: `检测到${riskyImages.length}张不合规的图片，无法进行压缩`,
						showCancel: false
					});
					return;
				}

				// 隐藏加载提示
				uni.hideLoading();
			} catch (error) {
				console.error('[安全检测] 批量检查图片失败:', error);
				uni.hideLoading();
				// 出错时继续执行，不阻止用户操作
			}

			this.compressing = true;
			this.startTime = Date.now();

			uni.showLoading({
				title: '压缩中...',
				mask: true
			});

			try {
				// 验证当前的压缩参数是否与预览时相同
				const currentParams = {
					quality: this.quality,
					size: this.size,
					customWidth: this.customWidth,
					customHeight: this.customHeight,
					compressMode: this.compressMode
				};

				const paramsChanged = !this.lastCompressParams ||
					JSON.stringify(currentParams) !== JSON.stringify(this.lastCompressParams);

				// 如果参数发生变化或者第一张图片没有压缩过，需要重新压缩第一张
				if (paramsChanged || !this.imagesList[0].compressedPath) {
					await this.previewCompression();
				}

				// 从第二张图片开始压缩，第一张已经在预览时压缩好了
				for (let i = 1; i < this.imagesList.length; i++) {
					this.currentIndex = i;
					const item = this.imagesList[i];

					let targetWidth, targetHeight;
					let canvas = null;
					let ctx = null;

					if (this.compressMode === 'custom') {
						// 自定义尺寸模式，使用自定义输入的尺寸
						targetWidth = parseInt(this.customWidth);
						targetHeight = parseInt(this.customHeight);

						// 1. 计算宽度和高度的缩放比例
						const widthRatio = targetWidth / item.width;
						const heightRatio = targetHeight / item.height;

						// 2. 使用较大的缩放比例，确保图片完整显示
						const scaleRatio = Math.max(widthRatio, heightRatio);

						// 3. 计算缩放后的尺寸
						const scaledWidth = Math.round(item.width * scaleRatio);
						const scaledHeight = Math.round(item.height * scaleRatio);

						// 4. 计算裁剪的起始位置（居中裁剪）
						const cropX = Math.round((scaledWidth - targetWidth) / 2);
						const cropY = Math.round((scaledHeight - targetHeight) / 2);

						// 设置 canvas 尺寸
						this.canvasWidth = targetWidth;
						this.canvasHeight = targetHeight;

						// 等待一帧以确保 canvas 尺寸更新
						await new Promise(resolve => setTimeout(resolve, 100));

						// 获取 canvas 上下文
						const query = uni.createSelectorQuery();
						try {
							canvas = await new Promise((resolve, reject) => {
								query.select('#compressCanvas')
									.fields({ node: true, size: true })
									.exec((res) => {
										if (res && res[0] && res[0].node) {
											resolve(res[0].node);
										} else {
											reject(new Error('找不到canvas节点'));
										}
									});
							});

							ctx = canvas.getContext('2d');

							// 设置 canvas 尺寸
							canvas.width = targetWidth;
							canvas.height = targetHeight;

							// 清空画布
							ctx.clearRect(0, 0, targetWidth, targetHeight);

							// 创建图片对象
							const image = canvas.createImage();
							await new Promise((resolve, reject) => {
								image.onload = resolve;
								image.onerror = reject;
								image.src = item.path;
							});

							// 先缩放，再裁剪
							ctx.drawImage(
								image,
								0, 0, item.width, item.height,  // 源图像区域
								-cropX, -cropY, scaledWidth, scaledHeight  // 目标区域，通过调整x和y坐标实现居中裁剪
							);
						} catch (error) {
							console.error('Canvas处理失败:', error);
							throw new Error('Canvas处理失败: ' + error.message);
						}
					} else {
						// 滑块压缩模式，根据百分比计算
						targetWidth = Math.floor(item.width * (this.size / 100));
						targetHeight = Math.floor(item.height * (this.size / 100));

						// 设置 canvas 尺寸
						this.canvasWidth = targetWidth;
						this.canvasHeight = targetHeight;

						// 等待一帧以确保 canvas 尺寸更新
						await new Promise(resolve => setTimeout(resolve, 100));

						// 获取 canvas 上下文
						const query = uni.createSelectorQuery();
						try {
							canvas = await new Promise((resolve, reject) => {
								query.select('#compressCanvas')
									.fields({ node: true, size: true })
									.exec((res) => {
										if (res && res[0] && res[0].node) {
											resolve(res[0].node);
										} else {
											reject(new Error('找不到canvas节点'));
										}
									});
							});

							ctx = canvas.getContext('2d');

							// 设置 canvas 尺寸
							canvas.width = targetWidth;
							canvas.height = targetHeight;

							// 清空画布
							ctx.clearRect(0, 0, targetWidth, targetHeight);

							// 创建图片对象
							const image = canvas.createImage();
							await new Promise((resolve, reject) => {
								image.onload = resolve;
								image.onerror = reject;
								image.src = item.path;
							});

							// 绘制图片
							ctx.drawImage(image, 0, 0, targetWidth, targetHeight);
						} catch (error) {
							console.error('Canvas处理失败:', error);
							throw new Error('Canvas处理失败: ' + error.message);
						}
					}

					// 确保canvas已定义
					if (!canvas) {
						throw new Error('Canvas未初始化');
					}

					// 导出图片
					const result = await new Promise((resolve, reject) => {
						try {
							uni.canvasToTempFilePath({
								canvas: canvas,
								fileType: 'jpg',
								quality: this.quality / 100,
								success: resolve,
								fail: reject
							});
						} catch (error) {
							reject(error);
						}
					});

					// 获取压缩后的图片信息
					const compressedInfo = await new Promise((resolve, reject) => {
						const fs = uni.getFileSystemManager();
						fs.getFileInfo({
							filePath: result.tempFilePath,
							success: resolve,
							fail: reject
						});
					});

					// 获取压缩后的图片尺寸信息
					const compressedImageInfo = await new Promise((resolve, reject) => {
						uni.getImageInfo({
							src: result.tempFilePath,
							success: resolve,
							fail: reject
						});
					});

					// 更新压缩后的信息
					this.$set(this.imagesList[i], 'compressedSize',
						Math.round(compressedInfo.size / 1024));
					this.$set(this.imagesList[i], 'compressedPath', result.tempFilePath);
					this.$set(this.imagesList[i], 'compressedWidth', compressedImageInfo.width);
					this.$set(this.imagesList[i], 'compressedHeight', compressedImageInfo.height);

					// 更新压缩进度提示
					uni.showLoading({
						title: `压缩中(${i+1}/${this.imagesList.length})`,
						mask: true
					});
				}

				this.endTime = Date.now();
				this.currentIndex = -1;

				// 减少免费次数（如果不是会员）
				if (!this.isValidMemberUser) {
					decreaseFreeCount();
					console.log('📉 减少免费次数，剩余:', getFreeCount());
				} else {
					console.log('👑 会员用户，无需减少免费次数');
				}

				// 压缩完成，保存图片
				await this.saveCompressedImages();

				uni.hideLoading();

				// 计算耗时
				const timeCost = ((this.endTime - this.startTime) / 1000).toFixed(1);

				// 显示分享弹窗
				this.showSharePopup = true;

			} catch (error) {
				console.error('压缩失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '压缩失败: ' + (error.message || '未知错误'),
					icon: 'none',
					duration: 2000
				});
			} finally {
				this.compressing = false;
				this.currentIndex = -1;
			}
		},

		// 保存压缩后的图片
		async saveCompressedImages() {
			const savedImages = [];

			try {
				for (const item of this.imagesList) {
					if (item.compressedPath) {
						await new Promise((resolve, reject) => {
							uni.saveImageToPhotosAlbum({
								filePath: item.compressedPath,
								success: () => {
									savedImages.push(item);
									resolve();
								},
								fail: (err) => {
									if (err.errMsg.includes('auth deny')) {
										// 权限问题，直接结束循环
										reject(new Error('需要相册权限'));
									} else {
										// 其他错误，继续尝试保存其他图片
										resolve();
									}
								}
							});
						});
					}
				}

				if (savedImages.length === 0 && this.imagesList.some(item => item.compressedPath)) {
					// 没有成功保存任何图片，但有压缩过的图片，可能是权限问题
					uni.showModal({
						title: '提示',
						content: '需要保存相册权限，是否去设置？',
						success: (res) => {
							if (res.confirm) {
								uni.openSetting();
							}
						}
					});
				}
			} catch (error) {
				// 处理权限问题
				uni.showModal({
					title: '提示',
					content: '需要保存相册权限，是否去设置？',
					success: (res) => {
						if (res.confirm) {
							uni.openSetting();
						}
					}
				});
			}
		},
		onShareAppMessage(res) {
			// 判断是否有全局分享信息
			if (getApp().globalData && getApp().globalData.shareInfo) {
				return getApp().globalData.shareInfo;
			}
			// 默认分享信息
			return {
				title: '推荐这个好用的图片压缩工具！',
				path: '/pages/index/index?from=share',
				imageUrl: '/static/share-icon.png'
			};
		},
		onShareTimeline() {
			// 判断是否有全局分享信息
			if (getApp().globalData && getApp().globalData.shareInfo) {
				return {
					title: getApp().globalData.shareInfo.title,
					path: getApp().globalData.shareInfo.path,
					imageUrl: getApp().globalData.shareInfo.imageUrl
				};
			}
			// 默认分享信息
			return {
				title: '推荐这个好用的图片压缩工具！',
				path: '/pages/index/index?from=share',
				imageUrl: '/static/share-icon.png'
			};
		},
		// 隐藏分享弹窗
		hideSharePopup() {
			this.showSharePopup = false;
		}
	}
}
</script>

<style lang="scss">
// 使用uni.scss中定义的主题色变量
$primary-color: $uni-color-primary; // 主题色：微信绿
$primary-gradient: $theme-color-primary-gradient;
$bg-color: $uni-bg-color-grey; // 背景色：微信灰
$text-primary: $uni-text-color; // 主要文字颜色
$text-secondary: $theme-text-secondary; // 次要文字颜色
$text-tertiary: $uni-text-color-grey; // 辅助文字颜色
$link-color: $theme-color-link; // 链接/高亮文字颜色
$border-color: $uni-border-color; // 边框颜色
$shadow-dark: $theme-shadow-dark;
$shadow-light: $theme-shadow-light;

page {
	background-color: $bg-color;
}

// 新拟物风格的混入
@mixin neumorphism {
	background: $bg-color;
	box-shadow: 12px 12px 24px $shadow-dark,
				-8px -8px 20px $shadow-light,
				inset 2px 2px 4px rgba(255, 255, 255, 0.5),
				inset -2px -2px 4px rgba(0, 0, 0, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

@mixin neumorphism-inset {
	background: $bg-color;
	box-shadow: inset 6px 6px 12px $shadow-dark,
				inset -6px -6px 12px $shadow-light;
}

// 磨砂玻璃风格的混入
@mixin glassmorphism {
	background: rgba($bg-color, 0.98);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.neumorphism {
	@include neumorphism;
}

.neumorphism-inset {
	@include neumorphism-inset;
}

.glassmorphism {
	@include glassmorphism;
}

.image-compression {
	padding: 0 30rpx 30rpx;
	position: relative;
	min-height: 100vh;
	box-sizing: border-box;
	padding-bottom: 260rpx; /* 为底部区域预留空间 */

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;
		padding: 0 30rpx;

		.nav-content {
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.back-btn {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.wx-nav-back {
					width: 12px;
					height: 24px;
					background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E%3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1 1 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E");
					background-size: cover;
					background-repeat: no-repeat;
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: $text-primary;
				font-weight: 500;
			}

			.share-button-container {
				min-width: 36px;
				display: flex;
				justify-content: flex-end;
				margin-right: 180rpx;  /* 增加右边距，让按钮与微信官方按钮保持一定距离 */
			}

			.placeholder {
				width: 60rpx;
			}
		}
	}

	.main-content {
		position: relative;
		width: 100%;
	}

	.image-list {
		margin: 20rpx 0;
		border-radius: 30rpx;
		padding: 30rpx;

		.section-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10rpx 0 20rpx;
			border-bottom: 1px solid rgba(0, 0, 0, 0.05);

			text {
				font-size: 32rpx;
				font-weight: 500;
				color: $text-primary;
			}

			.clear-btn {
				color: $primary-color;
				font-size: 28rpx;
				padding: 6rpx 20rpx;
				border-radius: 30rpx;
				@include glassmorphism;

				&:active {
					opacity: 0.8;
				}
			}
		}

		.image-item {
			position: relative;
			padding: 20rpx 0;
			border-bottom: 1px solid rgba(0, 0, 0, 0.05);
			display: flex;

			.preview-image {
				width: 135rpx;
				height: 135rpx;
				border-radius: 10rpx;
				margin-right: 20rpx;
			}

			.image-info {
				flex: 1;
				font-size: 26rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;

				.info-line {
					margin-bottom: 8rpx;
					display: flex;
					flex-direction: column;

					text {
						&:first-child {
							color: $text-primary;
							margin-bottom: 2rpx;
						}

						&:last-child {
							color: $text-secondary;
							font-size: 24rpx;
						}
					}
				}

				.progress-bar {
					height: 6rpx;
					width: 100%;
					background-color: rgba(7, 193, 96, 0.1);
					border-radius: 3rpx;
					overflow: hidden;
					margin-top: 4rpx;

					.progress-inner {
						height: 100%;
						background: $primary-gradient;
						transition: width 0.2s linear;
					}
				}
			}

			.delete-btn {
				position: absolute;
				right: 10rpx;
				top: 20rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #999;
				font-size: 32rpx;

				&:active {
					opacity: 0.8;
				}
			}
		}

		.tip-text {
			padding: 20rpx 0;
			font-size: 26rpx;
			color: $text-secondary;
			text-align: center;
		}

		.empty-state {
			padding: 50rpx 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			border-radius: 20rpx;
			transition: all 0.3s ease;
			background: linear-gradient(145deg, #ffffff, #f0f0f0);
			box-shadow: 20px 20px 40px rgba(0, 0, 0, 0.15),
						-12px -12px 24px rgba(255, 255, 255, 0.95),
						inset 2px 2px 4px rgba(255, 255, 255, 0.9),
						inset -2px -2px 4px rgba(0, 0, 0, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.8);

			&:active {
				transform: scale(0.98);
				background: linear-gradient(145deg, #f0f0f0, #ffffff);
				box-shadow: inset 10px 10px 20px rgba(0, 0, 0, 0.1),
							inset -10px -10px 20px rgba(255, 255, 255, 0.95);
			}

			.upload-icon {
				width: 120rpx;
				height: 120rpx;
				margin-bottom: 20rpx;
				opacity: 0.8;
				filter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.1));
			}

			text {
				color: $text-primary;
				font-size: 32rpx;
				margin-bottom: 10rpx;
				font-weight: 500;
				text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
			}

			.click-hint {
				color: $text-secondary;
				font-size: 24rpx;
				text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);
			}
		}
	}

	.footer-area {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
		z-index: 100;

		.compress-settings {
			border-radius: 20rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;

			.setting-item {
				margin-bottom: 15rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.setting-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10rpx;

					.setting-label {
						font-size: 28rpx;
						color: $text-primary;
						font-weight: 500;
					}

					.size-controls {
						display: flex;
						align-items: center;
						gap: 10rpx;

						.size-display {
							padding: 4rpx 16rpx;
							border-radius: 12rpx;
							font-size: 28rpx;
							color: $primary-color;
							font-weight: 500;
							display: flex;
							align-items: center;
							justify-content: center;
							min-width: 120rpx;

							&:active {
								opacity: 0.8;
							}
						}

						.setting-value {
							min-width: 60rpx;
							text-align: center;
							font-size: 28rpx;
							color: $primary-color;
							font-weight: 500;
							padding: 4rpx 16rpx;
							border-radius: 12rpx;
						}
					}
				}

				.slider-container {
					padding: 30rpx 20rpx;
					border-radius: 20rpx;

					::v-deep .uni-slider {
						margin: 0;
					}

					::v-deep .uni-slider-handle {
						width: 56rpx;
						height: 56rpx;
						border-radius: 50%;
						box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
					}

					::v-deep .uni-slider-track {
						height: 4rpx;
					}
				}
			}
		}

		.button-group {
			display: flex;
			justify-content: space-between;

			.btn {
				width: 45%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				border-radius: 44rpx;
				font-size: 32rpx;
				font-weight: 500;
				transition: all 0.3s ease;
				border: none;

				&.select-btn {
					color: $primary-color;
					border: 2rpx solid rgba(7, 193, 96, 0.3);
					background: $bg-color;
					box-shadow: 8px 8px 16px $shadow-dark,
								-6px -6px 12px $shadow-light,
								inset 1px 1px 2px rgba(255, 255, 255, 0.5);

					&:active {
						transform: scale(0.98);
						box-shadow: inset 6px 6px 12px $shadow-dark,
									inset -6px -6px 12px $shadow-light;
					}
				}

				&.compress-btn {
					background: $primary-gradient;
					color: #fff;
					box-shadow: 8px 8px 16px rgba(7, 193, 96, 0.2),
								-4px -4px 12px rgba(255, 255, 255, 0.8),
								inset 1px 1px 2px rgba(255, 255, 255, 0.3);

					&:active {
						transform: scale(0.98);
						box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.2);
					}
				}

				&[disabled] {
					opacity: 0.5;
					background: #E5E5E5;
					color: #999999;
				}
			}
		}
	}

	.size-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;

		.popup-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.4);
			backdrop-filter: blur(4px);
		}

		.popup-content {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 80%;
			max-width: 600rpx;
			background: $bg-color;
			border-radius: 30rpx;
			overflow: hidden;

			.popup-header {
				padding: 20rpx;
				text-align: center;
				position: relative;
				border-bottom: 1px solid rgba(0, 0, 0, 0.05);

				.popup-title {
					font-size: 32rpx;
					font-weight: 500;
					color: $text-primary;
				}

				.popup-close {
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					font-size: 40rpx;
					color: $text-secondary;
					padding: 10rpx;
					width: 40rpx;
					height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					&:active {
						opacity: 0.7;
					}
				}
			}

			.popup-body {
				padding: 30rpx;

				.input-group {
					margin-bottom: 20rpx;

					text {
						display: block;
						font-size: 28rpx;
						color: $text-secondary;
						margin-bottom: 8rpx;
					}

					input {
						width: 100%;
						height: 80rpx;
						background: #fff;
						border-radius: 16rpx;
						padding: 0 24rpx;
						font-size: 32rpx;
						color: $text-primary;
						box-sizing: border-box;
						box-shadow: inset 2rpx 2rpx 5rpx rgba(0, 0, 0, 0.1);
					}
				}

				.popup-buttons {
					display: flex;
					gap: 20rpx;
					margin-top: 30rpx;

					button {
						flex: 1;
						height: 80rpx;
						border-radius: 40rpx;
						font-size: 30rpx;
						border: none;

						&.cancel-btn {
							background: rgba(0, 0, 0, 0.05);
							color: $text-secondary;

							&:active {
								opacity: 0.8;
							}
						}

						&.confirm-btn {
							background: $primary-gradient;
							color: #fff;

							&:active {
								opacity: 0.9;
							}
						}
					}
				}
			}
		}
	}

	.share-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;

		.popup-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.4);
			backdrop-filter: blur(4px);
		}

		.popup-content {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 80%;
			max-width: 600rpx;
			background: $bg-color;
			border-radius: 30rpx;
			overflow: hidden;

			.popup-header {
				padding: 20rpx;
				text-align: center;
				position: relative;
				border-bottom: 1px solid rgba(0, 0, 0, 0.05);

				.popup-title {
					font-size: 32rpx;
					font-weight: 500;
					color: $text-primary;
				}

				.popup-close {
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					font-size: 40rpx;
					color: $text-secondary;
					padding: 10rpx;
					width: 40rpx;
					height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					&:active {
						opacity: 0.7;
					}
				}
			}

			.popup-body {
				padding: 30rpx;

				.share-content {
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 30rpx;

					.share-icon {
						width: 120rpx;
						height: 120rpx;
						margin-bottom: 20rpx;
					}

					.share-title {
						font-size: 32rpx;
						font-weight: 500;
						color: $text-primary;
						margin-bottom: 10rpx;
					}

					.share-desc {
						font-size: 24rpx;
						color: $text-secondary;
					}
				}

				.share-buttons {
					display: flex;
					gap: 20rpx;

					.share-btn {
						flex: 1;
						height: 80rpx;
						border-radius: 40rpx;
						font-size: 30rpx;
						background: $primary-gradient;
						color: #fff;
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 10rpx;
						border: none;
						transition: all 0.3s ease;

						&:active {
							opacity: 0.9;
						}

						.iconfont {
							font-size: 36rpx;
						}
					}

					.cancel-btn {
						flex: 1;
						height: 80rpx;
						border-radius: 40rpx;
						font-size: 30rpx;
						background: rgba(0, 0, 0, 0.03);
						color: $text-secondary;
						border: none;
						transition: all 0.3s ease;

						&:active {
							opacity: 0.8;
						}
					}
				}
			}
		}
	}
}
</style>