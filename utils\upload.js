async function uploadChunks(originalData, chunkSize) {
    const moduleOrders = originalData.RESUME_DATA.moduleOrders;
    const totalChunks = Math.ceil(moduleOrders.length / chunkSize);
  
    // 存储所有分块的 Promise
    const uploadPromises = [];
  
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = start + chunkSize;
      const chunkData = moduleOrders.slice(start, end);
  
      // 构造分块数据
      const chunkPayload = {
        ...originalData,
        RESUME_DATA: {
          ...originalData.RESUME_DATA,
          moduleOrders: chunkData
        },
        chunkInfo: { // 分块元信息
          chunkIndex,
          totalChunks,
          isLastChunk: chunkIndex === totalChunks - 1
        }
      };
  
      // 发起分块请求
      uploadPromises.push(
        new Promise((resolve, reject) => {
          wx.request({
            url: 'http://service',
            method: 'POST',
            header: { 'Content-Type': 'application/json' },
            data: chunkPayload,
            success: (res) => {
              if (res.statusCode !== 200) {
                reject(new Error(`分块 ${chunkIndex} 上传失败`));
                return;
              }
              console.log(`分块 ${chunkIndex} 上传成功`);
              resolve(res.data);
            },
            fail: (err) => reject(err)
          });
        })
      );
    }
  
    // 等待所有分块上传完成
    return Promise.all(uploadPromises);
  }