// 引入API模块
const resumeApi = require('../../../../utils/api/resumeApi');

Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;

        this.setData({
          currentResumeData: newVal
        });

        // 简历数据变更时清除PDF缓存
        this.clearPDFCache();
      }
    },
    template: {
      type: Object,
      value: {
        id: 'templateA01',
        name: '模板一'
      },
      observer: function(newVal) {
        if (newVal && newVal.id) {
          this.setData({
            currentTemplate: newVal.id
          });
        }
      }
    },
    config: {
      type: Object,
      value: {
        themeColor: '#2B6CB0',
        fontSize: '11',
        spacing: '1.2'
      },
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;

        const needUpdate =
          newVal.themeColor !== oldVal.themeColor ||
          newVal.fontSize !== oldVal.fontSize ||
          newVal.spacing !== oldVal.spacing;

        if (needUpdate) {
          this.updateStyle(newVal);
          // 配置变更时清除PDF缓存
          this.clearPDFCache();
        }
      }
    }
  },

  data: {
    currentTemplate: 'templateA01',
    currentResumeData: {},
    customStyle: '',
    lastConfig: null,
    // 图片预览相关
    previewImageUrl: '',
    imageLoading: false,
    imageError: false,
    // PDF缓存相关
    pdfFilePath: '',
    pdfDownloading: false,
    pdfError: false
  },

  methods: {
    updateStyle(config) {
      if (!config) return;

      let fontSize = '14pt';
      if (config.fontSize) {
        const size = parseInt(config.fontSize.toString().replace('px', ''));
        fontSize = size + 'pt';
      }

      const style = `
        --theme-color: ${config.themeColor || '#4B6CB0'};
        --font-size: ${fontSize};
        --spacing: ${config.spacing || 1};
      `;

      this.setData({
        customStyle: style,
        lastConfig: Object.assign({}, config)
      });

      const templateId = this.data.currentTemplate;
      const template = this.selectComponent(`#${templateId}`);
      if (template) {
        template.updateStyle(Object.assign({}, config, {
          fontSize: fontSize
        }));
      }
    },

    getResumeRenderData() {
      return {
        template: {
          id: this.data.currentTemplate,
          styles: {}
        },
        resumeData: this.data.currentResumeData,
        config: this.data.lastConfig || this.properties.config,
        customStyle: this.data.customStyle
      };
    },

    // 简单的预览图片请求方法（父页面调用）
    debounceRequestPreviewImage() {
      this.requestPreviewImage();

      // 预览图片生成成功后，启动后台PDF下载
      console.log('预览图片生成成功，开始后台下载PDF...');
      setTimeout(() => {
        this.backgroundDownloadPDF();
      }, 500); // 延迟500ms启动，避免同时请求过多
    },

    // 请求服务端生成预览图片
    requestPreviewImage() {
      const currentTemplate = this.data.currentTemplate;
      const currentResumeData = this.data.currentResumeData;
      const lastConfig = this.data.lastConfig;
      if (!currentTemplate || !currentResumeData || !lastConfig) {
        return;
      }

      this.setData({
        imageLoading: true,
        imageError: false,
        previewImageUrl: ''
      });

      // 调用API生成预览图片
      resumeApi.generatePreviewImage(currentResumeData, lastConfig, currentTemplate)
        .then(response => {

          if (response.success && response.data && response.data.image_url) {
            console.log('设置图片URL:', response.data.image_url);

            // 临时测试：如果是本地IP，提示用户配置域名白名单
            const imageUrl = response.data.image_url;
            // if (imageUrl.includes('192.168.') || imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
            //   console.warn('⚠️ 检测到本地IP图片URL，可能需要配置域名白名单');
            //   console.warn('请在微信开发者工具中：详情 -> 本地设置 -> 勾选"不校验合法域名"');
            // }

            this.setData({
              previewImageUrl: imageUrl,
              imageLoading: false,
              imageError: false
            });
          } else {
            console.error('服务器返回数据格式错误:', response);
            throw new Error('服务器返回数据格式错误');
          }
        })
        .catch(error => {
          console.error('预览图片生成失败:', error);
          this.setData({
            imageLoading: false,
            imageError: true,
            previewImageUrl: ''
          });
        });
    },

    // 图片加载成功
    onImageLoad() {
      this.setData({ imageError: false });
    },

    // 图片加载失败
    onImageError(e) {
      console.error('预览图片加载失败:', e);
      console.error('当前图片URL:', this.data.previewImageUrl);

      // 检查是否是域名白名单问题
      const imageUrl = this.data.previewImageUrl || '';
      if (imageUrl.includes('192.168.') || imageUrl.includes('localhost') || imageUrl.includes('127.0.0.1')) {
        console.error('🚨 图片加载失败可能是域名白名单问题！');
        console.error('解决方案：微信开发者工具 -> 详情 -> 本地设置 -> 勾选"不校验合法域名"');
      }

      this.setData({
        imageError: true,
        imageLoading: false
      });
    },

    // 重新生成预览
    retryGeneratePreview() {
      this.requestPreviewImage();
    },

    // 后台下载PDF
    backgroundDownloadPDF() {
      const currentTemplate = this.data.currentTemplate;
      const currentResumeData = this.data.currentResumeData;
      const lastConfig = this.data.lastConfig;

      if (!currentTemplate || !currentResumeData || !lastConfig) {
        console.log('PDF下载条件不满足，跳过后台下载');
        return;
      }

      // 如果已经在下载中或已有缓存文件，跳过
      if (this.data.pdfDownloading || this.data.pdfFilePath) {
        console.log('PDF已在下载中或已缓存，跳过重复下载');
        return;
      }

      console.log('开始后台下载PDF...');
      this.setData({
        pdfDownloading: true,
        pdfError: false
      });

      // 调用API生成PDF
      resumeApi.generatePDF(currentResumeData, lastConfig, currentTemplate)
        .then(response => {
          if (response.success && response.data && response.data.pdf_url) {
            console.log('PDF生成成功，开始下载:', response.data.pdf_url);

            // 获取简历名称，用于文件命名
            const resumeName = wx.getStorageSync('resumeName') || 'resume';
            const safeResumeName = resumeName.replace(/[\\/:*?"<>|]/g, '_');
            const fileName = `${safeResumeName}.pdf`;
            const finalFilePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

            // 下载PDF文件
            wx.downloadFile({
              url: response.data.pdf_url,
              filePath: finalFilePath,
              success: (res) => {
                if (res.statusCode === 200) {
                  console.log('PDF后台下载成功，文件路径:', res.filePath);
                  this.setData({
                    pdfFilePath: res.filePath,
                    pdfDownloading: false,
                    pdfError: false
                  });

                  // 通知父页面PDF已准备就绪
                  this.triggerEvent('pdfReady', {
                    filePath: res.filePath,
                    fileName: fileName
                  });
                } else {
                  console.error(`PDF下载失败，状态码: ${res.statusCode}`);
                  this.setData({
                    pdfDownloading: false,
                    pdfError: true
                  });
                }
              },
              fail: (error) => {
                console.error('PDF下载失败:', error);
                this.setData({
                  pdfDownloading: false,
                  pdfError: true
                });
              }
            });
          } else {
            console.error('PDF生成失败，服务器返回数据格式错误:', response);
            this.setData({
              pdfDownloading: false,
              pdfError: true
            });
          }
        })
        .catch(error => {
          console.error('PDF生成过程中发生错误:', error);
          this.setData({
            pdfDownloading: false,
            pdfError: true
          });
        });
    },

    // 获取PDF文件路径（供父页面调用）
    getPDFFilePath() {
      return this.data.pdfFilePath;
    },

    // 检查PDF是否已准备就绪
    isPDFReady() {
      return !this.data.pdfDownloading && !this.data.pdfError && this.data.pdfFilePath;
    },

    // 清除PDF缓存
    clearPDFCache() {
      this.setData({
        pdfFilePath: '',
        pdfDownloading: false,
        pdfError: false
      });
    }
  }
});
