# 证件照分步处理API测试文档

## 测试目标
验证新的分步处理API调用流程是否正常工作

## 修改内容总结

### 1. API配置更新 (config/apiConfig.js)
- **新增路由**: 
  - `idPhotoGenerateTransparentUrl: '/idphoto/generate_transparent'` - 生成透明背景证件照
  - `idPhotoGenerateAddColorUrl: '/idphoto/generate_add_color'` - 为透明证件照添加背景色

### 2. API工具类更新 (utils/api/idPhotoAPI.js)
- **新增函数**:
  - `generateTransparentPhoto()` - 生成透明背景证件照（第一步）
  - `generateAddColor()` - 为透明证件照添加背景色（第二步）
- **保留兼容**: `generateIDPhoto()` 保留旧版本兼容性

### 3. 结果页面更新 (pages/idPhoto/result/result.js)
- **processPhoto方法**: 使用 `generateTransparentPhoto` 生成透明背景照片
- **saveToAlbum方法**: 使用 `generateAddColor` 为透明照片添加背景色
- **新增辅助函数**: `base64ToTempFile()` 将base64转换为临时文件

## 新的处理流程

### 第一步：生成透明背景证件照
1. 用户选择照片和尺寸
2. 调用 `generateTransparentPhoto` 接口
3. 服务端返回透明背景的标准版和高清版照片
4. 前端显示透明背景照片，用户可预览不同背景色效果

### 第二步：添加背景色（保存时）
1. 用户选择背景色并点击保存
2. 如果选择透明背景，直接保存透明照片
3. 如果选择其他颜色：
   - 将透明背景照片转换为临时文件
   - 调用 `generateAddColor` 接口添加背景色
   - 保存最终的带背景色照片

## 测试步骤

### 测试1: 透明背景照片生成
1. 打开证件照制作页面
2. 选择尺寸并上传照片
3. 验证是否调用 `generateTransparentPhoto` 接口
4. 验证是否正确显示透明背景照片
5. 验证背景色预览是否正常

### 测试2: 背景色添加
1. 在结果页面选择非透明的背景色
2. 点击保存到相册
3. 验证是否调用 `generateAddColor` 接口
4. 验证是否正确保存带背景色的照片

### 测试3: 透明背景保存
1. 在结果页面选择透明背景
2. 点击保存到相册
3. 验证是否直接保存透明背景照片（不调用添加背景色接口）

## API接口对比

### 旧版本（单步处理）
```javascript
// 一次性生成带背景色的证件照
const result = await idPhotoAPI.generateIDPhoto({
  imagePath: imagePath,
  size: 'one_inch',
  color: 'white'
});
```

### 新版本（分步处理）
```javascript
// 第一步：生成透明背景证件照
const transparentResult = await idPhotoAPI.generateTransparentPhoto({
  imagePath: imagePath,
  size: 'one_inch'
});

// 第二步：添加背景色（保存时）
const finalResult = await idPhotoAPI.generateAddColor({
  imagePath: transparentImagePath,
  color: 'white'
});
```

## 预期优势

1. **更好的用户体验**: 用户可以先看到透明背景的证件照效果，确认人像处理是否满意
2. **灵活的颜色选择**: 前端可以实时预览不同背景色效果，无需重复处理人像
3. **减少服务器负载**: 避免因更换背景色而重复进行人像分割处理
4. **更快的响应速度**: 第二步只需要添加背景色，处理速度更快

## 注意事项

1. **网络请求**: 确保两个新接口的网络请求正常
2. **文件转换**: 验证base64到临时文件的转换是否正确
3. **错误处理**: 检查各种异常情况的处理
4. **兼容性**: 确保旧版本API仍然可用
5. **权限**: 验证文件系统权限是否正常
