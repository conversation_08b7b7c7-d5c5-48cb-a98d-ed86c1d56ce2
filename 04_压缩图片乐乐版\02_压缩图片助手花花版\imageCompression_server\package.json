{"name": "image-compression-server", "version": "1.0.0", "description": "图像压缩小程序后端服务 - 支持多商户的用户管理和会员系统", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "node test-api.js", "miniapps:list": "node scripts/listMerchants.js", "miniapps:validate": "node scripts/validateMerchants.js", "miniapps:validate:single": "node scripts/validateMerchants.js", "products:init": "node scripts/initProducts.js init", "products:show": "node scripts/initProducts.js show", "apps:setup": "node scripts/setupApps.js setup", "apps:status": "node scripts/setupApps.js status", "deploy:init": "node scripts/deploy.js init", "deploy:status": "node scripts/deploy.js status", "deploy:all": "node scripts/deploy.js all"}, "keywords": ["image-compression", "wechat-miniprogram", "membership", "multi-merchant", "express", "mysql"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "moment": "^2.29.4", "mysql2": "^3.6.5", "node-cron": "^3.0.3", "uuid": "^9.0.1", "wechatpay-axios-plugin": "^0.8.14"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}