#!/usr/bin/env node

/**
 * 小程序配置设置脚本
 * 帮助快速配置真实的AppID和商户信息
 */

const fs = require('fs');
const path = require('path');
// 加载环境变量
require('dotenv').config();
const { query } = require('../config/database');

/**
 * 更新小程序配置
 * @param {string} appName 小程序名称
 * @param {Object} config 配置信息
 */
async function updateAppConfig(appName, config) {
  try {
    const configPath = path.join(__dirname, '../miniapps', appName, 'config.json');
    
    if (!fs.existsSync(configPath)) {
      console.log(`❌ 配置文件不存在: ${configPath}`);
      return false;
    }

    // 读取现有配置
    const existingConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    // 备份原AppID
    const oldAppId = existingConfig.appId;
    
    // 更新配置
    const updatedConfig = {
      ...existingConfig,
      appId: config.appId,
      merchant: {
        merchantId: config.merchantId || existingConfig.merchant.merchantId,
        merchantName: config.merchantName || existingConfig.merchant.merchantName
      },
      wechat: {
        ...existingConfig.wechat,
        appId: config.appId,
        appSecret: config.appSecret || existingConfig.wechat.appSecret,
        mchId: config.merchantId || existingConfig.wechat.mchId
      },
      updatedAt: new Date().toISOString()
    };

    // 写入配置文件
    fs.writeFileSync(configPath, JSON.stringify(updatedConfig, null, 2), 'utf8');
    
    console.log(`✅ 更新配置: ${appName}`);
    console.log(`   旧AppID: ${oldAppId}`);
    console.log(`   新AppID: ${config.appId}`);
    
    // 更新数据库中的商品AppID
    if (oldAppId !== config.appId) {
      await updateProductsAppId(oldAppId, config.appId);
    }
    
    return true;

  } catch (error) {
    console.error(`❌ 更新配置失败 ${appName}:`, error.message);
    return false;
  }
}

/**
 * 更新数据库中商品的AppID
 * @param {string} oldAppId 旧AppID
 * @param {string} newAppId 新AppID
 */
async function updateProductsAppId(oldAppId, newAppId) {
  try {
    const result = await query(
      'UPDATE products SET app_id = ? WHERE app_id = ?',
      [newAppId, oldAppId]
    );
    
    if (result.affectedRows > 0) {
      console.log(`   📦 更新了 ${result.affectedRows} 个商品的AppID`);
    }
  } catch (error) {
    console.error(`   ⚠️ 更新商品AppID失败:`, error.message);
  }
}

/**
 * 批量配置示例
 */
async function setupExampleConfigs() {
  console.log('🚀 开始配置示例小程序...\n');

  const configs = [
    {
      appName: 'imagecomp_huahua',
      appId: 'wx123456789abcdef1',  // 请替换为真实AppID
      merchantId: '1234567890',
      merchantName: '花花科技有限公司',
      appSecret: 'your_real_app_secret_here'
    },
    {
      appName: 'compress_assistant_huahua',
      appId: 'wx123456789abcdef2',  // 请替换为真实AppID
      merchantId: '1234567890',
      merchantName: '花花科技有限公司',
      appSecret: 'your_real_app_secret_here'
    },
    {
      appName: 'imagecomp_wangwang',
      appId: 'wx123456789abcdef3',  // 请替换为真实AppID
      merchantId: '0987654321',
      merchantName: '汪汪科技有限公司',
      appSecret: 'your_real_app_secret_here'
    }
  ];

  let successCount = 0;
  
  for (const config of configs) {
    const success = await updateAppConfig(config.appName, config);
    if (success) {
      successCount++;
    }
    console.log('');
  }

  console.log(`📊 配置完成: ${successCount}/${configs.length} 个小程序`);
  console.log('');
  console.log('⚠️ 重要提醒:');
  console.log('1. 请将示例AppID替换为真实的微信小程序AppID');
  console.log('2. 请填写真实的AppSecret和商户信息');
  console.log('3. 配置完成后运行: npm run products:init');
}

/**
 * 显示当前配置状态
 */
async function showCurrentStatus() {
  console.log('📋 当前小程序配置状态');
  console.log('========================\n');

  const miniappsDir = path.join(__dirname, '../miniapps');
  const appDirs = fs.readdirSync(miniappsDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory() && dirent.name !== 'README.md')
    .map(dirent => dirent.name);

  for (const appName of appDirs) {
    try {
      const configPath = path.join(miniappsDir, appName, 'config.json');
      if (fs.existsSync(configPath)) {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        const status = config.isActive ? '🟢 激活' : '🔴 停用';
        const configured = config.appId.includes('wx_') && !config.appId.includes('001') ? '⚠️ 需配置' : '✅ 已配置';
        
        console.log(`📱 ${config.businessName}`);
        console.log(`   文件夹: ${appName}`);
        console.log(`   AppID: ${config.appId}`);
        console.log(`   商户: ${config.merchant.merchantName}`);
        console.log(`   状态: ${status}`);
        console.log(`   配置: ${configured}`);
        console.log('');
      }
    } catch (error) {
      console.log(`❌ 读取配置失败: ${appName}`);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'setup':
        await setupExampleConfigs();
        break;
        
      case 'status':
        await showCurrentStatus();
        break;
        
      default:
        console.log('📖 小程序配置脚本使用说明:');
        console.log('');
        console.log('命令:');
        console.log('  node scripts/setupApps.js setup   # 配置示例AppID（需要手动修改）');
        console.log('  node scripts/setupApps.js status  # 显示当前配置状态');
        console.log('');
        console.log('配置流程:');
        console.log('1. 运行 setup 命令生成示例配置');
        console.log('2. 手动编辑配置文件，填入真实AppID和密钥');
        console.log('3. 运行 npm run products:init 初始化商品');
        console.log('4. 运行 status 命令检查配置状态');
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  updateAppConfig,
  setupExampleConfigs,
  showCurrentStatus
};
