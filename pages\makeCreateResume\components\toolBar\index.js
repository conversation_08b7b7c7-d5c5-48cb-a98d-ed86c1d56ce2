Component({
  properties: {
    config: {
      type: Object,
      value: {
        fontSize: 11,
        spacing: 1.5
      },
      observer(newVal) {
        // 当config变化时，更新currentFontSize和currentSpacing
        if (newVal.fontSize) {
          this.setData({
            currentFontSize: parseFloat(newVal.fontSize)
          });
        }
        if (newVal.spacing !== undefined) {
          this.setData({
            currentSpacing: newVal.spacing
          });
        }
      }
    }
  },

  data: {
    colors: ['#608BC1', '#133E87', '#5C7AEA', '#FF8210', '#CD4DCC', '#F12D2D'],
    colorNames: ['扬帆起航', '深蓝智慧', '青云直上', '朝阳似火', '紫气东来', '锦绣前程'],
    showThemePopup: false,
    showSpacingPopup: false,
    showRenamePopup: false,  // 新增：控制重命名弹窗显示
    resumeName: '',          // 新增：简历名称
    currentFontSize: 11,      // 当前字号大小
    currentSpacing: 1.5        // 当前行间距
  },

  methods: {
    // 主题色相关方法
    onThemeClick() {
      this.setData({
        showThemePopup: true
      });
    },

    onThemeClose() {
      this.setData({
        showThemePopup: false
      });
    },

    onThemeSelect(e) {
      const color = e.currentTarget.dataset.color;
      this.triggerEvent('configChange', { field: 'themeColor', value: color });
      this.onThemeClose();
    },

    // 字号间距相关方法
    onSpacingClick() {
      this.setData({
        showSpacingPopup: true
      });
    },

    onSpacingClose() {
      this.setData({
        showSpacingPopup: false
      });
    },

    onFontSizeChange(e) {
      const fontSize = e.detail.value;
      console.log('fontSize change:', fontSize);
      this.setData({
        currentFontSize: e.detail.value
      });
      this.triggerEvent('configChange', { field: 'fontSize', value: fontSize });
    },

    onSpacingChange(e) {
      const spacing = e.detail.value;
      this.setData({
        currentSpacing: spacing
      });
      this.triggerEvent('configChange', { field: 'spacing', value: spacing });
    },

    // 重命名相关方法
    onRename() {
      this.setData({
        showRenamePopup: true,
        resumeName: ''  // 清空输入框
      });
    },

    onRenameClose() {
      this.setData({
        showRenamePopup: false
      });
    },

    onRenameInput(e) {
      this.setData({
        resumeName: e.detail.value
      });
    },

    onRenameConfirm() {
      const name = this.data.resumeName.trim();
      if (!name) {
        wx.showToast({
          title: '请输入简历名称',
          icon: 'none'
        });
        return;
      }

      // 触发重命名事件，将新名称传递给父组件
      this.triggerEvent('rename', { name });

      // 关闭弹窗
      this.onRenameClose();

      // 显示成功提示
      wx.showToast({
        title: '重命名成功',
        icon: 'success'
      });
    },

    // 其他功能
    onCoverToggle() {
      // 显示"正在开发中..."提示
      wx.showToast({
        title: '正在开发中...',
        icon: 'none',
        duration: 800
      });

      // 注释掉原有的事件触发，因为功能还在开发中
      // this.triggerEvent('coverToggle');
    },

    onDownload() {
      this.triggerEvent('download');
    }
  }
});