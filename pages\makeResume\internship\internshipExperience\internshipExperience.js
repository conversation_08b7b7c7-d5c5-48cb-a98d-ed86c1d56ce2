const app = getApp();
const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
Page({
  data: {
    internshipFormData: [],
    currentIndex: -1,
    startY: 0,
    moveY: 0
  },
  onLoad() {
    this.loadInternshipList();
  },
  /**
   * 从全局管理器加载实习经历列表
   */
  loadInternshipList() {
    try {
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();
      let internshipFormData = [];
      if (currentResume && currentResume.internship && Array.isArray(currentResume.internship)) {
        internshipFormData = currentResume.internship.map(item =>
          typeof item.toObject === 'function' ? item.toObject() : item
        );
        console.log('✅ 实习经历列表加载成功:', internshipFormData.length, '条');
      } else {
        console.log('📝 没有实习经历数据');
      }
      this.setData({
        internshipFormData: internshipFormData
      });
    } catch (error) {
      console.error('❌ 加载实习经历列表失败:', error);
      this.setData({
        internshipFormData: []
      });
    }
  },
  // 长按触发移动
  handleLongPress(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY
    });
    wx.vibrateShort(); // 添加震动反馈
  },
  // 触摸移动中
  touchMove(e) {
    if (this.data.currentIndex < 0) return;
    const moveY = e.touches[0].clientY;
    const moveDistance = moveY - this.data.startY;
    // 计算目标位置
    const itemHeight = 50; // 每个项目的高度，根据实际调整
    const moveIndex = Math.round(moveDistance / itemHeight);
    let targetIndex = this.data.currentIndex + moveIndex;
    targetIndex = Math.max(0, Math.min(targetIndex, this.data.internshipFormData.length - 1));
    if (targetIndex !== this.data.currentIndex) {
      // 交换位置
      const internshipFormData = [...this.data.internshipFormData];
      const temp = internshipFormData[this.data.currentIndex];
      internshipFormData[this.data.currentIndex] = internshipFormData[targetIndex];
      internshipFormData[targetIndex] = temp;
      this.setData({
        internshipFormData,
        currentIndex: targetIndex,
        startY: moveY
      });
    }
  },
  /**
   * 触摸结束 - 保存排序后的数据
   */
  touchEnd() {
    if (this.data.currentIndex >= 0) {
      try {
        // 更新所有项的sortIndex
        const internshipFormData = this.data.internshipFormData.map((item, index) => ({
          ...item,
          sortIndex: index
        }));
        // 保存排序后的数据
        this.setData({
          internshipFormData,
          currentIndex: -1,
          startY: 0,
          moveY: 0
        });
        // 保存到全局管理器
        const success = ResumeFormHelper.saveFieldData('internship', internshipFormData, app);
        if (success) {
        } else {
        }
      } catch (error) {
        console.error('❌ 保存排序数据失败:', error);
      }
    }
  },
  // 添加实习经历
  addInternship() {
    wx.navigateTo({
      url: '../internshipEdit/internshipEdit'
    });
  },
  // 编辑实习经历
  editInternship(e) {
    const index = e.currentTarget.dataset.index;
    wx.navigateTo({
      url: `../internshipEdit/internshipEdit?index=${index}`
    });
  },
  /**
   * 删除实习经历
   */
  deleteInternship(e) {
    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '提示',
      content: '确定要删除这条实习经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            console.log('=== 删除实习经历 ===');
            console.log('删除索引:', index);
            const internshipFormData = [...this.data.internshipFormData];
            internshipFormData.splice(index, 1);
            // 保存到全局管理器
            const success = ResumeFormHelper.saveFieldData('internship', internshipFormData, app);
            if (success) {
              this.setData({ internshipFormData });
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('❌ 删除实习经历失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },
  /**
   * 页面显示时刷新数据
   */
  onShow() {
    console.log('=== 实习经历列表页面显示 ===');
    this.loadInternshipList();
  }
});