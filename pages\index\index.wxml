<view class="container">
  <!-- 顶部标题 -->
  <!-- <view class="header">
    <text class="title">小程序(个人简历模板简历制作)</text>
  </view> -->
  


  <!-- 图片容器 -->
  <view class="image-container">
    <image class="header-image" src="./images/touXiang.png" mode="aspectFit"></image>
  </view>
  
  <!-- 功能区域 -->
  <view class="function-area">
    <!-- 第一行 -->
    <view class="function-row">
      <view class="function-item" bindtap="toMakeResume">
        <image class="icon" src="./images/makeResume.svg" mode="aspectFit"></image>
        <text>制作简历</text>
      </view>
      <view class="function-item" bindtap="toResumeStyle">
        <image class="icon" src="./images/ResumeStyle.svg" mode="aspectFit"></image>
        <text>简历样式</text>
      </view>
    </view>
    <!-- 第二行 -->
    <view class="function-row">
      <view class="function-item" bindtap="toIDPhoto">
        <image class="icon" src="./images/idPhoto.svg" mode="aspectFit"></image>
        <text>证件照制作</text>
      </view>
      <view class="function-item" bindtap="toWordTemplate">
        <image class="icon" src="./images/freeResume.svg" mode="aspectFit"></image>
        <text>免费模板</text>
      </view>
    </view>
    <!-- 第三行 -->
    <view class="function-row">
      <view class="function-item" bindtap="toProfile">
        <image class="icon" src="./images/touXiang.png" mode="aspectFit"></image>
        <text>我的</text>
      </view>
      <view class="function-item">
        <!-- 空白占位 -->
      </view>
    </view>
  </view>
</view> 