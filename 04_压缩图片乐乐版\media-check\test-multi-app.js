/**
 * 多小程序测试脚本
 * 用于测试多个微信小程序的内容安全检测API
 */
const axios = require('axios');
const config = require('./config');

// 测试配置
const testConfig = {
  apiUrl: 'http://localhost:8848/api/security-check',
  mediaUrl: 'https://example.com/test-image.jpg', // 替换为实际的媒体URL
  mediaType: 2 // 2表示图片
};

/**
 * 测试特定小程序的内容安全检测API
 * @param {string} appId 小程序AppID
 * @param {string} appName 小程序名称
 */
async function testAppSecurityCheck(appId, appName) {
  try {
    console.log(`开始测试小程序[${appName}]的内容安全检测API...`);
    
    const response = await axios.post(testConfig.apiUrl, {
      mediaUrl: testConfig.mediaUrl,
      mediaType: testConfig.mediaType,
      appId: appId
    });
    
    console.log(`小程序[${appName}]API响应:`, JSON.stringify(response.data, null, 2));
    
    if (response.data.code === 0) {
      console.log(`小程序[${appName}]测试成功!`);
    } else {
      console.error(`小程序[${appName}]测试失败:`, response.data.msg);
    }
    
    console.log('-----------------------------------');
  } catch (error) {
    console.error(`小程序[${appName}]测试出错:`, error.message);
    if (error.response) {
      console.error('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
    console.log('-----------------------------------');
  }
}

/**
 * 测试所有配置的小程序
 */
async function testAllApps() {
  console.log('开始测试所有配置的小程序...');
  console.log('===================================');
  
  // 测试默认小程序配置
  await testAppSecurityCheck(config.wechat.appId, '默认小程序');
  
  // 测试所有配置的小程序
  for (const app of config.wechat.apps) {
    await testAppSecurityCheck(app.appId, app.name);
  }
  
  console.log('所有小程序测试完成!');
}

// 执行测试
testAllApps();
