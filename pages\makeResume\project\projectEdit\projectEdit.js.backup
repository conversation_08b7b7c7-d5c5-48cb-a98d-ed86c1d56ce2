const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    projectEditFormData: null, // 将在 onLoad 中初始化为 ProjectItem 实例
    isEdit: false,
    editIndex: -1,
    currentDate: '' // 当前日期，用于"至今"后重新选择日期时的默认值
  },

  onLoad(options) {
    console.log('=== 项目经历编辑页面加载 ===');

    // 初始化当前日期（格式：YYYY-MM）
    const now = new Date();
    const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    this.setData({
      currentDate: currentDate
    });

    if (options.index !== undefined) {
      // 编辑模式
      const index = parseInt(options.index);
      this.setData({
        isEdit: true,
        editIndex: index
      });
      this.loadProjectData(index);
    } else {
      // 新增模式
      this.loadProjectData();
    }
  },

  /**
   * 加载项目经历数据
   */
  loadProjectData(index = -1) {
    try {
      let projectData;

      if (index >= 0) {
        // 编辑模式：从全局管理器获取指定索引的项目经历
        const resumeManager = app.getResumeManager();
        const currentResume = resumeManager.getCurrentResume();

        if (currentResume && currentResume.project && currentResume.project[index]) {
          projectData = currentResume.project[index].toObject();
          console.log('✅ 加载项目经历数据成功:', projectData);
        } else {
          console.warn('⚠️ 指定索引的项目经历不存在，使用空数据');
          projectData = ResumeFormHelper.getEmptyFieldData('projectItem');
        }
      } else {
        // 新增模式：使用空的 ProjectItem 实例
        projectData = ResumeFormHelper.getEmptyFieldData('projectItem');
        console.log('📝 创建空的项目经历数据:', projectData);
      }

      this.setData({
        projectEditFormData: projectData
      });

    } catch (error) {
      console.error('❌ 加载项目经历数据失败:', error);
      // 出错时使用空数据
      const emptyData = ResumeFormHelper.getEmptyFieldData('projectItem');
      this.setData({
        projectEditFormData: emptyData
      });
    }
  },

  // 处理输入
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`projectEditFormData.${field}`]: e.detail.value
    });
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`projectEditFormData.${field}`]: e.detail.value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'projectEditFormData.endDate': '至今'
    });
  },

  /**
   * 保存项目经历信息
   */
  saveInfo() {
    try {
      console.log('=== 保存项目经历信息 ===');
      const { projectEditFormData, isEdit, editIndex } = this.data;
      console.log('保存的数据:', projectEditFormData);

      // 使用 ResumeFormHelper 进行数据验证
      const errors = ResumeFormHelper.validateFieldData('projectItem', projectEditFormData);
      if (errors.length > 0) {
        wx.showToast({
          title: errors[0], // 显示第一个错误
          icon: 'none'
        });
        return;
      }

      // 获取当前简历的项目经历数组
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();
      let projectList = currentResume.project ?
        currentResume.project.map(item => item.toObject()) : [];

      if (isEdit && editIndex >= 0) {
        // 编辑模式：更新指定索引的数据
        projectList[editIndex] = projectEditFormData;
        console.log('✅ 更新项目经历，索引:', editIndex);
      } else {
        // 新增模式：添加到数组末尾
        projectList.push(projectEditFormData);
        console.log('✅ 新增项目经历');
      }

      // 保存整个项目经历数组
      const success = ResumeFormHelper.saveFieldData('project', projectList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 项目经历保存成功');

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('❌ 保存项目经历失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除项目经历
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该项目经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            console.log('=== 删除项目经历 ===');
            const { editIndex } = this.data;
            console.log('删除索引:', editIndex);

            // 获取当前简历的项目经历数组
            const resumeManager = app.getResumeManager();
            const currentResume = resumeManager.getCurrentResume();
            let projectList = currentResume.project ?
              currentResume.project.map(item => item.toObject()) : [];

            // 删除指定索引的项目经历
            projectList.splice(editIndex, 1);

            // 保存更新后的数组
            const success = ResumeFormHelper.saveFieldData('project', projectList, app);

            if (success) {
              console.log('✅ 项目经历删除成功');
              wx.navigateBack({
                success: () => {
                  wx.showToast({
                    title: '删除成功',
                    icon: 'success'
                  });
                }
              });
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }

          } catch (error) {
            console.error('❌ 删除项目经历时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});