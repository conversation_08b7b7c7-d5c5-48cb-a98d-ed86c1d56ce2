<view class="container3">
  <!-- 顶部标题已经在导航栏显示 -->

  <!-- 名称输入区域 -->
  <view class="formItem3">
    <view class="label3 required">自定义</view>
    <input class="input3"
           placeholder="请输入"
           value="{{custom3FormData.customName}}"
           bindinput="handleNameInput"/>
  </view>

  <!-- 时间选择区域 -->
  <view class="formItem3">
    <view class="label3">时间</view>
    <view class="datePicker3">
      <view class="dateSection3">
        <picker mode="date" fields="month" value="{{custom3FormData.startDate}}" bindchange="handleStartDateChange">
          <view class="picker3 {{custom3FormData.startDate ? '' : 'placeholder3'}}">
            {{custom3FormData.startDate || '请选择'}}
          </view>
        </picker>
      </view>
      <text class="separator3">至</text>
      <view class="dateSection3">
        <picker mode="date" fields="month" value="{{custom3FormData.endDate === '至今' ? currentDate : custom3FormData.endDate}}" bindchange="handleEndDateChange">
          <view class="picker3 {{custom3FormData.endDate ? '' : 'placeholder3'}}">
            {{custom3FormData.endDate || '请选择'}}
          </view>
        </picker>
      </view>
      <view class="nowBtn3" bindtap="setToNow">至今</view>
    </view>
  </view>

  <!-- 角色输入区域 -->
  <view class="formItem3">
    <view class="label3">角色</view>
    <input class="input3"
           placeholder="请输入(选填)"
           value="{{custom3FormData.role}}"
           bindinput="handleRoleInput"/>
  </view>

  <!-- 自定义内容区域 -->
  <view class="formItem3 contentArea3">
    <view class="label3">自定义内容</view>
    <textarea class="contentInput3"
              placeholder="请输入自定义的详细内容"
              value="{{custom3FormData.content}}"
              bindinput="handleContentInput"
              maxlength="-1"
              auto-height/>

  </view>

  <!-- 底部按钮组 -->
  <view class="buttonGroup3">
    <button class="saveBtn3" bindtap="saveContent">保存信息</button>
    <button class="deleteBtn3" bindtap="deleteContent">删除</button>
  </view>
</view>