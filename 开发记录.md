# 开发记录

## 2024-01-XX - 简历模板切换预览图修复

### 问题描述
用户反馈在微信端简历预览界面，切换任何一个简历模板时，得到的预览图始终是2号模板的。

### 问题分析
1. **文件路径固定问题**：在 `pages/makeCreateResume/components/resumePreview/index.js` 中，预览图片使用固定文件路径 `createResume_preview_resume.jpg`
2. **缓存机制问题**：微信小程序的图片组件会缓存相同路径的图片
3. **文件清理缺失**：没有清理旧的预览图片文件的机制

### 修复内容

#### 1. 修改预览图片文件命名策略
**文件**: `pages/makeCreateResume/components/resumePreview/index.js`
**修改内容**:
- 将固定文件名改为包含模板ID和时间戳的唯一文件名
- 格式：`preview_{templateId}_{timestamp}.jpg`
- 添加缓存破坏参数：`?t={timestamp}`

```javascript
// 修改前
const tempFilePath = `${wx.env.USER_DATA_PATH}/createResume_preview_resume.jpg`;

// 修改后
const timestamp = Date.now();
const tempFilePath = `${wx.env.USER_DATA_PATH}/preview_${currentTemplate}_${timestamp}.jpg`;
const imageUrlWithCache = `${tempFilePath}?t=${timestamp}`;
```

#### 2. 添加旧文件清理机制
**文件**: `pages/makeCreateResume/components/resumePreview/index.js`
**新增功能**:
- 新增 `cleanupOldPreviewFiles()` 方法
- 自动清理旧的预览图片文件（保留最新3个）
- 在生成新预览图前调用清理方法

#### 3. 优化模板切换逻辑
**文件**: `pages/makeCreateResume/components/resumePreview/index.js`
**修改内容**:
- 在模板切换时立即清除当前预览图显示
- 添加模板切换的调试日志
- 确保模板变化时能正确触发预览更新

#### 4. 添加调试日志
**文件**: `utils/api/resumeApi.js`
**修改内容**:
- 在API请求中添加详细的调试日志
- 记录模板ID、主题配置等关键参数
- 便于排查服务端接收的参数是否正确

### 技术细节

#### 文件命名策略
- 使用模板ID区分不同模板的预览图
- 使用时间戳确保文件名唯一性
- 避免文件覆盖和缓存问题

#### 缓存处理
- URL添加时间戳参数破坏缓存
- 模板切换时清空当前预览图显示
- 确保新图片能正确加载

#### 文件管理
- 自动清理机制防止文件累积
- 保留最新3个预览文件
- 异步清理不影响用户体验

### 预期效果
1. 切换模板时预览图能正确更新
2. 不同模板显示对应的预览图
3. 文件管理更加合理，避免存储空间浪费
4. 提供详细的调试信息便于问题排查

### 测试计划
1. 测试模板A01、A02、A03之间的切换
2. 验证预览图是否正确对应模板
3. 检查文件清理机制是否正常工作
4. 确认调试日志输出是否正确

### 测试步骤
1. **打开简历预览页面**
   - 进入 `pages/makeCreateResume/makeCreateResume` 页面
   - 确保有简历数据可供预览

2. **测试模板切换**
   - 点击底部的模板选择器
   - 依次切换到模板一、模板二、模板三
   - 观察预览图是否正确更新

3. **检查调试日志**
   - 打开微信开发者工具的控制台
   - 查看是否有以下日志输出：
     - `模板切换：templateA01 -> templateA02`
     - `=== 预览图片API请求 ===`
     - `模板ID: templateA02`
     - `预览图片临时文件创建成功`

4. **验证文件管理**
   - 多次切换模板
   - 检查是否自动清理旧的预览文件
   - 确认不会产生过多临时文件

### 预期结果
- ✅ 切换到模板一时显示模板一的预览图
- ✅ 切换到模板二时显示模板二的预览图
- ✅ 切换到模板三时显示模板三的预览图
- ✅ 每次切换都有相应的调试日志
- ✅ 临时文件数量控制在合理范围内

## 2024-01-XX - 简历数据处理逻辑优化

### 问题描述
用户反馈有一些用户输入的简历字段的数据不能全部正确汇集到最终的简历数据集合中，需要检查简历字段的处理问题，并规划更好的数据处理逻辑。

### 问题分析
通过代码分析发现以下关键问题：

1. **数据字段映射不一致**
   - makeResume页面保存时使用复数形式：`educations`、`schools`、`internships`、`works`、`projects`、`customs`
   - makeResume页面加载时期望复数形式
   - 服务端期望格式使用单数形式：`education`、`school`、`internship`、`work`、`project`、`custom1`

2. **数据传递时的字段名转换问题**
   - generateResume()方法中传递给预览页面的数据使用单数形式
   - 但保存时使用复数形式，导致数据不一致

3. **自定义模块数据结构不匹配**
   - 保存时：`customs: this.data.custom1`
   - 服务端期望：`custom1: List[CustomItem]`、`custom2: List[CustomItem]`、`custom3: List[CustomItem]`

### 修复内容

#### 1. 创建数据格式转换工具
**文件**: `utils/resume/dataFormatter.js` (新建)
**功能**:
- `convertFromResumeManager()`: 将resumeManager格式转换为makeResume页面格式
- `convertToResumeManager()`: 将makeResume页面格式转换为resumeManager格式
- `convertToServerFormat()`: 将makeResume页面格式转换为服务端期望格式
- 各种数据验证和清理函数

#### 2. 修复makeResume页面数据处理
**文件**: `pages/makeResume/makeResume.js`
**修改内容**:
- 引入数据格式转换工具
- 修改`loadCurrentResumeData()`方法使用格式转换
- 修改`saveCurrentResumeData()`方法使用格式转换
- 修改`generateResume()`方法使用服务端格式转换

#### 3. 统一数据流程
**数据流程优化**:
1. **用户输入** → 各个模块页面保存到本地存储和resumeManager
2. **数据加载** → resumeManager格式 → 转换为页面格式 → 显示
3. **数据保存** → 页面格式 → 转换为resumeManager格式 → 存储
4. **数据传递** → 页面格式 → 转换为服务端格式 → 发送到服务器

### 技术细节

#### 数据格式映射表
| 阶段 | basicInfo | education | work | custom1 |
|------|-----------|-----------|------|---------|
| 页面内部 | basicInfo | education | work | custom1 |
| resumeManager | basicInfo | educations | works | customs |
| 服务端 | basicInfo | education | work | custom1 |

#### 关键转换逻辑
```javascript
// resumeManager → 页面格式
education: resumeManagerData.educations || []

// 页面格式 → resumeManager
educations: makeResumeData.education || []

// 页面格式 → 服务端格式
education: validateEducationList(makeResumeData.education || [])
```

### 预期效果
1. 数据在各个阶段的格式转换正确无误
2. 用户输入的所有字段都能正确汇集到最终数据
3. 服务端接收到的数据格式完全符合预期
4. 提供详细的调试日志便于问题排查
5. 代码结构更加清晰，便于维护

## 2024-01-XX - 自定义模块日期选择器优化

### 问题描述
用户反馈自定义1、自定义2、自定义3这3个模块的日期选择器精确到日，希望和其他栏目一样只选择年和月。

### 问题分析
通过对比其他模块的代码发现：
- **其他模块**（education、work、internship等）使用 `<picker mode="date" fields="month">` 来只选择年月
- **自定义模块**使用 `<picker mode="date">` 默认选择年月日

### 修复内容

#### 修改日期选择器配置
**文件**:
- `pages/makeResume/custom/custom1/custom1.wxml`
- `pages/makeResume/custom/custom2/custom2.wxml`
- `pages/makeResume/custom/custom3/custom3.wxml`

**修改内容**:
```xml
<!-- 修改前 -->
<picker mode="date" value="{{customFormData.startDate}}" bindchange="handleStartDateChange">

<!-- 修改后 -->
<picker mode="date" fields="month" value="{{customFormData.startDate}}" bindchange="handleStartDateChange">
```

#### 技术细节
- 添加 `fields="month"` 属性限制日期选择器只显示年月
- 保持与其他模块（education、work、project等）的一致性
- 不需要修改JavaScript逻辑，只需要修改WXML模板

### 预期效果
- ✅ 自定义模块的日期选择器只显示年月选择
- ✅ 与其他模块的日期选择体验保持一致
- ✅ 用户操作更加简洁高效

## 2024-01-XX - 简历模块删除同步修复

### 问题描述
用户反馈教育背景模块中的description字段在最终汇集的简历数据中缺失，检查发现是删除操作时没有同步到resumeManager导致的数据不一致问题。

### 问题分析
通过代码分析发现以下问题：

1. **教育背景模块删除同步缺失**
   - `education.js` 和 `educationEdit.js` 中删除操作只更新了本地存储
   - 没有同步到 `resumeManager`，导致数据不一致

2. **其他模块类似问题**
   - 工作经历、项目经历、实习经历、在校经历模块都存在相同问题
   - 删除操作时只更新本地存储，未同步到统一的数据管理器

3. **数据流不完整**
   - 保存操作有双重保存策略（本地存储 + resumeManager）
   - 删除操作缺少resumeManager同步，导致数据不一致

### 修复内容

#### 1. 修复教育背景模块删除同步
**文件**:
- `pages/makeResume/education/education/education.js`
- `pages/makeResume/education/educationEdit/educationEdit.js`

**修改内容**:
```javascript
// 在删除操作中添加resumeManager同步
const resumeManager = require('../../../../utils/resume/resumeManager.js');
const currentResumeData = resumeManager.getCurrentResumeData() || {};
currentResumeData.educations = educationFormData;
resumeManager.saveCurrentResumeData(currentResumeData);
```

#### 2. 修复工作经历模块删除同步
**文件**: `pages/makeResume/work/work/work.js`
**修改内容**: 在 `deleteWork()` 方法中添加resumeManager同步

#### 3. 修复项目经历模块删除同步
**文件**: `pages/makeResume/project/project/project.js`
**修改内容**: 在 `deleteProject()` 方法中添加resumeManager同步

#### 4. 修复实习经历模块删除同步
**文件**: `pages/makeResume/internship/internshipExperience/internshipExperience.js`
**修改内容**: 在 `deleteInternship()` 方法中添加resumeManager同步

#### 5. 修复在校经历模块删除同步
**文件**: `pages/makeResume/school/school/school.js`
**修改内容**: 在 `deleteSchool()` 方法中添加resumeManager同步

### 技术细节

#### 统一的删除同步模式
```javascript
// 删除操作的标准模式
deleteItem(e) {
  const index = e.currentTarget.dataset.index;
  wx.showModal({
    title: '提示',
    content: '确定要删除这条记录吗？',
    success: (res) => {
      if (res.confirm) {
        // 1. 更新本地数据
        const formData = this.data.formData;
        formData.splice(index, 1);
        this.setData({ formData });
        wx.setStorageSync('storageKey', formData);

        // 2. 同步到resumeManager
        const resumeManager = require('../../../../utils/resume/resumeManager.js');
        const currentResumeData = resumeManager.getCurrentResumeData() || {};
        currentResumeData.fieldName = formData;
        resumeManager.saveCurrentResumeData(currentResumeData);

        // 3. 添加调试日志
        console.log('=== 模块删除同步 ===');
        console.log('删除后的数据:', formData);
      }
    }
  });
}
```

#### 数据一致性保证
- **保存操作**：本地存储 + resumeManager（已有）
- **删除操作**：本地存储 + resumeManager（新增）
- **排序操作**：本地存储 + resumeManager（部分模块已有）

### 预期效果
1. 教育背景模块的description字段能正确保存和传递
2. 所有模块的删除操作都能正确同步到resumeManager
3. 数据在各个阶段保持一致性
4. 用户删除数据后，预览页面能正确反映变化
5. 服务端接收到的数据完整准确
