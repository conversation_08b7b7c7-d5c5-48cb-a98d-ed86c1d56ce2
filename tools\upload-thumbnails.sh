# 简历模板11
# 原文件: 11.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_44e08240" \
  -F "file=@./thumbnails/template_general_44e08240.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板10
# 原文件: 10.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_928270ab" \
  -F "file=@./thumbnails/template_general_928270ab.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板21
# 原文件: 21.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_79cb8c08" \
  -F "file=@./thumbnails/template_general_79cb8c08.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板5
# 原文件: 5.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_a0e96f29" \
  -F "file=@./thumbnails/template_general_a0e96f29.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板4
# 原文件: 4.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_2c8734e9" \
  -F "file=@./thumbnails/template_general_2c8734e9.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板1
# 原文件: 1.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_50fa92e8" \
  -F "file=@./thumbnails/template_general_50fa92e8.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板2
# 原文件: 2.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_a7e0c3ad" \
  -F "file=@./thumbnails/template_general_a7e0c3ad.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板3
# 原文件: 3.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_456dc700" \
  -F "file=@./thumbnails/template_general_456dc700.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板9
# 原文件: 9.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_a7373648" \
  -F "file=@./thumbnails/template_general_a7373648.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板8
# 原文件: 8.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_9d9a8325" \
  -F "file=@./thumbnails/template_general_9d9a8325.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板6
# 原文件: 6.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_beaab876" \
  -F "file=@./thumbnails/template_general_beaab876.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 表格简历 熊猫
# 原文件: 表格简历-熊猫.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_91526f8f" \
  -F "file=@./thumbnails/template_general_91526f8f.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板7
# 原文件: 7.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_c8b0f6ca" \
  -F "file=@./thumbnails/template_general_c8b0f6ca.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板13
# 原文件: 13.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_0f2ffe99" \
  -F "file=@./thumbnails/template_general_0f2ffe99.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板1
# 原文件: 1.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_60381b17" \
  -F "file=@./thumbnails/template_general_60381b17.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板12
# 原文件: 12.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_8cf6e395" \
  -F "file=@./thumbnails/template_general_8cf6e395.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板15
# 原文件: 15.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_bbe64abf" \
  -F "file=@./thumbnails/template_general_bbe64abf.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板18
# 原文件: 18.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_aea6df56" \
  -F "file=@./thumbnails/template_general_aea6df56.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板20
# 原文件: 20.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_5459e99f" \
  -F "file=@./thumbnails/template_general_5459e99f.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板16
# 原文件: 16.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_aeb748d8" \
  -F "file=@./thumbnails/template_general_aeb748d8.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板14
# 原文件: 14.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_b6698b3f" \
  -F "file=@./thumbnails/template_general_b6698b3f.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板17
# 原文件: 17.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_4a4a9f24" \
  -F "file=@./thumbnails/template_general_4a4a9f24.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板19
# 原文件: 19.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_5d616929" \
  -F "file=@./thumbnails/template_general_5d616929.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板22
# 原文件: 22.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_10274569" \
  -F "file=@./thumbnails/template_general_10274569.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板25
# 原文件: 25.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_9e350cc0" \
  -F "file=@./thumbnails/template_general_9e350cc0.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板23
# 原文件: 23.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_a0449117" \
  -F "file=@./thumbnails/template_general_a0449117.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板24
# 原文件: 24.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_a530ede0" \
  -F "file=@./thumbnails/template_general_a530ede0.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板5
# 原文件: 5.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_152fab2b" \
  -F "file=@./thumbnails/template_general_152fab2b.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板6
# 原文件: 6.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_06996603" \
  -F "file=@./thumbnails/template_general_06996603.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板4
# 原文件: 4.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_1cc90dda" \
  -F "file=@./thumbnails/template_general_1cc90dda.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板3
# 原文件: 3.docx
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_f784aabd" \
  -F "file=@./thumbnails/template_general_f784aabd.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 简历模板25
# 原文件: 25.doc
curl -X POST "https://your-server.com/api/upload-thumbnail" \
  -F "templateId=template_general_bca91093" \
  -F "file=@./thumbnails/template_general_bca91093.jpg" \
  -H "Authorization: Bearer YOUR_TOKEN"
