<view class="container">
  <view class="formGroup">
    <!-- 工作时间 -->
    <view class="formItem">
      <text class="label">工作时间</text>
      <view class="datePicker">
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{workEditFormData.startDate}}" data-field="startDate" bindchange="handleDateChange">
            <view class="picker {{workEditFormData.startDate ? '' : 'placeholder'}}">
              {{workEditFormData.startDate || '请选择'}}
            </view>
          </picker>
        </view>
        <text class="separator">至</text>
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{workEditFormData.endDate === '至今' ? currentDate : workEditFormData.endDate}}" data-field="endDate" bindchange="handleDateChange">
            <view class="picker {{workEditFormData.endDate ? '' : 'placeholder'}}">
              {{workEditFormData.endDate || '请选择'}}
            </view>
          </picker>
        </view>
        <view class="nowBtn" bindtap="setEndDateToNow">至今</view>
      </view>
    </view>

    <!-- 公司名称 -->
    <view class="formItem">
      <text class="label">公司名称</text>
      <input class="input" placeholder="请输入" value="{{workEditFormData.company}}" data-field="company" bindinput="handleInput"/>
    </view>

    <!-- 工作职位 -->
    <view class="formItem">
      <text class="label">工作职位</text>
      <input class="input" placeholder="请输入 最多15字符" maxlength="15" value="{{workEditFormData.position}}" data-field="position" bindinput="handleInput"/>
    </view>

    <!-- 工作内容 -->
    <view class="formItem">
      <text class="label">工作内容</text>
      <textarea class="textarea" placeholder="请输入主要工作内容" value="{{workEditFormData.description}}" data-field="description" bindinput="handleInput"/>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveInfo">保存</button>
    <button wx:if="{{isEdit}}" class="deleteBtn" bindtap="deleteInfo">删除</button>
  </view>
</view> 