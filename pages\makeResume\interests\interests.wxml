<view class="container">
  <!-- 兴趣列表 -->
  <view class="interestsList">
    <view class="interestItem" wx:for="{{16}}" wx:key="index">
      <text class="interestLabel">兴趣爱好{{index + 1}}</text>
      <input class="interestInput" 
             placeholder="请输入(选填)" 
             value="{{interestsList[index]}}"
             data-index="{{index}}"
             bindinput="handleInput"/>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveInfo">保存信息</button>
    <button class="deleteBtn" bindtap="deleteInfo">删除</button>
  </view>
</view> 