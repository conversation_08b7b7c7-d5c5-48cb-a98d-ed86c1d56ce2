.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 140rpx;
}

.evaluationSection {
  padding: 30rpx;
}

.sectionTitle {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.inputArea {
  position: relative;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.evaluationInput {
  width: 100%;
  height: 400rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.wordCount {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.tipsSection {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

.tipsTitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.tipsContent {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.tipItem {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
}

.buttonArea {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40rpx;
}

.saveBtn, .deleteBtn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.saveBtn {
  flex: 3;
  background: #4B8BF5;
  color: #fff;
}

.deleteBtn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
} 