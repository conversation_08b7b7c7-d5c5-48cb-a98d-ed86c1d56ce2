/**
 * 性能监控工具
 * 用于监控setData传输量、请求频率等性能指标
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      setDataCalls: 0,
      totalDataTransferred: 0,
      requestCount: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      largestDataTransfer: 0,
      startTime: Date.now(),
      // 图片压缩相关指标
      imageCompressions: 0,
      totalOriginalImageSize: 0,
      totalCompressedImageSize: 0,
      averageCompressionRatio: 0
    };

    this.requestTimes = [];
    this.dataTransferSizes = [];
    this.compressionRatios = [];
    this.isEnabled = true;
  }

  // 记录setData调用
  recordSetData(data, componentName = 'unknown') {
    if (!this.isEnabled) return;

    const dataSize = this.calculateDataSize(data);
    this.metrics.setDataCalls++;
    this.metrics.totalDataTransferred += dataSize;
    this.dataTransferSizes.push(dataSize);

    if (dataSize > this.metrics.largestDataTransfer) {
      this.metrics.largestDataTransfer = dataSize;
    }

    // 分级警告系统
    if (dataSize > 1024 * 1024) { // 1MB
      console.error(`[严重警告] ${componentName} setData传输过大: ${this.formatSize(dataSize)}`);
    } else if (dataSize > 500 * 1024) { // 500KB
      console.warn(`[性能警告] ${componentName} setData传输较大: ${this.formatSize(dataSize)}`);
    } else if (dataSize > 200 * 1024) { // 200KB
      console.log(`[性能提示] ${componentName} setData传输中等: ${this.formatSize(dataSize)}`);
    } else {
      console.log(`[性能监控] ${componentName} setData: ${this.formatSize(dataSize)}`);
    }
  }

  // 记录API请求
  recordRequest(startTime, endTime, isCacheHit = false) {
    if (!this.isEnabled) return;

    const responseTime = endTime - startTime;
    this.requestTimes.push(responseTime);
    this.metrics.requestCount++;

    if (isCacheHit) {
      this.metrics.cacheHits++;
    } else {
      this.metrics.cacheMisses++;
    }

    // 计算平均响应时间
    this.metrics.averageResponseTime =
      this.requestTimes.reduce((sum, time) => sum + time, 0) / this.requestTimes.length;

    console.log(`[性能监控] API请求: ${responseTime}ms ${isCacheHit ? '(缓存命中)' : '(网络请求)'}`);
  }

  // 记录图片压缩
  recordImageCompression(originalSize, compressedSize, compressionTime = 0) {
    if (!this.isEnabled) return;

    this.metrics.imageCompressions++;
    this.metrics.totalOriginalImageSize += originalSize;
    this.metrics.totalCompressedImageSize += compressedSize;

    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100);
    this.compressionRatios.push(compressionRatio);

    // 计算平均压缩率
    this.metrics.averageCompressionRatio =
      this.compressionRatios.reduce((sum, ratio) => sum + ratio, 0) / this.compressionRatios.length;

    console.log(`[性能监控] 图片压缩: ${this.formatSize(originalSize)} → ${this.formatSize(compressedSize)} (压缩率: ${compressionRatio.toFixed(1)}%)${compressionTime ? ` 耗时: ${compressionTime}ms` : ''}`);
  }

  // 计算数据大小
  calculateDataSize(data) {
    try {
      const jsonString = JSON.stringify(data);
      // 微信小程序兼容的字节长度计算
      return this.getStringByteLength(jsonString);
    } catch (error) {
      console.error('计算数据大小失败:', error);
      return 0;
    }
  }

  // 获取字符串字节长度（兼容微信小程序）
  getStringByteLength(str) {
    let byteLength = 0;
    for (let i = 0; i < str.length; i++) {
      const charCode = str.charCodeAt(i);
      if (charCode <= 0x7F) {
        byteLength += 1;
      } else if (charCode <= 0x7FF) {
        byteLength += 2;
      } else if (charCode <= 0xFFFF) {
        byteLength += 3;
      } else {
        byteLength += 4;
      }
    }
    return byteLength;
  }

  // 格式化大小显示
  formatSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / (1024 * 1024)) + ' MB';
  }

  // 获取性能报告
  getPerformanceReport() {
    const runTime = Date.now() - this.metrics.startTime;
    const cacheHitRate = this.metrics.requestCount > 0
      ? (this.metrics.cacheHits / this.metrics.requestCount * 100).toFixed(1)
      : 0;

    const report = {
      运行时间: Math.round(runTime / 1000) + '秒',
      setData调用次数: this.metrics.setDataCalls,
      总数据传输量: this.formatSize(this.metrics.totalDataTransferred),
      平均每次传输: this.formatSize(this.metrics.totalDataTransferred / Math.max(this.metrics.setDataCalls, 1)),
      最大单次传输: this.formatSize(this.metrics.largestDataTransfer),
      API请求次数: this.metrics.requestCount,
      缓存命中率: cacheHitRate + '%',
      平均响应时间: Math.round(this.metrics.averageResponseTime) + 'ms',
      性能评级: this.getPerformanceGrade()
    };

    // 如果有图片压缩数据，添加到报告中
    if (this.metrics.imageCompressions > 0) {
      report.图片压缩次数 = this.metrics.imageCompressions;
      report.压缩前总大小 = this.formatSize(this.metrics.totalOriginalImageSize);
      report.压缩后总大小 = this.formatSize(this.metrics.totalCompressedImageSize);
      report.平均压缩率 = this.metrics.averageCompressionRatio.toFixed(1) + '%';
      report.压缩节省空间 = this.formatSize(this.metrics.totalOriginalImageSize - this.metrics.totalCompressedImageSize);
    }

    return report;
  }

  // 获取性能评级
  getPerformanceGrade() {
    let score = 100;

    // 根据平均数据传输大小扣分
    const avgTransferSize = this.metrics.totalDataTransferred / Math.max(this.metrics.setDataCalls, 1);
    if (avgTransferSize > 500 * 1024) score -= 30; // 超过500KB
    else if (avgTransferSize > 200 * 1024) score -= 15; // 超过200KB

    // 根据缓存命中率加分
    const cacheHitRate = this.metrics.requestCount > 0
      ? (this.metrics.cacheHits / this.metrics.requestCount)
      : 0;
    if (cacheHitRate > 0.8) score += 10; // 缓存命中率超过80%
    else if (cacheHitRate < 0.3) score -= 20; // 缓存命中率低于30%

    // 根据响应时间扣分
    if (this.metrics.averageResponseTime > 3000) score -= 25; // 超过3秒
    else if (this.metrics.averageResponseTime > 1000) score -= 10; // 超过1秒

    if (score >= 90) return 'A (优秀)';
    if (score >= 80) return 'B (良好)';
    if (score >= 70) return 'C (一般)';
    if (score >= 60) return 'D (较差)';
    return 'F (很差)';
  }

  // 打印性能报告
  printReport() {
    console.log('========== 性能监控报告 ==========');
    const report = this.getPerformanceReport();
    Object.entries(report).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });
    console.log('================================');
  }

  // 重置监控数据
  reset() {
    this.metrics = {
      setDataCalls: 0,
      totalDataTransferred: 0,
      requestCount: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      largestDataTransfer: 0,
      startTime: Date.now(),
      // 图片压缩相关指标
      imageCompressions: 0,
      totalOriginalImageSize: 0,
      totalCompressedImageSize: 0,
      averageCompressionRatio: 0
    };
    this.requestTimes = [];
    this.dataTransferSizes = [];
    this.compressionRatios = [];
  }

  // 启用/禁用监控
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  // 获取数据传输趋势
  getDataTransferTrend() {
    const recentTransfers = this.dataTransferSizes.slice(-10); // 最近10次
    if (recentTransfers.length < 2) return '数据不足';

    const firstHalf = recentTransfers.slice(0, Math.floor(recentTransfers.length / 2));
    const secondHalf = recentTransfers.slice(Math.floor(recentTransfers.length / 2));

    const firstAvg = firstHalf.reduce((sum, size) => sum + size, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, size) => sum + size, 0) / secondHalf.length;

    const change = ((secondAvg - firstAvg) / firstAvg * 100).toFixed(1);

    if (Math.abs(change) < 5) return '稳定';
    return change > 0 ? `上升${change}%` : `下降${Math.abs(change)}%`;
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
