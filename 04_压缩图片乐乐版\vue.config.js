const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  configureWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      config.optimization = {
        minimize: true,
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: true,
                drop_debugger: true
              },
              output: {
                comments: false
              }
            },
            extractComments: false,
            parallel: true
          })
        ]
      };
    }
  },
  transpileDependencies: ['uni-app']
} 