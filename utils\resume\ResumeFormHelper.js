/**
 * 简历表单助手工具
 * 提供统一的表单数据初始化和管理方法
 */

const {
  ResumeData,
  BasicInfo,
  JobIntention,
  EducationItem,
  WorkItem,
  ProjectItem,
  CustomItem,
  SchoolExperienceItem,
  InternshipItem,
  InterestsManager,
  SkillsManager,
  AwardsManager
} = require('./ResumeData.js');

/**
 * 简历表单助手类
 */
class ResumeFormHelper {

  /**
   * 获取指定字段的空实例
   * @param {string} fieldName - 字段名称
   * @returns {Object} 空实例的对象表示
   */
  static getEmptyFieldData(fieldName) {
    switch (fieldName) {
      case 'basicInfo':
        return new BasicInfo().toObject();

      case 'jobIntention':
        return new JobIntention().toObject();

      case 'educationItem':
        return new EducationItem().toObject();

      case 'workItem':
        return new WorkItem().toObject();

      case 'projectItem':
        return new ProjectItem().toObject();

      case 'customItem':
        return new CustomItem().toObject();

      case 'schoolExperienceItem':
        return new SchoolExperienceItem().toObject();

      case 'internshipItem':
        return new InternshipItem().toObject();

      case 'interests':
        return new InterestsManager().getAllItems();

      case 'skills':
        return new SkillsManager().getAllItems();

      case 'awards':
        return new AwardsManager().getAllItems();

      default:
        console.warn(`未知的字段类型: ${fieldName}`);
        return {};
    }
  }

  /**
   * 从全局管理器加载字段数据
   * @param {string} fieldName - 字段名称
   * @param {Object} app - 应用实例
   * @returns {Object} 字段数据对象
   */
  static loadFieldData(fieldName, app) {
    try {
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (currentResume && currentResume[fieldName]) {
        // 特殊处理新的管理类
        if (fieldName === 'interests' || fieldName === 'skills' || fieldName === 'awards') {
          if (typeof currentResume[fieldName].getAllItems === 'function') {
            return currentResume[fieldName].getAllItems();
          }
          // 兼容旧数据格式
          else if (Array.isArray(currentResume[fieldName])) {
            const ManagerClass = fieldName === 'interests' ? InterestsManager :
                               fieldName === 'skills' ? SkillsManager : AwardsManager;
            return new ManagerClass(currentResume[fieldName]).getAllItems();
          }
        }
        // 如果是数组字段，返回数组
        else if (Array.isArray(currentResume[fieldName])) {
          return currentResume[fieldName].map(item =>
            typeof item.toObject === 'function' ? item.toObject() : item
          );
        }
        // 如果是对象字段，返回对象
        else if (typeof currentResume[fieldName].toObject === 'function') {
          return currentResume[fieldName].toObject();
        }
        // 其他类型直接返回
        else {
          return currentResume[fieldName];
        }
      } else {
        // 返回空数据
        return this.getEmptyFieldData(fieldName);
      }
    } catch (error) {
      console.error(`加载字段 ${fieldName} 数据失败:`, error);
      return this.getEmptyFieldData(fieldName);
    }
  }

  /**
   * 保存字段数据到全局管理器
   * @param {string} fieldName - 字段名称
   * @param {any} data - 要保存的数据
   * @param {Object} app - 应用实例
   * @returns {boolean} 是否保存成功
   */
  static saveFieldData(fieldName, data, app) {
    try {
      const resumeManager = app.getResumeManager();
      resumeManager.updateField(fieldName, data);
      return true;
    } catch (error) {
      console.error(`保存字段 ${fieldName} 数据失败:`, error);
      return false;
    }
  }

  /**
   * 清空字段数据
   * @param {string} fieldName - 字段名称
   * @param {Object} app - 应用实例
   * @returns {boolean} 是否清空成功
   */
  static clearFieldData(fieldName, app) {
    const emptyData = this.getEmptyFieldData(fieldName);
    return this.saveFieldData(fieldName, emptyData, app);
  }

  /**
   * 验证字段数据
   * @param {string} fieldName - 字段名称
   * @param {any} data - 要验证的数据
   * @returns {Array} 验证错误列表
   */
  static validateFieldData(fieldName, data) {
    try {
      let instance;

      switch (fieldName) {
        case 'basicInfo':
          instance = new BasicInfo(data);
          break;
        case 'jobIntention':
          instance = new JobIntention(data);
          break;
        case 'educationItem':
          instance = new EducationItem(data);
          break;
        case 'workItem':
          instance = new WorkItem(data);
          break;
        case 'projectItem':
          instance = new ProjectItem(data);
          break;
        case 'customItem':
          instance = new CustomItem(data);
          break;
        case 'schoolExperienceItem':
          instance = new SchoolExperienceItem(data);
          break;
        case 'internshipItem':
          instance = new InternshipItem(data);
          break;
        default:
          return []; // 未知字段类型，不验证
      }

      // 如果实例有 validate 方法，调用它
      if (typeof instance.validate === 'function') {
        return instance.validate();
      }

      return [];
    } catch (error) {
      return [`字段 ${fieldName} 验证失败: ${error.message}`];
    }
  }

  /**
   * 获取字段的默认值映射
   * @param {string} fieldName - 字段名称
   * @returns {Object} 默认值映射对象
   */
  static getFieldDefaults(fieldName) {
    const emptyData = this.getEmptyFieldData(fieldName);
    const defaults = {};

    // 为每个字段生成默认值映射
    Object.keys(emptyData).forEach(key => {
      defaults[key] = emptyData[key];
    });

    return defaults;
  }

  /**
   * 创建表单页面的通用方法集合
   * @param {string} fieldName - 字段名称
   * @returns {Object} 包含常用方法的对象
   */
  static createFormPageMethods(fieldName) {
    return {
      /**
       * 加载数据
       */
      loadData() {
        const app = getApp();
        const data = ResumeFormHelper.loadFieldData(fieldName, app);

        this.setData({
          formData: data
        });

        console.log(`✅ ${fieldName} 数据加载成功:`, data);
      },

      /**
       * 保存数据
       */
      saveData() {
        const app = getApp();
        const success = ResumeFormHelper.saveFieldData(fieldName, this.data.formData, app);

        if (success) {
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 1500
          });

          console.log(`✅ ${fieldName} 保存成功`);

          setTimeout(() => {
            wx.navigateBack();
          }, 500);
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      },

      /**
       * 删除数据
       */
      deleteData() {
        wx.showModal({
          title: '提示',
          content: `确定要删除${fieldName}信息吗？`,
          success: (res) => {
            if (res.confirm) {
              const app = getApp();
              const success = ResumeFormHelper.clearFieldData(fieldName, app);

              if (success) {
                // 重置表单数据
                const emptyData = ResumeFormHelper.getEmptyFieldData(fieldName);
                this.setData({
                  formData: emptyData
                });

                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });

                console.log(`✅ ${fieldName} 删除成功`);

                setTimeout(() => {
                  wx.navigateBack();
                }, 500);
              } else {
                wx.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
              }
            }
          }
        });
      },

      /**
       * 验证数据
       */
      validateData() {
        const errors = ResumeFormHelper.validateFieldData(fieldName, this.data.formData);
        return errors;
      }
    };
  }
}

module.exports = ResumeFormHelper;
