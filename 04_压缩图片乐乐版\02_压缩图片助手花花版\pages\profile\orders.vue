<template>
	<view class="orders-container">
		<!-- 自定义导航栏 -->
		<view class="custom-nav glassmorphism">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-content">
				<view class="back-button-container">
					<button class="back-btn" @tap="goBack">
						<text class="back-icon">‹</text>
					</button>
				</view>
				<text class="nav-title">订单查询</text>
				<view class="nav-right-buttons">
					<!-- 保留容器用于布局平衡 -->
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content" :style="{ paddingTop: navHeight + 'px' }">
			<!-- 订单列表 -->
			<view class="orders-section">
				<!-- 刷新按钮 -->
				<view class="refresh-section">
					<button class="refresh-btn neumorphism" @tap="refreshOrderData" :disabled="loading">
						<text class="refresh-text">{{ loading ? '刷新中...' : '刷新订单' }}</text>
					</button>
				</view>

				<!-- 订单列表 -->
				<view class="order-list" v-if="orderList.length > 0">
					<view 
						class="order-item neumorphism" 
						v-for="order in orderList" 
						:key="order.id"
						@tap="viewOrderDetail(order)"
					>
						<view class="order-header">
							<view class="order-info">
								<text class="order-title">{{ order.title }}</text>
								<text class="order-time">{{ order.createTime }}</text>
							</view>
							<view class="order-status" :class="'status-' + order.status">
								<text class="status-text">{{ getStatusText(order.status) }}</text>
							</view>
						</view>
						
						<view class="order-details">
							<view class="detail-row">
								<text class="detail-label">订单号</text>
								<view class="detail-value">
									<text class="order-id">{{ order.id }}</text>
									<button class="copy-btn" @tap.stop="copyOrderId(order.id)">
										<image src="/static/copy.svg" mode="aspectFit" class="copy-icon"></image>
									</button>
								</view>
							</view>

							<view class="detail-row">
								<text class="detail-label">支付金额</text>
								<view class="amount-info">
									<text class="current-price">¥{{ order.actualPrice }}</text>
									<text class="original-price" v-if="order.originalPrice !== order.actualPrice">¥{{ order.originalPrice }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state neumorphism" v-else>
					<image src="/static/empty-orders.svg" mode="aspectFit" class="empty-icon"></image>
					<text class="empty-title">暂无订单记录</text>
					<text class="empty-desc">您还没有购买过会员服务</text>
					<button class="goto-member-btn" @tap="gotoMemberPage">
						<text>立即开通会员</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getMemberOrders, refreshMemberOrders } from '@/utils/memberManager.js'
import { loginToImageCompressionService } from '@/utils/auth.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			navHeight: 0,
			// 订单列表
			orderList: [],
			// 加载状态
			loading: false
		}
	},
	async onReady() {
		// 获取状态栏高度
		const windowInfo = uni.getWindowInfo()
		this.statusBarHeight = windowInfo.statusBarHeight
		// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
		this.navHeight = this.statusBarHeight + 44

		// 加载订单数据
		this.loadOrderData()
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 加载订单数据
		async loadOrderData() {
			if (this.loading) return;

			try {
				this.loading = true;
				console.log('🔍 开始加载订单数据...');

				// 确保用户已登录
				await loginToImageCompressionService();

				// 获取订单列表
				this.orderList = await getMemberOrders();

				console.log('✅ 订单数据加载成功:', this.orderList.length, '条');
			} catch (error) {
				console.error('❌ 加载订单数据失败:', error);
				this.orderList = [];

				// 显示错误提示
				uni.showToast({
					title: '加载订单失败',
					icon: 'none',
					duration: 2000
				});
			} finally {
				this.loading = false;
			}
		},

		// 刷新订单数据
		async refreshOrderData() {
			try {
				console.log('🔄 刷新订单数据...');

				// 显示加载提示
				uni.showLoading({
					title: '刷新中...',
					mask: true
				});

				// 强制刷新订单数据
				this.orderList = await refreshMemberOrders();

				console.log('✅ 订单数据刷新成功:', this.orderList.length, '条');

				// 显示成功提示
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1500
				});
			} catch (error) {
				console.error('❌ 刷新订单数据失败:', error);

				// 显示错误提示
				uni.showToast({
					title: '刷新失败，请重试',
					icon: 'none',
					duration: 2000
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'completed': '支付成功',
				'paid': '支付成功',
				'pending': '待支付',
				'failed': '支付失败',
				'refunded': '已退款',
				'cancelled': '已取消'
			}
			return statusMap[status] || '未知状态'
		},

		// 复制订单号
		copyOrderId(orderId) {
			uni.setClipboardData({
				data: orderId,
				success: () => {
					uni.showToast({
						title: '订单号已复制',
						icon: 'success'
					})
				}
			})
		},

		// 查看订单详情
		viewOrderDetail(order) {
			// TODO: 实现订单详情查看功能
			uni.showModal({
				title: '订单详情',
				content: `订单号：${order.id}\n商品：${order.title}\n金额：¥${order.actualPrice}\n状态：${this.getStatusText(order.status)}`,
				showCancel: false,
				confirmText: '知道了'
			})
		},

		// 跳转到会员页面
		gotoMemberPage() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss">
// 定义主题色变量，与主页保持一致
$primary-color: #0EA5E9; // 主题色：科技蓝
$primary-gradient: linear-gradient(145deg, #0EA5E9, #0284C7);
$bg-color: #F7F7F7; // 背景色：微信灰
$text-primary: #2C2C2C; // 主要文字颜色
$text-secondary: #666666; // 次要文字颜色
$border-color: #E5E5E5;

// 自定义阴影变量
$shadow-dark: rgba(163, 177, 198, 0.6);
$shadow-light: rgba(255, 255, 255, 0.7);

// 新拟物风格的混入
@mixin neumorphism {
	background: $bg-color;
	box-shadow: 12px 12px 24px $shadow-dark,
				-8px -8px 20px $shadow-light,
				inset 2px 2px 4px rgba(255, 255, 255, 0.5),
				inset -2px -2px 4px rgba(0, 0, 0, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

// 磨砂玻璃风格的混入
@mixin glassmorphism {
	background: rgba($bg-color, 0.98);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.neumorphism {
	@include neumorphism;
}

.glassmorphism {
	@include glassmorphism;
}

.orders-container {
	padding: 30rpx;

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		padding: 0 30rpx;

		.nav-content {
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.back-button-container {
				min-width: 44px;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.back-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 44px;
					height: 44px;
					background: transparent !important;
					border: none !important;
					border-radius: 0 !important;
					box-shadow: none !important;
					padding: 0;
					margin: 0;

					&::after {
						display: none !important;
					}

					.back-icon {
						font-size: 36px;
						color: #000000;
						font-weight: normal;
						line-height: 1;
						margin-left: -2px;
					}
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: $text-primary;
				font-weight: 500;
			}

			.nav-right-buttons {
				min-width: 44px;
			}
		}
	}

	.main-content {
		position: relative;
		width: 100%;
		padding-bottom: 40rpx;
		margin-top: -40rpx;

		.orders-section {
			.refresh-section {
				margin-bottom: 32rpx;

				.refresh-btn {
					width: 100%;
					height: 88rpx;
					background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
					border: 2rpx solid rgba($primary-color, 0.2);
					border-radius: 16rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 32rpx;
					color: $primary-color;
					font-weight: 500;

					&::after {
						display: none;
					}

					&:active {
						background: linear-gradient(135deg, rgba($primary-color, 0.15) 0%, rgba($primary-color, 0.08) 100%);
					}

					&:disabled {
						opacity: 0.6;
						background: rgba($text-secondary, 0.1);
						color: $text-secondary;
						border-color: rgba($text-secondary, 0.2);
					}

					.refresh-text {
						font-size: 32rpx;
						font-weight: 500;
					}
				}
			}

			.order-list {
				.order-item {
					border-radius: 20rpx;
					padding: 30rpx;
					margin-bottom: 20rpx;

					.order-header {
						display: flex;
						justify-content: space-between;
						align-items: flex-start;
						margin-bottom: 20rpx;

						.order-info {
							.order-title {
								font-size: 32rpx;
								font-weight: 600;
								color: $text-primary;
								display: block;
								margin-bottom: 8rpx;
							}

							.order-time {
								font-size: 24rpx;
								color: $text-secondary;
							}
						}

						.order-status {
							padding: 8rpx 16rpx;
							border-radius: 20rpx;
							font-size: 24rpx;

							&.status-completed,
							&.status-paid {
								background: rgba(14, 165, 233, 0.1);
								color: #0EA5E9;
							}

							&.status-pending {
								background: rgba(250, 157, 59, 0.1);
								color: #FA9D3B;
							}

							&.status-failed {
								background: rgba(250, 81, 81, 0.1);
								color: #FA5151;
							}

							&.status-refunded {
								background: rgba(250, 81, 81, 0.1);
								color: #FA5151;
							}

							&.status-cancelled {
								background: rgba(153, 153, 153, 0.1);
								color: #999999;
							}

							.status-text {
								font-weight: 500;
							}
						}
					}

					.order-details {
						.detail-row {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-bottom: 15rpx;

							&:last-child {
								margin-bottom: 0;
							}

							.detail-label {
								font-size: 28rpx;
								color: $text-secondary;
							}

							.detail-value {
								display: flex;
								align-items: center;

								.order-id {
									font-size: 24rpx;
									color: $text-primary;
									margin-right: 10rpx;
									max-width: 200rpx;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
								}

								.copy-btn {
									display: flex;
									align-items: center;
									justify-content: center;
									width: 48rpx;
									height: 48rpx;
									background: rgba($primary-color, 0.1);
									border: none;
									border-radius: 12rpx;
									padding: 0;
									margin: 0;

									&::after {
										display: none;
									}

									&:active {
										background: rgba($primary-color, 0.2);
									}

									.copy-icon {
										width: 24rpx;
										height: 24rpx;
									}
								}
							}

							.amount-info {
								display: flex;
								align-items: center;

								.current-price {
									font-size: 32rpx;
									font-weight: 600;
									color: $primary-color;
									margin-right: 12rpx;
								}

								.original-price {
									font-size: 24rpx;
									color: $text-secondary;
									text-decoration: line-through;
								}
							}
						}
					}
				}
			}

			.empty-state {
				border-radius: 30rpx;
				padding: 60rpx 30rpx;
				text-align: center;

				.empty-icon {
					width: 200rpx;
					height: 200rpx;
					margin: 0 auto 30rpx;
					opacity: 0.6;
				}

				.empty-title {
					display: block;
					font-size: 32rpx;
					font-weight: 500;
					color: $text-primary;
					margin-bottom: 12rpx;
				}

				.empty-desc {
					display: block;
					font-size: 28rpx;
					color: $text-secondary;
					margin-bottom: 40rpx;
					line-height: 1.5;
				}

				.goto-member-btn {
					width: 300rpx;
					height: 80rpx;
					background: linear-gradient(135deg, #0EA5E9, #0284C7);
					border: none;
					border-radius: 40rpx;
					color: white;
					font-size: 28rpx;
					font-weight: 500;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 0 auto;
					transition: all 0.3s ease;
					box-shadow: 0 4rpx 16rpx rgba(14, 165, 233, 0.3);

					&::after {
						display: none;
					}

					&:active {
						background: linear-gradient(135deg, #0284C7, #0369A1);
						transform: scale(0.98);
					}
				}
			}
		}
	}
}
</style>
