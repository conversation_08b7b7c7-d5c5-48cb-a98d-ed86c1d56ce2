const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    workEditFormData: null, // 将在 onLoad 中初始化为 WorkItem 实例
    isEdit: false,
    editIndex: -1,
    currentDate: '' // 当前日期，用于"至今"后重新选择日期时的默认值
  },

  onLoad(options) {

    // 初始化当前日期（格式：YYYY-MM）
    const now = new Date();
    const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    this.setData({
      currentDate: currentDate
    });

    if (options.index !== undefined) {
      // 编辑模式
      this.loadWorkItemForEdit(parseInt(options.index));
    } else {
      // 新增模式
      this.initNewWorkItem();
    }
  },

  /**
   * 加载工作经历项进行编辑
   */
  loadWorkItemForEdit(index) {
    try {
      const workData = ResumeFormHelper.loadFieldData('work', app);

      if (workData && workData[index]) {
        this.setData({
          workEditFormData: workData[index],
          isEdit: true,
          editIndex: index
        });
      } else {
        this.initNewWorkItem();
      }
    } catch (error) {
      console.error('❌ 加载工作经历编辑数据失败:', error);
      this.initNewWorkItem();
    }
  },

  /**
   * 初始化新的工作经历项
   */
  initNewWorkItem() {
    const emptyWorkItem = ResumeFormHelper.getEmptyFieldData('workItem');
    this.setData({
      workEditFormData: emptyWorkItem,
      isEdit: false,
      editIndex: -1
    });
  },

  // 处理输入
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`workEditFormData.${field}`]: e.detail.value
    });
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`workEditFormData.${field}`]: e.detail.value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'workEditFormData.endDate': '至今'
    });
  },

  /**
   * 保存工作经历信息
   */
  saveInfo() {
    const { workEditFormData, isEdit, editIndex } = this.data;

    // 表单验证
    if (!workEditFormData.company) {
      wx.showToast({
        title: '请输入公司名称',
        icon: 'none'
      });
      return;
    }
    if (!workEditFormData.position) {
      wx.showToast({
        title: '请输入工作职位',
        icon: 'none'
      });
      return;
    }
    if (!workEditFormData.startDate) {
      wx.showToast({
        title: '请选择开始时间',
        icon: 'none'
      });
      return;
    }
    if (!workEditFormData.endDate) {
      wx.showToast({
        title: '请选择结束时间',
        icon: 'none'
      });
      return;
    }

    try {
      const workData = ResumeFormHelper.loadFieldData('work', app);
      let workList = Array.isArray(workData) ? [...workData] : [];

      if (isEdit) {
        workList[editIndex] = workEditFormData;
      } else {
        workList.push(workEditFormData);
      }

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('work', workList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });



        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('❌ 保存工作经历时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除工作经历
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该工作经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            const { editIndex } = this.data;
            const workData = ResumeFormHelper.loadFieldData('work', app);
            let workList = Array.isArray(workData) ? [...workData] : [];

            workList.splice(editIndex, 1);

            // 使用 ResumeFormHelper 统一保存
            const success = ResumeFormHelper.saveFieldData('work', workList, app);

            if (success) {
              wx.navigateBack({
                success: () => {
                  wx.showToast({
                    title: '删除成功',
                    icon: 'success'
                  });
                }
              });
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('❌ 删除工作经历时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});