
/**
 * share.js
 * 微信小程序分享功能帮助模块
 */

/**
 * 默认的全局分享信息
 * 当页面没有指定特定分享内容时，将使用此默认配置
 */
const DEFAULT_SHARE_INFO = {
  title: '推荐你使用这款超好用的简历制作工具！',
  path: '/pages/index/index', // 默认跳转到小程序首页
  imageUrl: '/pages/index/images/touXiang.png' // 默认分享图片
};

/**
 * 为页面生成最终的分享配置
 * @param {Object} options - 当前页面希望覆盖的分享信息。
 *   - title: string - 自定义分享标题
 *   - path: string - 自定义分享路径 (例如 /pages/detail/detail?id=123)
 *   - imageUrl: string - 自定义分享图片
 * @returns {Object} - 返回一个可以直接被 onShareAppMessage 使用的对象
 */
const getShareConfig = function(options = {}) {
  // 使用页面传入的 options 覆盖默认配置
  const config = { ...DEFAULT_SHARE_INFO, ...options };

  // --- 统一处理分享逻辑 ---
  // 1. 获取当前用户信息，用于追踪分享来源
  const app = getApp();
  const userInfo = app.globalData.userInfo; // 假设用户信息存储在 globalData

  // 2. 在分享路径后统一添加 sharerId 参数
  if (userInfo && userInfo.id) {
    // 判断原始路径中是否已有 '?'
    const separator = config.path.includes('?') ? '&' : '?';
    config.path = `${config.path}${separator}sharerId=${userInfo.id}`;
  }

  console.log('生成的分享路径:', config.path);

  return config;
}

/**
 * 为朋友圈分享生成配置
 * @param {Object} options - 当前页面希望覆盖的分享信息
 *   - title: string - 自定义标题
 *   - query: string - 自定义查询参数 (例如 id=123&type=a)
 *   - imageUrl: string - 自定义图片
 * @returns {Object}
 */
const getTimelineShareConfig = function(options = {}) {
    const defaultConfig = {
        title: DEFAULT_SHARE_INFO.title,
        query: '',
        imageUrl: DEFAULT_SHARE_INFO.imageUrl
    };

    return { ...defaultConfig, ...options };
}

module.exports = {
  getShareConfig,
  getTimelineShareConfig
}
