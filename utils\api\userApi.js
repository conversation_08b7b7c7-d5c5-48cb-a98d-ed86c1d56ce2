/**
 * 用户相关API接口
 */
const request = require('./request');

/**
 * 用户登录
 * @param {string} code 微信登录code
 * @param {Object} userInfo 用户信息（可选）
 */
function login(code, userInfo = null) {
  const requestData = {
    code
  };

  // 如果有用户信息，按照服务端格式添加
  if (userInfo) {
    requestData.user_info = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender || 0,
      country: userInfo.country || '',
      province: userInfo.province || '',
      city: userInfo.city || ''
    };
  }

  console.log('执行登录API，请求数据:', requestData);
  return request.post('/auth/login', requestData, {
    needAuth: false, // 登录接口不需要认证
    showLoading: false,
    // loadingText: '登录中...'
  });
}

/**
 * 验证token有效性（通过获取用户信息来验证）
 */
function validateToken() {
  return request.get('/auth/user', {}, {
    showLoading: false,
    showError: false,
    needAuth: true
  });
}

/**
 * 获取用户信息
 */
function getUserInfo() {
  return request.get('/auth/user', {}, {
    showLoading: false,
    needAuth: true
  });
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 */
function updateUserInfo(userInfo) {
  return request.put('/auth/user', userInfo, {
    showLoading: false,
    // loadingText: '更新中...'
  });
}

/**
 * 刷新访问令牌
 */
function refreshToken() {
  return request.post('/auth/refresh', {}, {
    showLoading: false,
    needAuth: true // 根据API文档，刷新token需要认证
  });
}

/**
 * 查询会员状态
 */
function getMemberStatus() {
  return request.get('/auth/member-status', {}, {
    showLoading: false,
    showError: false,
    needAuth: true
  });
}

module.exports = {
  login,
  validateToken,
  getUserInfo,
  updateUserInfo,
  refreshToken,
  getMemberStatus
};
