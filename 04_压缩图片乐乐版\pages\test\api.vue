<template>
	<view class="test-container">
		<!-- 自定义导航栏 -->
		<view class="custom-nav glassmorphism">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-content">
				<view class="back-button-container">
					<button class="back-btn" @tap="goBack">
						<text class="back-icon">‹</text>
					</button>
				</view>
				<text class="nav-title">API测试</text>
				<view class="nav-right-buttons">
					<!-- 保留容器用于布局平衡 -->
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content" :style="{ paddingTop: navHeight + 'px' }">
			<!-- 测试按钮区域 -->
			<view class="test-section">
				<view class="test-group neumorphism">
					<text class="group-title">用户登录测试</text>
					<button class="test-btn" @tap="testUserLogin" :disabled="loading">
						<text>测试用户登录</text>
					</button>
					<view class="result-area" v-if="loginResult">
						<text class="result-title">登录结果:</text>
						<text class="result-text">{{ loginResult }}</text>
					</view>
				</view>

				<view class="test-group neumorphism">
					<text class="group-title">会员信息测试</text>
					<button class="test-btn" @tap="testMemberInfo" :disabled="loading">
						<text>获取会员信息</text>
					</button>
					<button class="test-btn" @tap="testRefreshMember" :disabled="loading">
						<text>刷新会员信息</text>
					</button>
					<view class="result-area" v-if="memberResult">
						<text class="result-title">会员信息:</text>
						<text class="result-text">{{ memberResult }}</text>
					</view>
				</view>

				<view class="test-group neumorphism">
					<text class="group-title">商品信息测试</text>
					<button class="test-btn" @tap="testProductList" :disabled="loading">
						<text>获取商品列表</text>
					</button>
					<button class="test-btn" @tap="testMemberPackages" :disabled="loading">
						<text>获取会员套餐</text>
					</button>
					<view class="result-area" v-if="productResult">
						<text class="result-title">商品信息:</text>
						<text class="result-text">{{ productResult }}</text>
					</view>
				</view>

				<view class="test-group neumorphism">
					<text class="group-title">订单信息测试</text>
					<button class="test-btn" @tap="testOrderList" :disabled="loading">
						<text>获取订单列表</text>
					</button>
					<button class="test-btn" @tap="testRefreshOrders" :disabled="loading">
						<text>刷新订单列表</text>
					</button>
					<view class="result-area" v-if="orderResult">
						<text class="result-title">订单信息:</text>
						<text class="result-text">{{ orderResult }}</text>
					</view>
				</view>

				<view class="test-group neumorphism">
					<text class="group-title">清除缓存</text>
					<button class="test-btn clear-btn" @tap="clearAllCache" :disabled="loading">
						<text>清除所有缓存</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { loginToImageCompressionService, clearAuthInfo } from '@/utils/auth.js'
import { getMemberInfo, refreshMemberInfo, getMemberPackages, getMemberOrders, refreshMemberOrders } from '@/utils/memberManager.js'
import { getProductList, refreshProductList, clearProductCache } from '@/utils/productManager.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			navHeight: 0,
			loading: false,
			loginResult: '',
			memberResult: '',
			productResult: '',
			orderResult: ''
		}
	},
	async onReady() {
		// 获取状态栏高度
		const windowInfo = uni.getWindowInfo()
		this.statusBarHeight = windowInfo.statusBarHeight
		// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
		this.navHeight = this.statusBarHeight + 44
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 测试用户登录
		async testUserLogin() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.loginResult = '登录中...';
				
				const result = await loginToImageCompressionService();
				this.loginResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '登录测试成功',
					icon: 'success'
				});
			} catch (error) {
				this.loginResult = `登录失败: ${error.message}`;
				
				uni.showToast({
					title: '登录测试失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 测试会员信息
		async testMemberInfo() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.memberResult = '获取中...';
				
				const result = await getMemberInfo();
				this.memberResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '获取会员信息成功',
					icon: 'success'
				});
			} catch (error) {
				this.memberResult = `获取失败: ${error.message}`;
				
				uni.showToast({
					title: '获取会员信息失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 测试刷新会员信息
		async testRefreshMember() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.memberResult = '刷新中...';
				
				const result = await refreshMemberInfo();
				this.memberResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '刷新会员信息成功',
					icon: 'success'
				});
			} catch (error) {
				this.memberResult = `刷新失败: ${error.message}`;
				
				uni.showToast({
					title: '刷新会员信息失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 测试商品列表
		async testProductList() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.productResult = '获取中...';
				
				const result = await getProductList();
				this.productResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '获取商品列表成功',
					icon: 'success'
				});
			} catch (error) {
				this.productResult = `获取失败: ${error.message}`;
				
				uni.showToast({
					title: '获取商品列表失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 测试会员套餐
		async testMemberPackages() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.productResult = '获取中...';
				
				const result = await getMemberPackages();
				this.productResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '获取会员套餐成功',
					icon: 'success'
				});
			} catch (error) {
				this.productResult = `获取失败: ${error.message}`;
				
				uni.showToast({
					title: '获取会员套餐失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 测试订单列表
		async testOrderList() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.orderResult = '获取中...';
				
				const result = await getMemberOrders();
				this.orderResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '获取订单列表成功',
					icon: 'success'
				});
			} catch (error) {
				this.orderResult = `获取失败: ${error.message}`;
				
				uni.showToast({
					title: '获取订单列表失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 测试刷新订单
		async testRefreshOrders() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				this.orderResult = '刷新中...';
				
				const result = await refreshMemberOrders();
				this.orderResult = JSON.stringify(result, null, 2);
				
				uni.showToast({
					title: '刷新订单列表成功',
					icon: 'success'
				});
			} catch (error) {
				this.orderResult = `刷新失败: ${error.message}`;
				
				uni.showToast({
					title: '刷新订单列表失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 清除所有缓存
		async clearAllCache() {
			if (this.loading) return;
			
			try {
				this.loading = true;
				
				// 清除认证信息
				clearAuthInfo();
				
				// 清除商品缓存
				clearProductCache();
				
				// 清除结果显示
				this.loginResult = '';
				this.memberResult = '';
				this.productResult = '';
				this.orderResult = '';
				
				uni.showToast({
					title: '缓存清除成功',
					icon: 'success'
				});
			} catch (error) {
				uni.showToast({
					title: '缓存清除失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
// 引入全局样式变量
@import '@/uni.scss';

// 使用uni.scss中定义的主题色变量
$primary-color: $uni-color-primary; // 主题色：微信绿
$primary-gradient: $theme-color-primary-gradient;
$bg-color: $uni-bg-color-grey; // 背景色：微信灰
$text-primary: $uni-text-color; // 主要文字颜色
$text-secondary: $theme-text-secondary; // 次要文字颜色
$text-tertiary: $uni-text-color-grey; // 辅助文字颜色
$link-color: $theme-color-link; // 链接/高亮文字颜色
$border-color: $uni-border-color; // 边框颜色
$shadow-dark: $theme-shadow-dark;
$shadow-light: $theme-shadow-light;

.test-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		padding: 0 30rpx;

		.nav-content {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.back-button-container {
				width: 80rpx;
				display: flex;
				justify-content: flex-start;

				.back-btn {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					background: rgba(255, 255, 255, 0.2);
					border: none;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 32rpx;
					color: white;
					font-weight: bold;

					&::after {
						display: none;
					}

					&:active {
						background: rgba(255, 255, 255, 0.3);
					}

					.back-icon {
						font-size: 40rpx;
						line-height: 1;
					}
				}
			}

			.nav-title {
				font-size: 36rpx;
				font-weight: 600;
				color: white;
				text-align: center;
			}

			.nav-right-buttons {
				width: 80rpx;
			}
		}
	}

	.main-content {
		padding: 32rpx;

		.test-section {
			.test-group {
				margin-bottom: 32rpx;
				padding: 32rpx;
				border-radius: 20rpx;

				.group-title {
					display: block;
					font-size: 32rpx;
					font-weight: 600;
					color: $text-primary;
					margin-bottom: 24rpx;
				}

				.test-btn {
					width: 100%;
					height: 80rpx;
					background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
					border: none;
					border-radius: 12rpx;
					color: white;
					font-size: 28rpx;
					font-weight: 500;
					margin-bottom: 16rpx;

					&::after {
						display: none;
					}

					&:active {
						background: linear-gradient(135deg, darken($primary-color, 5%) 0%, $primary-color 100%);
					}

					&:disabled {
						opacity: 0.6;
						background: rgba($text-tertiary, 0.3);
					}

					&.clear-btn {
						background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);

						&:active {
							background: linear-gradient(135deg, #ee5a52 0%, #ff6b6b 100%);
						}
					}
				}

				.result-area {
					margin-top: 24rpx;
					padding: 24rpx;
					background: rgba($text-tertiary, 0.05);
					border-radius: 12rpx;
					border: 2rpx solid rgba($text-tertiary, 0.1);

					.result-title {
						display: block;
						font-size: 28rpx;
						font-weight: 600;
						color: $text-primary;
						margin-bottom: 12rpx;
					}

					.result-text {
						display: block;
						font-size: 24rpx;
						color: $text-secondary;
						line-height: 1.6;
						word-break: break-all;
						white-space: pre-wrap;
						font-family: 'Courier New', monospace;
					}
				}
			}
		}
	}
}
</style>
