<!-- 下载链接浮窗组件 -->
<wxs module="utils">
  function truncateUrl(url) {
    if (!url) return '';
    var maxLength = 30;
    if (url.length <= maxLength) {
      return url;
    }
    return url.substring(0, maxLength) + '...';
  }

  module.exports = {
    truncateUrl: truncateUrl
  };
</wxs>

<view class="download-modal" wx:if="{{visible}}" catchtap="onMaskTap">
  <view class="modal-content" catchtap="stopPropagation">
    <!-- 关闭按钮 -->
    <view class="close-btn" bindtap="onClose">
      <text class="close-icon">×</text>
    </view>

    <!-- 模板信息 -->
    <view class="template-info" wx:if="{{templateData}}">
      <image
        class="template-thumb"
        src="{{templateData.thumb_url}}"
        mode="aspectFit"
        binderror="onImageError"
      ></image>
      <view class="template-details">
        <text class="template-title">下载简历模板</text>
      </view>
    </view>

    <!-- 下载链接区域 -->
    <view class="download-links">
      <!-- 百度网盘链接 -->
      <view class="link-section">
        <view class="link-header">
          <view class="platform-icon baidu-icon">百</view>
          <text class="platform-name">百度网盘</text>
        </view>
        <view class="link-content">
          <view class="link-row">
            <view class="link-info">
              <text class="link-label">链接:</text>
              <text class="link-value" user-select="{{true}}">{{utils.truncateUrl(downloadData.baidu_url) || '暂无链接'}}</text>
            </view>
          </view>
          <view class="button-row">
            <button
              class="action-btn copy-btn"
              bindtap="onCopyLink"
              data-type="baidu_url"
              disabled="{{!downloadData.baidu_url}}"
            >
              复制
            </button>
            <button
              class="action-btn share-btn"
              open-type="share"
              data-platform="baidu"
            >
              分享
            </button>
          </view>
        </view>
      </view>

      <!-- 夸克网盘链接 -->
      <view class="link-section">
        <view class="link-header">
          <view class="platform-icon quark-icon">夸</view>
          <text class="platform-name">夸克网盘</text>
        </view>
        <view class="link-content">
          <view class="link-row">
            <view class="link-info">
              <text class="link-label">链接:</text>
              <text class="link-value" user-select="{{true}}">{{utils.truncateUrl(downloadData.quark_url) || '暂无链接'}}</text>
            </view>
          </view>
          <view class="button-row">
            <button
              class="action-btn copy-btn"
              bindtap="onCopyLink"
              data-type="quark_url"
              disabled="{{!downloadData.quark_url}}"
            >
              复制
            </button>
            <button
              class="action-btn share-btn"
              open-type="share"
              data-platform="quark"
            >
              分享
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="usage-tips">
      <view class="tips-header">
        <text class="tips-title">使用说明</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 点击复制按钮复制链接</text>
        <text class="tip-item">• 请使用网盘下载后，选择电脑端WPS编辑</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">获取下载链接中...</text>
    </view>
  </view>
</view>
