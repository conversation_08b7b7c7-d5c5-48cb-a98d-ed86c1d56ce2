/**
 * API请求工具
 * 统一处理所有API请求，包含错误处理、重试机制等
 */

import { TIMEOUT_CONFIG, RETRY_CONFIG, APP_CONFIG } from '@/config/api.js'
import { requestWithRetry } from '@/utils/networkUtils.js'

/**
 * 统一的API请求方法
 * @param {Object} options 请求选项
 * @param {string} options.url 请求URL
 * @param {string} options.method 请求方法 GET/POST/PUT/DELETE
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {number} options.timeout 超时时间
 * @param {boolean} options.needAuth 是否需要认证
 * @returns {Promise} 请求结果
 */
export async function apiRequest(options = {}) {
  try {
    // 默认配置
    const defaultOptions = {
      method: 'GET',
      timeout: TIMEOUT_CONFIG.DEFAULT,
      needAuth: true,
      header: {
        'Content-Type': 'application/json'
      }
    }

    // 合并配置
    const requestOptions = { ...defaultOptions, ...options }

    // 如果需要认证，添加认证信息
    if (requestOptions.needAuth) {
      const authInfo = await getAuthInfo()
      if (authInfo) {
        requestOptions.data = {
          ...requestOptions.data,
          ...authInfo
        }
      }
    }

    console.log('🚀 发起API请求:', {
      url: requestOptions.url,
      method: requestOptions.method,
      needAuth: requestOptions.needAuth
    })

    // 使用带重试机制的网络请求
    const response = await requestWithRetry(requestOptions, RETRY_CONFIG.MAX_RETRIES, requestOptions.timeout)

    console.log('📥 API响应:', {
      url: requestOptions.url,
      statusCode: response.statusCode,
      success: response.statusCode === 200
    })

    // 检查HTTP状态码
    if (response.statusCode !== 200) {
      throw new Error(`HTTP错误: ${response.statusCode}`)
    }

    // 检查业务状态码
    const result = response.data
    if (result && typeof result === 'object') {
      // 兼容不同的响应格式
      if (result.success === false) {
        throw new Error(result.error || result.message || '请求失败')
      }
      if (result.code !== undefined && result.code !== 0) {
        throw new Error(result.msg || result.message || '请求失败')
      }
    }

    return result
  } catch (error) {
    console.error('❌ API请求失败:', {
      url: options.url,
      error: error.message
    })
    throw error
  }
}

/**
 * GET请求
 */
export async function apiGet(url, params = {}, options = {}) {
  // 将参数拼接到URL中
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    url += (url.includes('?') ? '&' : '?') + queryString
  }

  return apiRequest({
    url,
    method: 'GET',
    ...options
  })
}

/**
 * POST请求
 */
export async function apiPost(url, data = {}, options = {}) {
  return apiRequest({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 */
export async function apiPut(url, data = {}, options = {}) {
  return apiRequest({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
export async function apiDelete(url, options = {}) {
  return apiRequest({
    url,
    method: 'DELETE',
    ...options
  })
}

/**
 * 获取认证信息
 * @returns {Promise<Object>} 认证信息
 */
async function getAuthInfo() {
  try {
    // 从缓存获取OpenID
    const openid = uni.getStorageSync('openid')
    
    if (openid) {
      return {
        openid: openid,
        appId: APP_CONFIG.APP_ID,
        app_id: APP_CONFIG.APP_ID  // 兼容后端期望的参数名
      }
    }

    // 如果没有OpenID，返回基本信息
    return {
      appId: APP_CONFIG.APP_ID,
      app_id: APP_CONFIG.APP_ID  // 兼容后端期望的参数名
    }
  } catch (error) {
    console.error('获取认证信息失败:', error)
    return {
      appId: APP_CONFIG.APP_ID
    }
  }
}

/**
 * 显示API错误提示
 * @param {Error} error 错误对象
 * @param {string} defaultMessage 默认错误消息
 */
export function showApiError(error, defaultMessage = '操作失败，请重试') {
  const message = error.message || defaultMessage
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 处理API错误
 * @param {Error} error 错误对象
 * @param {Object} options 处理选项
 */
export function handleApiError(error, options = {}) {
  const { showToast = true, defaultMessage = '操作失败，请重试' } = options
  
  console.error('API错误处理:', error)
  
  if (showToast) {
    showApiError(error, defaultMessage)
  }
  
  // 可以在这里添加更多错误处理逻辑
  // 比如错误上报、特殊错误码处理等
}

// 导出默认对象
export default {
  apiRequest,
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
  showApiError,
  handleApiError
}
