/**
 * API 测试脚本
 * 用于测试图像压缩服务器的主要接口
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:8851';
const TEST_DATA = {
  openid: 'test_openid_123456',
  appId: 'wx_test_app_id',
  userInfo: {
    nickname: '测试用户',
    avatar_url: 'https://example.com/avatar.jpg'
  }
};

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 测试函数
async function testAPI() {
  console.log('🚀 开始测试图像压缩服务器API...\n');

  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await api.get('/health');
    console.log('✅ 健康检查通过:', healthResponse.data.message);
    console.log('');

    // 2. 测试用户登录
    console.log('2. 测试用户登录...');
    const loginResponse = await api.post('/api/user/login', TEST_DATA);
    console.log('✅ 用户登录成功:', loginResponse.data.message);
    console.log('用户ID:', loginResponse.data.data.user.userId);
    console.log('');

    // 3. 测试获取用户状态
    console.log('3. 测试获取用户状态...');
    const statusResponse = await api.get('/api/user/status', {
      params: {
        openid: TEST_DATA.openid,
        appId: TEST_DATA.appId
      }
    });
    console.log('✅ 获取用户状态成功');
    console.log('会员状态:', statusResponse.data.data.membership.isActive);
    console.log('');

    // 4. 测试检查商户
    console.log('4. 测试检查商户...');
    const merchantResponse = await api.get(`/api/merchant/check/${TEST_DATA.appId}`);
    console.log('✅ 商户检查完成');
    console.log('是否支持:', merchantResponse.data.data.isSupported);
    console.log('');

    // 5. 测试获取商品列表
    console.log('5. 测试获取商品列表...');
    try {
      const productsResponse = await api.get('/api/membership/products', {
        params: { appId: TEST_DATA.appId }
      });
      console.log('✅ 获取商品列表成功');
      console.log('商品数量:', productsResponse.data.data.products.length);
    } catch (error) {
      console.log('⚠️  获取商品列表失败:', error.response?.data?.error || error.message);
    }
    console.log('');

    // 6. 测试会员权限检查
    console.log('6. 测试会员权限检查...');
    const permissionResponse = await api.post('/api/membership/check-permission', {
      openid: TEST_DATA.openid,
      appId: TEST_DATA.appId
    });
    console.log('✅ 会员权限检查完成');
    console.log('有活跃会员:', permissionResponse.data.data.hasActiveMembership);
    console.log('可以替代广告:', permissionResponse.data.data.canReplaceAd);
    console.log('');

    // 7. 测试检查会员权限（替代广告）
    console.log('7. 测试检查会员权限（替代广告）...');
    const checkMembershipResponse = await api.post('/api/user/check-membership', {
      openid: TEST_DATA.openid,
      appId: TEST_DATA.appId
    });
    console.log('✅ 检查会员权限成功');
    console.log('可以替代广告:', checkMembershipResponse.data.data.canReplaceAd);
    console.log('');

    // 8. 测试系统概览
    console.log('8. 测试系统概览...');
    const overviewResponse = await api.get('/api/merchant/system/overview');
    console.log('✅ 系统概览获取成功');
    console.log('总用户数:', overviewResponse.data.data.total.total_users);
    console.log('总收入:', overviewResponse.data.data.total.total_revenue);
    console.log('');

    console.log('🎉 所有API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    console.error('请检查服务器是否正常运行，数据库是否已初始化');
  }
}

// 测试创建订单流程
async function testOrderFlow() {
  console.log('\n🛒 开始测试订单流程...\n');

  try {
    // 1. 先登录用户
    await api.post('/api/user/login', TEST_DATA);
    console.log('✅ 用户登录完成');

    // 2. 创建订单
    console.log('2. 测试创建订单...');
    try {
      const orderResponse = await api.post('/api/order/create', {
        openid: TEST_DATA.openid,
        appId: TEST_DATA.appId,
        productCode: 'day_card'
      });
      console.log('✅ 订单创建成功');
      console.log('订单ID:', orderResponse.data.data.orderId);
      console.log('订单金额:', orderResponse.data.data.amount);

      const orderId = orderResponse.data.data.orderId;

      // 3. 查询订单
      console.log('3. 测试查询订单...');
      const orderDetailResponse = await api.get(`/api/order/${orderId}`, {
        params: {
          openid: TEST_DATA.openid,
          appId: TEST_DATA.appId
        }
      });
      console.log('✅ 订单查询成功');
      console.log('订单状态:', orderDetailResponse.data.data.paymentStatus);

      // 4. 模拟支付成功
      console.log('4. 测试支付成功回调...');
      const paySuccessResponse = await api.post('/api/order/pay-success', {
        orderId: orderId,
        transactionId: `test_transaction_${Date.now()}`,
        paymentMethod: 'wechat'
      });
      console.log('✅ 支付成功处理完成');
      console.log('会员类型:', paySuccessResponse.data.data.membershipType);

      // 5. 检查会员状态
      console.log('5. 测试会员状态...');
      const membershipResponse = await api.get('/api/membership/status', {
        params: {
          openid: TEST_DATA.openid,
          appId: TEST_DATA.appId
        }
      });
      console.log('✅ 会员状态检查完成');
      console.log('会员是否激活:', membershipResponse.data.data.isActive);
      console.log('会员类型:', membershipResponse.data.data.type);

    } catch (error) {
      console.log('⚠️  订单流程测试失败:', error.response?.data?.error || error.message);
      console.log('这可能是因为缺少商户配置，请先在数据库中添加测试商户');
    }

    console.log('\n🎉 订单流程测试完成！');

  } catch (error) {
    console.error('❌ 订单流程测试失败:', error.response?.data || error.message);
  }
}

// 主函数
async function main() {
  console.log('图像压缩服务器 API 测试工具');
  console.log('================================\n');

  // 基础API测试
  await testAPI();

  // 订单流程测试
  await testOrderFlow();

  console.log('\n测试完成！');
  console.log('如果有错误，请检查：');
  console.log('1. 服务器是否在运行 (http://localhost:8851)');
  console.log('2. 数据库是否已初始化');
  console.log('3. 是否已添加测试商户配置');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAPI,
  testOrderFlow
};
