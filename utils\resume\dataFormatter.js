/**
 * 简历数据格式转换工具
 * 处理不同阶段的数据格式转换和验证
 */

/**
 * 将resumeManager格式转换为makeResume页面格式
 * @param {Object} resumeManagerData - resumeManager中的数据
 * @returns {Object} makeResume页面期望的数据格式
 */
function convertFromResumeManager(resumeManagerData) {
  if (!resumeManagerData) return null;

  return {
    basicInfo: resumeManagerData.basicInfo || {},
    jobIntention: resumeManagerData.jobIntention || {},
    // 从复数形式转换为单数形式（页面内部使用）
    education: resumeManagerData.educations || [],
    school: resumeManagerData.schools || [],
    internship: resumeManagerData.internships || [],
    work: resumeManagerData.works || [],
    project: resumeManagerData.projects || [],
    skills: resumeManagerData.skills || [],
    awards: resumeManagerData.awards || [],
    interests: resumeManagerData.interests || [],
    evaluation: resumeManagerData.evaluation || [],
    // 自定义模块处理
    custom1: resumeManagerData.customs || [],
    custom2: resumeManagerData.custom2 || [],
    custom3: resumeManagerData.custom3 || []
  };
}

/**
 * 将makeResume页面格式转换为resumeManager格式
 * @param {Object} makeResumeData - makeResume页面的数据
 * @returns {Object} resumeManager期望的数据格式
 */
function convertToResumeManager(makeResumeData) {
  if (!makeResumeData) return null;

  return {
    basicInfo: makeResumeData.basicInfo || {},
    jobIntention: makeResumeData.jobIntention || {},
    // 转换为复数形式（resumeManager存储格式）
    educations: makeResumeData.education || [],
    schools: makeResumeData.school || [],
    internships: makeResumeData.internship || [],
    works: makeResumeData.work || [],
    projects: makeResumeData.project || [],
    skills: makeResumeData.skills || [],
    awards: makeResumeData.awards || [],
    interests: makeResumeData.interests || [],
    evaluation: makeResumeData.evaluation || [],
    // 自定义模块处理
    customs: makeResumeData.custom1 || [],
    custom2: makeResumeData.custom2 || [],
    custom3: makeResumeData.custom3 || []
  };
}

/**
 * 将makeResume页面格式转换为服务端期望格式
 * @param {Object} makeResumeData - makeResume页面的数据
 * @returns {Object} 服务端期望的数据格式
 */
function convertToServerFormat(makeResumeData) {
  if (!makeResumeData) return null;

  // 验证和清理数据
  const cleanedData = {
    moduleOrders: makeResumeData.moduleOrders || {},
    basicInfo: validateBasicInfo(makeResumeData.basicInfo || {}),
    jobIntention: validateJobIntention(makeResumeData.jobIntention || {}),
    // 服务端期望单数字段名
    education: validateEducationList(makeResumeData.education || []),
    school: validateSchoolList(makeResumeData.school || []),
    internship: validateInternshipList(makeResumeData.internship || []),
    work: validateWorkList(makeResumeData.work || []),
    project: validateProjectList(makeResumeData.project || []),
    skills: validateSkillsList(makeResumeData.skills || []),
    awards: validateAwardsList(makeResumeData.awards || []),
    interests: validateInterestsList(makeResumeData.interests || []),
    evaluation: validateEvaluationList(makeResumeData.evaluation || []),
    custom1: validateCustomList(makeResumeData.custom1 || []),
    custom2: validateCustomList(makeResumeData.custom2 || []),
    custom3: validateCustomList(makeResumeData.custom3 || [])
  };

  console.log('=== 数据格式转换 ===');
  console.log('原始数据:', makeResumeData);
  console.log('转换后数据:', cleanedData);

  return cleanedData;
}

/**
 * 验证基本信息数据
 */
function validateBasicInfo(basicInfo) {
  return {
    name: basicInfo.name || null,
    gender: basicInfo.gender || null,
    phone: basicInfo.phone || null,
    photoUrl: basicInfo.photoUrl || null,
    city: basicInfo.city || null,
    email: basicInfo.email || null,
    wechat: basicInfo.wechat || null,
    age: basicInfo.age || null,
    birthday: basicInfo.birthday || null,
    marriage: basicInfo.marriage || null,
    politics: basicInfo.politics || null,
    nation: basicInfo.nation || null,
    hometown: basicInfo.hometown || null,
    height: basicInfo.height || null,
    weight: basicInfo.weight || null,
    educationLevel: basicInfo.educationLevel || null,
    customTitle1: basicInfo.customTitle1 || null,
    customContent1: basicInfo.customContent1 || null,
    customTitle2: basicInfo.customTitle2 || null,
    customContent2: basicInfo.customContent2 || null
  };
}

/**
 * 验证求职意向数据
 */
function validateJobIntention(jobIntention) {
  return {
    position: jobIntention.position || null,
    city: jobIntention.city || jobIntention.location || null,
    salary: jobIntention.salary || null,
    status: jobIntention.status || null
  };
}

/**
 * 验证教育经历列表
 */
function validateEducationList(educationList) {
  if (!Array.isArray(educationList)) return [];
  
  return educationList.map(item => ({
    school: item.school || '',
    major: item.major || '',
    degree: item.degree || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    description: item.description || null
  }));
}

/**
 * 验证在校经历列表
 */
function validateSchoolList(schoolList) {
  if (!Array.isArray(schoolList)) return [];
  
  return schoolList.map(item => ({
    role: item.role || null,
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    content: item.content || item.description || ''
  }));
}

/**
 * 验证实习经历列表
 */
function validateInternshipList(internshipList) {
  if (!Array.isArray(internshipList)) return [];
  
  return internshipList.map(item => ({
    company: item.company || '',
    position: item.position || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    content: item.content || item.description || ''
  }));
}

/**
 * 验证工作经历列表
 */
function validateWorkList(workList) {
  if (!Array.isArray(workList)) return [];
  
  return workList.map(item => ({
    company: item.company || '',
    position: item.position || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    description: item.description || item.content || null
  }));
}

/**
 * 验证项目经历列表
 */
function validateProjectList(projectList) {
  if (!Array.isArray(projectList)) return [];
  
  return projectList.map(item => ({
    projectName: item.projectName || item.name || '',
    role: item.role || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    description: item.description || item.content || null
  }));
}

/**
 * 验证技能列表
 */
function validateSkillsList(skillsList) {
  if (!Array.isArray(skillsList)) return [];
  
  return skillsList.filter(skill => 
    typeof skill === 'string' && skill.trim() !== ''
  );
}

/**
 * 验证奖项列表
 */
function validateAwardsList(awardsList) {
  if (!Array.isArray(awardsList)) return [];
  
  return awardsList.filter(award => 
    typeof award === 'string' && award.trim() !== ''
  );
}

/**
 * 验证兴趣爱好列表
 */
function validateInterestsList(interestsList) {
  if (!Array.isArray(interestsList)) return [];
  
  return interestsList.filter(interest => 
    typeof interest === 'string' && interest.trim() !== ''
  );
}

/**
 * 验证自我评价列表
 */
function validateEvaluationList(evaluationList) {
  if (!Array.isArray(evaluationList)) return [];
  
  return evaluationList.map(item => {
    if (typeof item === 'string') {
      return { content: item, moduleOrder: 1 };
    }
    return {
      content: item.content || '',
      moduleOrder: item.moduleOrder || 1
    };
  });
}

/**
 * 验证自定义模块列表
 */
function validateCustomList(customList) {
  if (!Array.isArray(customList)) return [];
  
  return customList.map(item => ({
    customName: item.customName || '',
    startDate: item.startDate || '',
    endDate: item.endDate || '',
    role: item.role || null,
    content: item.content || null
  }));
}

module.exports = {
  convertFromResumeManager,
  convertToResumeManager,
  convertToServerFormat,
  validateBasicInfo,
  validateJobIntention,
  validateEducationList,
  validateSchoolList,
  validateInternshipList,
  validateWorkList,
  validateProjectList,
  validateSkillsList,
  validateAwardsList,
  validateInterestsList,
  validateEvaluationList,
  validateCustomList
};
