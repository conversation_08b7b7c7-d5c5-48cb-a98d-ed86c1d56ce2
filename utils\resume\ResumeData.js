/**
 * 简历数据类 - 基于 resume.py 的 JavaScript 实现
 * 提供统一的简历数据结构和管理方法
 */

/**
 * 基本信息类
 */
class BasicInfo {
  constructor(data = {}) {
    this.name = data.name || '';
    this.gender = data.gender || '';
    this.phone = data.phone || '';
    this.photoUrl = data.photoUrl || '';
    this.city = data.city || '';
    this.email = data.email || '';
    this.wechat = data.wechat || '';
    this.age = data.age || '';
    this.birthday = data.birthday || '';
    this.marriage = data.marriage || '';
    this.politics = data.politics || '';
    this.nation = data.nation || '';
    this.hometown = data.hometown || '';
    this.height = data.height || '';
    this.weight = data.weight || '';
    this.educationLevel = data.educationLevel || '';
    this.customTitle1 = data.customTitle1 || '';
    this.customContent1 = data.customContent1 || '';
    this.customTitle2 = data.customTitle2 || '';
    this.customContent2 = data.customContent2 || '';
  }

  // 验证必填字段
  validate() {
    const errors = [];
    if (!this.name.trim()) {
      errors.push('姓名不能为空');
    }
    if (!this.phone.trim()) {
      errors.push('手机号不能为空');
    }
    return errors;
  }

  // 转换为普通对象
  toObject() {
    return {
      name: this.name,
      gender: this.gender,
      phone: this.phone,
      photoUrl: this.photoUrl,
      city: this.city,
      email: this.email,
      wechat: this.wechat,
      age: this.age,
      birthday: this.birthday,
      marriage: this.marriage,
      politics: this.politics,
      nation: this.nation,
      hometown: this.hometown,
      height: this.height,
      weight: this.weight,
      educationLevel: this.educationLevel,
      customTitle1: this.customTitle1,
      customContent1: this.customContent1,
      customTitle2: this.customTitle2,
      customContent2: this.customContent2
    };
  }
}

/**
 * 求职意向类
 */
class JobIntention {
  constructor(data = {}) {
    this.position = data.position || '';
    this.city = data.city || '';
    this.salary = data.salary || '';
    this.status = data.status || '';
  }

  toObject() {
    return {
      position: this.position,
      city: this.city,
      salary: this.salary,
      status: this.status
    };
  }
}

/**
 * 教育经历项
 */
class EducationItem {
  constructor(data = {}) {
    this.school = data.school || '';
    this.major = data.major || '';
    this.degree = data.degree || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.description = data.description || '';
  }

  validate() {
    const errors = [];
    if (!this.school.trim()) errors.push('学校名称不能为空');
    if (!this.major.trim()) errors.push('专业不能为空');
    if (!this.degree.trim()) errors.push('学历不能为空');
    return errors;
  }

  toObject() {
    return {
      school: this.school,
      major: this.major,
      degree: this.degree,
      startDate: this.startDate,
      endDate: this.endDate,
      description: this.description
    };
  }
}

/**
 * 工作经历项
 */
class WorkItem {
  constructor(data = {}) {
    this.company = data.company || '';
    this.position = data.position || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.description = data.description || '';
  }

  validate() {
    const errors = [];
    if (!this.company.trim()) errors.push('公司名称不能为空');
    if (!this.position.trim()) errors.push('职位不能为空');
    return errors;
  }

  toObject() {
    return {
      company: this.company,
      position: this.position,
      startDate: this.startDate,
      endDate: this.endDate,
      description: this.description
    };
  }
}

/**
 * 项目经历项
 */
class ProjectItem {
  constructor(data = {}) {
    this.projectName = data.projectName || '';
    this.role = data.role || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.description = data.description || '';
  }

  validate() {
    const errors = [];
    if (!this.projectName.trim()) errors.push('项目名称不能为空');
    if (!this.role.trim()) errors.push('担任角色不能为空');
    return errors;
  }

  toObject() {
    return {
      projectName: this.projectName,
      role: this.role,
      startDate: this.startDate,
      endDate: this.endDate,
      description: this.description
    };
  }
}

/**
 * 自定义经历项
 */
class CustomItem {
  constructor(data = {}) {
    this.customName = data.customName || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.role = data.role || '';
    this.content = data.content || '';
  }

  toObject() {
    return {
      customName: this.customName,
      startDate: this.startDate,
      endDate: this.endDate,
      role: this.role,
      content: this.content
    };
  }
}

/**
 * 在校经历项
 */
class SchoolExperienceItem {
  constructor(data = {}) {
    this.role = data.role || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.content = data.content || '';
  }

  toObject() {
    return {
      role: this.role,
      startDate: this.startDate,
      endDate: this.endDate,
      content: this.content
    };
  }
}

/**
 * 实习经历项
 */
class InternshipItem {
  constructor(data = {}) {
    this.company = data.company || '';
    this.position = data.position || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.content = data.content || '';
  }

  validate() {
    const errors = [];
    if (!this.company.trim()) errors.push('公司名称不能为空');
    if (!this.position.trim()) errors.push('职位不能为空');
    return errors;
  }

  toObject() {
    return {
      company: this.company,
      position: this.position,
      startDate: this.startDate,
      endDate: this.endDate,
      content: this.content
    };
  }
}

/**
 * 兴趣爱好管理类
 */
class InterestsManager {
  constructor(data = []) {
    // 确保最多16个兴趣爱好，不足的用空字符串填充
    this.items = [];
    if (Array.isArray(data)) {
      // 取前16个有效项
      for (let i = 0; i < 16; i++) {
        this.items[i] = (data[i] && typeof data[i] === 'string') ? data[i] : '';
      }
    } else {
      // 如果不是数组，初始化为16个空字符串
      for (let i = 0; i < 16; i++) {
        this.items[i] = '';
      }
    }
  }

  // 获取所有项（包括空项）
  getAllItems() {
    return [...this.items];
  }

  // 获取有效项（过滤空字符串）
  getValidItems() {
    return this.items.filter(item => item && item.trim() !== '');
  }

  // 设置指定索引的项
  setItem(index, value) {
    if (index >= 0 && index < 16) {
      this.items[index] = value || '';
    }
  }

  // 获取指定索引的项
  getItem(index) {
    if (index >= 0 && index < 16) {
      return this.items[index];
    }
    return '';
  }

  // 清空所有项
  clear() {
    for (let i = 0; i < 16; i++) {
      this.items[i] = '';
    }
  }

  // 转换为普通数组（只包含有效项）
  toArray() {
    return this.getValidItems();
  }

  // 转换为普通对象
  toObject() {
    return {
      items: this.getAllItems(),
      validItems: this.getValidItems()
    };
  }
}

/**
 * 技能特长管理类
 */
class SkillsManager {
  constructor(data = []) {
    // 确保最多16个技能特长，不足的用空字符串填充
    this.items = [];
    if (Array.isArray(data)) {
      // 取前16个有效项
      for (let i = 0; i < 16; i++) {
        this.items[i] = (data[i] && typeof data[i] === 'string') ? data[i] : '';
      }
    } else {
      // 如果不是数组，初始化为16个空字符串
      for (let i = 0; i < 16; i++) {
        this.items[i] = '';
      }
    }
  }

  // 获取所有项（包括空项）
  getAllItems() {
    return [...this.items];
  }

  // 获取有效项（过滤空字符串）
  getValidItems() {
    return this.items.filter(item => item && item.trim() !== '');
  }

  // 设置指定索引的项
  setItem(index, value) {
    if (index >= 0 && index < 16) {
      this.items[index] = value || '';
    }
  }

  // 获取指定索引的项
  getItem(index) {
    if (index >= 0 && index < 16) {
      return this.items[index];
    }
    return '';
  }

  // 清空所有项
  clear() {
    for (let i = 0; i < 16; i++) {
      this.items[i] = '';
    }
  }

  // 转换为普通数组（只包含有效项）
  toArray() {
    return this.getValidItems();
  }

  // 转换为普通对象
  toObject() {
    return {
      items: this.getAllItems(),
      validItems: this.getValidItems()
    };
  }
}

/**
 * 奖项证书管理类
 */
class AwardsManager {
  constructor(data = []) {
    // 确保最多16个奖项证书，不足的用空字符串填充
    this.items = [];
    if (Array.isArray(data)) {
      // 取前16个有效项
      for (let i = 0; i < 16; i++) {
        this.items[i] = (data[i] && typeof data[i] === 'string') ? data[i] : '';
      }
    } else {
      // 如果不是数组，初始化为16个空字符串
      for (let i = 0; i < 16; i++) {
        this.items[i] = '';
      }
    }
  }

  // 获取所有项（包括空项）
  getAllItems() {
    return [...this.items];
  }

  // 获取有效项（过滤空字符串）
  getValidItems() {
    return this.items.filter(item => item && item.trim() !== '');
  }

  // 设置指定索引的项
  setItem(index, value) {
    if (index >= 0 && index < 16) {
      this.items[index] = value || '';
    }
  }

  // 获取指定索引的项
  getItem(index) {
    if (index >= 0 && index < 16) {
      return this.items[index];
    }
    return '';
  }

  // 清空所有项
  clear() {
    for (let i = 0; i < 16; i++) {
      this.items[i] = '';
    }
  }

  // 转换为普通数组（只包含有效项）
  toArray() {
    return this.getValidItems();
  }

  // 转换为普通对象
  toObject() {
    return {
      items: this.getAllItems(),
      validItems: this.getValidItems()
    };
  }
}

/**
 * 主简历数据类
 */
class ResumeData {
  constructor(data = {}) {
    // 模块顺序
    this.moduleOrders = data.moduleOrders || {};

    // 基本信息
    this.basicInfo = new BasicInfo(data.basicInfo);

    // 求职意向
    this.jobIntention = new JobIntention(data.jobIntention);

    // 教育经历
    this.education = (data.education || []).map(item => new EducationItem(item));

    // 在校经历
    this.school = (data.school || []).map(item => new SchoolExperienceItem(item));

    // 实习经历
    this.internship = (data.internship || []).map(item => new InternshipItem(item));

    // 工作经历
    this.work = (data.work || []).map(item => new WorkItem(item));

    // 项目经历
    this.project = (data.project || []).map(item => new ProjectItem(item));

    // 技能特长
    this.skills = new SkillsManager(data.skills);

    // 获奖情况
    this.awards = new AwardsManager(data.awards);

    // 兴趣爱好
    this.interests = new InterestsManager(data.interests);

    // 自我评价
    this.evaluation = data.evaluation || [];

    // 自定义模块
    this.custom1 = (data.custom1 || []).map(item => new CustomItem(item));
    this.custom2 = (data.custom2 || []).map(item => new CustomItem(item));
    this.custom3 = (data.custom3 || []).map(item => new CustomItem(item));

    // 元数据
    this.id = data.id || '';
    this.title = data.title || '新建简历';
    this.createTime = data.createTime || Date.now();
    this.updateTime = data.updateTime || Date.now();
  }

  // 验证整个简历数据
  validate() {
    const errors = [];

    // 验证基本信息
    const basicInfoErrors = this.basicInfo.validate();
    if (basicInfoErrors.length > 0) {
      errors.push(...basicInfoErrors.map(err => `基本信息: ${err}`));
    }

    // 验证教育经历
    this.education.forEach((item, index) => {
      const itemErrors = item.validate();
      if (itemErrors.length > 0) {
        errors.push(...itemErrors.map(err => `教育经历${index + 1}: ${err}`));
      }
    });

    // 验证工作经历
    this.work.forEach((item, index) => {
      const itemErrors = item.validate();
      if (itemErrors.length > 0) {
        errors.push(...itemErrors.map(err => `工作经历${index + 1}: ${err}`));
      }
    });

    return errors;
  }

  // 转换为普通对象（用于存储和传输）
  toObject() {
    return {
      id: this.id,
      title: this.title,
      createTime: this.createTime,
      updateTime: this.updateTime,
      moduleOrders: this.moduleOrders,
      basicInfo: this.basicInfo.toObject(),
      jobIntention: this.jobIntention.toObject(),
      education: this.education.map(item => item.toObject()),
      school: this.school.map(item => item.toObject()),
      internship: this.internship.map(item => item.toObject()),
      work: this.work.map(item => item.toObject()),
      project: this.project.map(item => item.toObject()),
      skills: this.skills.toArray(),
      awards: this.awards.toArray(),
      interests: this.interests.toArray(),
      evaluation: this.evaluation,
      custom1: this.custom1.map(item => item.toObject()),
      custom2: this.custom2.map(item => item.toObject()),
      custom3: this.custom3.map(item => item.toObject())
    };
  }

  // 转换为 JSON 字符串
  toJSON() {
    return JSON.stringify(this.toObject());
  }

  // 从普通对象创建实例
  static fromObject(obj) {
    return new ResumeData(obj);
  }

  // 从 JSON 字符串创建实例
  static fromJSON(jsonStr) {
    try {
      const obj = JSON.parse(jsonStr);
      return ResumeData.fromObject(obj);
    } catch (error) {
      console.error('解析简历数据失败:', error);
      return new ResumeData();
    }
  }

  // 更新时间戳
  touch() {
    this.updateTime = Date.now();
  }

  // 克隆实例
  clone() {
    return ResumeData.fromObject(this.toObject());
  }
}

module.exports = {
  ResumeData,
  BasicInfo,
  JobIntention,
  EducationItem,
  WorkItem,
  ProjectItem,
  CustomItem,
  SchoolExperienceItem,
  InternshipItem,
  InterestsManager,
  SkillsManager,
  AwardsManager
};
