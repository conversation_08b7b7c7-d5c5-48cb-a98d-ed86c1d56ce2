/**
 * 网络连接检测工具
 * 用于检测网络状态和服务器连通性
 */

/**
 * 检查网络连接状态
 * @returns {Promise<Object>} 网络状态信息
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        const networkType = res.networkType;
        const isConnected = networkType !== 'none';
        
        resolve({
          isConnected,
          networkType,
          message: isConnected ? '网络连接正常' : '网络未连接'
        });
      },
      fail: () => {
        resolve({
          isConnected: false,
          networkType: 'unknown',
          message: '无法获取网络状态'
        });
      }
    });
  });
}

/**
 * 检查服务器连通性
 * @param {string} baseUrl 服务器基础URL
 * @returns {Promise<Object>} 连通性检测结果
 */
function checkServerConnectivity(baseUrl) {
  return new Promise((resolve) => {
    // 使用简单的请求测试服务器连通性
    wx.request({
      url: baseUrl + '/health', // 假设有健康检查接口
      method: 'GET',
      timeout: 5000,
      success: () => {
        resolve({
          isConnected: true,
          message: '服务器连接正常'
        });
      },
      fail: (err) => {
        let message = '服务器连接失败';
        
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            message = '服务器响应超时';
          } else if (err.errMsg.includes('CONNECTION_REFUSED')) {
            message = '服务器拒绝连接';
          }
        }
        
        resolve({
          isConnected: false,
          message,
          error: err
        });
      }
    });
  });
}

/**
 * 综合网络检测
 * @param {string} baseUrl 服务器基础URL
 * @returns {Promise<Object>} 综合检测结果
 */
async function performNetworkCheck(baseUrl) {
  console.log('开始网络连接检测...');
  
  // 检查本地网络状态
  const networkStatus = await checkNetworkStatus();
  console.log('本地网络状态:', networkStatus);
  
  if (!networkStatus.isConnected) {
    return {
      success: false,
      message: '设备未连接网络，请检查网络设置',
      details: networkStatus
    };
  }
  
  // 检查服务器连通性
  const serverStatus = await checkServerConnectivity(baseUrl);
  console.log('服务器连通性:', serverStatus);
  
  return {
    success: serverStatus.isConnected,
    message: serverStatus.isConnected ? '网络连接正常' : serverStatus.message,
    details: {
      network: networkStatus,
      server: serverStatus
    }
  };
}

/**
 * 显示网络状态提示
 * @param {Object} checkResult 检测结果
 */
function showNetworkStatus(checkResult) {
  const title = checkResult.success ? '网络正常' : '网络异常';
  const icon = checkResult.success ? 'success' : 'none';
  
  wx.showToast({
    title: checkResult.message,
    icon: icon,
    duration: 2000
  });
}

module.exports = {
  checkNetworkStatus,
  checkServerConnectivity,
  performNetworkCheck,
  showNetworkStatus
};
