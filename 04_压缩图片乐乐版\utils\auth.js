import { getOpenIdUrl, getUserLoginUrl, APP_CONFIG } from '@/config/api.js'
import { apiPost } from '@/utils/apiRequest.js'

/**
 * 获取用户OpenID
 * @returns {Promise<string>} 用户OpenID
 */
export async function getOpenId() {
  try {
    // 尝试从缓存获取OpenID
    const cachedOpenId = uni.getStorageSync('openid');
    if (cachedOpenId) {
      console.log('使用缓存的OpenID:', cachedOpenId);
      return cachedOpenId;
    }

    // 获取登录凭证
    const loginResult = await new Promise((resolve, reject) => {
      uni.login({
        success: resolve,
        fail: reject
      });
    });

    // 使用登录凭证获取OpenID（仍使用media-check服务）
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: getOpenIdUrl(),
        method: 'POST',
        data: {
          code: loginResult.code,
          appId: APP_CONFIG.APP_ID
        },
        success: resolve,
        fail: reject
      });
    });

    if (response.statusCode === 200 && response.data.code === 0) {
      const openid = response.data.data.openid;
      // 缓存OpenID
      uni.setStorageSync('openid', openid);
      console.log('获取新的OpenID:', openid);
      return openid;
    } else {
      throw new Error('获取OpenID失败: ' + (response.data.msg || '未知错误'));
    }
  } catch (error) {
    console.error('获取OpenID失败:', error);
    throw error;
  }
}

/**
 * 用户登录到图像压缩服务
 * @param {Object} userInfo 用户信息（可选）
 * @returns {Promise<Object>} 登录结果
 */
export async function loginToImageCompressionService(userInfo = null) {
  try {
    console.log('🚀 开始登录到图像压缩服务...');

    // 获取OpenID
    const openid = await getOpenId();

    // 构建登录请求数据
    const loginData = {
      openid: openid,
      appId: APP_CONFIG.APP_ID
    };

    // 如果提供了用户信息，添加到请求中
    if (userInfo) {
      loginData.userInfo = userInfo;
    }

    // 调用图像压缩服务的登录接口
    const result = await apiPost(getUserLoginUrl(), loginData, { needAuth: false });

    console.log('✅ 登录成功:', result);

    // 缓存用户信息
    if (result.data && result.data.user) {
      uni.setStorageSync('userInfo', result.data.user);
    }

    return result;
  } catch (error) {
    console.error('❌ 登录失败:', error);
    throw error;
  }
}

/**
 * 获取缓存的用户信息
 * @returns {Object|null} 用户信息
 */
export function getCachedUserInfo() {
  try {
    return uni.getStorageSync('userInfo') || null;
  } catch (error) {
    console.error('获取缓存用户信息失败:', error);
    return null;
  }
}

/**
 * 清除用户认证信息
 */
export function clearAuthInfo() {
  try {
    uni.removeStorageSync('openid');
    uni.removeStorageSync('userInfo');
    console.log('✅ 已清除用户认证信息');
  } catch (error) {
    console.error('清除认证信息失败:', error);
  }
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isUserLoggedIn() {
  const openid = uni.getStorageSync('openid');
  const userInfo = uni.getStorageSync('userInfo');
  return !!(openid && userInfo);
}
