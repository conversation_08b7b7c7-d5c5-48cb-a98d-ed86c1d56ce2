.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 140rpx;
}

.formGroup {
  background: #fff;
  padding: 0 30rpx;
}

.formItem {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
  padding-right: 20rpx;
}

.input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
}

.deleteIcon {
  padding: 20rpx;
  color: #999;
  font-size: 32rpx;
}

.buttonGroup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 120rpx;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  z-index: 100;
  gap: 40rpx;
}

.saveBtn {
  flex: 3;
  height: 80rpx;
  line-height: 80rpx;
  background: #4B8BF5;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.deleteBtn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pickerContent {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  color: #333;
}

/* 当没有选择时的颜色 */
.pickerContent:empty::before {
  content: attr(placeholder);
  color: #999;
} 