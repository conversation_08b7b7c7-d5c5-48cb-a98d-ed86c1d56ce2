/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #07C160; // 微信绿
$uni-color-success: #07C160; // 微信绿
$uni-color-warning: #FA9D3B; // 微信橙
$uni-color-error: #FA5151; // 微信红

/* 文字基本颜色 */
$uni-text-color: #333333; // 主要文字颜色，微信风格
$uni-text-color-inverse: #ffffff; // 反色
$uni-text-color-grey: #999999; // 辅助灰色，微信风格
$uni-text-color-placeholder: #666666; // 占位符文字颜色，微信风格
$uni-text-color-disable: #cccccc; // 禁用状态文字颜色，微信风格

/* 背景颜色 */
$uni-bg-color: #ffffff; // 白色背景，微信风格
$uni-bg-color-grey: #F7F7F7; // 微信灰色背景
$uni-bg-color-hover: #EEEEEE; // 点击状态颜色，微信风格
$uni-bg-color-mask: rgba(0, 0, 0, 0.5); // 遮罩颜色，微信风格

/* 边框颜色 */
$uni-border-color: #EEEEEE; // 边框颜色，微信风格

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:12px;
$uni-font-size-base:14px;
$uni-font-size-lg:16px;

/* 图片尺寸 */
$uni-img-size-sm:20px;
$uni-img-size-base:26px;
$uni-img-size-lg:40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #333333; // 文章标题颜色，微信风格
$uni-font-size-title:20px;
$uni-color-subtitle: #666666; // 二级标题颜色，微信风格
$uni-font-size-subtitle:26px;
$uni-color-paragraph: #333333; // 文章段落颜色，微信风格
$uni-font-size-paragraph:15px;

/* 自定义主题色变量 - 微信风格 */
$theme-color-primary: #07C160; // 主题色：微信绿
$theme-color-primary-light: #09D66E; // 主题色浅色变体
$theme-color-primary-dark: #06AD56; // 主题色深色变体
$theme-color-primary-gradient: linear-gradient(145deg, #07C160, #06AD56); // 主题渐变色
$theme-color-primary-transparent: rgba(7, 193, 96, 0.1); // 主题色透明版本

/* 辅助色 */
$theme-color-link: #576B95; // 链接/高亮文字颜色
$theme-color-warning: #FA9D3B; // 警告色：橙色
$theme-color-error: #FA5151; // 错误色：红色
$theme-color-success: #07C160; // 成功色：绿色（同主题色）

/* 背景和文字颜色 */
$theme-bg-color: #F7F7F7; // 背景色：微信灰
$theme-bg-color-white: #FFFFFF; // 卡片背景色：白色
$theme-text-primary: #333333; // 主要文字颜色
$theme-text-secondary: #666666; // 次要文字颜色
$theme-text-tertiary: #999999; // 辅助文字颜色
$theme-border-color: #EEEEEE; // 边框颜色
$theme-shadow-dark: rgba(0, 0, 0, 0.1); // 暗部阴影
$theme-shadow-light: rgba(255, 255, 255, 0.9); // 亮部阴影
