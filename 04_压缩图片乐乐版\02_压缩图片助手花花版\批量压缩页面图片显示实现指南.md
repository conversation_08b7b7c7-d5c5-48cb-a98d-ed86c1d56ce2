# 批量压缩页面图片显示实现指南

本文档详细记录了微信小程序批量图片压缩页面的图片显示实现过程，可作为其他套壳程序的参考指南。

## 目录

- [整体架构](#整体架构)
- [图片列表实现](#图片列表实现)
- [单个图片项实现](#单个图片项实现)
- [图片状态显示](#图片状态显示)
- [样式设计](#样式设计)
- [交互逻辑](#交互逻辑)
- [完整代码示例](#完整代码示例)

## 整体架构

批量压缩页面的图片显示采用Vue框架结合微信小程序组件实现，主要包含以下部分：

1. **图片列表容器**：使用Vue的`v-for`指令渲染多个图片项
2. **单个图片项**：包含预览图、信息区和删除按钮
3. **空状态提示**：当没有选择图片时显示的上传提示
4. **底部操作区**：包含选择图片、压缩等功能按钮

## 图片列表实现

图片列表使用Vue的列表渲染功能，遍历`imagesList`数组显示每个图片项：

```html
<view class="image-list">
  <view class="section-title">
    <text>已选择图片 ({{imagesList.length}})</text>
    <view class="clear-btn" v-if="imagesList.length > 0" @click="clearImages">清空</view>
  </view>
  
  <!-- 图片项列表 -->
  <view class="image-item" v-for="(item, index) in imagesList" :key="index">
    <!-- 图片项内容 -->
  </view>
  
  <!-- 提示信息 -->
  <view class="tip-text" v-if="imagesList.length > 0">
    <text>压缩在微信本地完成，您的照片不会被泄露</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" v-if="imagesList.length === 0" @tap="selectImages">
    <image src="/static/upload.svg" mode="aspectFit" class="upload-icon"></image>
    <text>请选择需要压缩的图片</text>
    <text class="click-hint">点击此处选择图片</text>
  </view>
</view>
```

### 数据结构

`imagesList`数组中的每个图片对象包含以下属性：

```javascript
{
  path: "临时文件路径",           // 原始图片路径
  width: 1920,                  // 原始宽度
  height: 1080,                 // 原始高度
  originalSize: 1024,           // 原始大小(KB)
  compressedPath: "压缩后路径",   // 压缩后的图片路径
  compressedWidth: 960,         // 压缩后的宽度
  compressedHeight: 540,        // 压缩后的高度
  compressedSize: 256           // 压缩后的大小(KB)
}
```

## 单个图片项实现

每个图片项包含三个主要部分：预览图、信息区和删除按钮。

```html
<view class="image-item">
  <!-- 预览图 -->
  <image class="preview-image" :src="item.path" mode="aspectFit"></image>
  
  <!-- 信息区 -->
  <view class="image-info">
    <view class="info-line">
      <text>压缩前：</text>
      <text>尺寸: {{item.width}} × {{item.height}} 大小: {{item.originalSize}} KB</text>
    </view>
    <view class="info-line">
      <text>压缩后：</text>
      <text v-if="item.compressedSize">尺寸: {{item.compressedWidth || item.width}} × {{item.compressedHeight || item.height}} 大小: {{item.compressedSize}} KB</text>
      <text v-else>尺寸: {{compressMode === 'custom' ? customWidth + ' × ' + customHeight : Math.floor(item.width * size / 100) + ' × ' + Math.floor(item.height * size / 100)}} 大小: -- KB</text>
    </view>
    <view class="progress-bar" v-if="compressing && currentIndex === index">
      <view class="progress-inner" :style="{width: '100%'}"></view>
    </view>
  </view>
  
  <!-- 删除按钮 -->
  <view class="delete-btn" @click="deleteImage(index)">×</view>
</view>
```

### 预览图

预览图使用微信小程序的`<image>`组件实现：

```html
<image class="preview-image" :src="item.path" mode="aspectFit"></image>
```

- `:src="item.path"`：动态绑定图片路径
- `mode="aspectFit"`：保持图片宽高比，确保图片完整显示

### 信息区

信息区显示压缩前后的图片信息：

```html
<view class="image-info">
  <!-- 压缩前信息 -->
  <view class="info-line">
    <text>压缩前：</text>
    <text>尺寸: {{item.width}} × {{item.height}} 大小: {{item.originalSize}} KB</text>
  </view>
  
  <!-- 压缩后信息（条件渲染） -->
  <view class="info-line">
    <text>压缩后：</text>
    <text v-if="item.compressedSize">尺寸: {{item.compressedWidth || item.width}} × {{item.compressedHeight || item.height}} 大小: {{item.compressedSize}} KB</text>
    <text v-else>尺寸: {{compressMode === 'custom' ? customWidth + ' × ' + customHeight : Math.floor(item.width * size / 100) + ' × ' + Math.floor(item.height * size / 100)}} 大小: -- KB</text>
  </view>
  
  <!-- 进度条（仅在压缩当前图片时显示） -->
  <view class="progress-bar" v-if="compressing && currentIndex === index">
    <view class="progress-inner" :style="{width: '100%'}"></view>
  </view>
</view>
```

### 删除按钮

删除按钮位于图片项右上角，点击可删除对应图片：

```html
<view class="delete-btn" @click="deleteImage(index)">×</view>
```

## 图片状态显示

图片状态显示根据压缩状态动态变化：

### 未压缩状态

显示预估的压缩后尺寸和"-- KB"占位符：

```html
<text v-else>尺寸: {{compressMode === 'custom' ? customWidth + ' × ' + customHeight : Math.floor(item.width * size / 100) + ' × ' + Math.floor(item.height * size / 100)}} 大小: -- KB</text>
```

### 已压缩状态

显示实际压缩后的尺寸和大小：

```html
<text v-if="item.compressedSize">尺寸: {{item.compressedWidth || item.width}} × {{item.compressedHeight || item.height}} 大小: {{item.compressedSize}} KB</text>
```

### 压缩中状态

显示进度条：

```html
<view class="progress-bar" v-if="compressing && currentIndex === index">
  <view class="progress-inner" :style="{width: '100%'}"></view>
</view>
```

## 样式设计

批量压缩页面采用新拟物风格（Neumorphism）设计，主要样式如下：

```scss
// 定义主题色变量
$primary-color: #0EA5E9; // 主题色：科技蓝
$primary-gradient: linear-gradient(145deg, #0EA5E9, #0284C7);
$bg-color: #F7F7F7; // 背景色：微信灰
$text-primary: #2C2C2C; // 主要文字颜色
$text-secondary: #666666; // 次要文字颜色
$shadow-dark: rgba(0, 0, 0, 0.1);
$shadow-light: rgba(255, 255, 255, 0.9);

// 图片项样式
.image-item {
  position: relative;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;

  .preview-image {
    width: 135rpx;
    height: 135rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
  }

  .image-info {
    flex: 1;
    font-size: 26rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .info-line {
      margin-bottom: 8rpx;
      display: flex;
      flex-direction: column;

      text {
        &:first-child {
          color: $text-primary;
          margin-bottom: 2rpx;
        }

        &:last-child {
          color: $text-secondary;
          font-size: 24rpx;
        }
      }
    }

    .progress-bar {
      height: 6rpx;
      width: 100%;
      background-color: rgba(0, 180, 219, 0.1);
      border-radius: 3rpx;
      overflow: hidden;
      margin-top: 4rpx;

      .progress-inner {
        height: 100%;
        background: $primary-gradient;
        transition: width 0.2s linear;
      }
    }
  }

  .delete-btn {
    position: absolute;
    right: 10rpx;
    top: 20rpx;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 32rpx;
  }
}
```

## 交互逻辑

### 图片处理流程

1. **选择图片**：通过`selectImages`方法选择图片
2. **处理图片**：通过`processImages`方法处理选择的图片
3. **更新状态**：通过`updateCompressionStatus`方法更新图片压缩状态
4. **预览压缩**：通过`previewCompression`方法预览第一张图片的压缩效果
5. **批量压缩**：通过`startCompression`方法开始批量压缩

### 关键方法实现

```javascript
// 处理图片列表
async processImages(tempFilePaths) {
  // 显示加载提示
  if (tempFilePaths.length > 0) {
    uni.showLoading({ title: '处理图片中...' });
  }

  // 导入网络工具
  const { requestQueue } = require('@/utils/networkUtils.js');

  // 创建处理任务
  const processTasks = tempFilePaths.map((path, index) => {
    return async () => {
      try {
        // 更新加载提示
        uni.showLoading({ title: `处理图片 ${index+1}/${tempFilePaths.length}` });

        // 获取图片信息
        const [fileInfo, imageInfo] = await Promise.all([
          new Promise((resolve, reject) => {
            const fs = uni.getFileSystemManager();
            fs.getFileInfo({
              filePath: path,
              success: resolve,
              fail: reject
            });
          }),
          new Promise((resolve, reject) => {
            uni.getImageInfo({
              src: path,
              success: resolve,
              fail: reject
            });
          })
        ]);

        return {
          path: path,
          width: imageInfo.width,
          height: imageInfo.height,
          originalSize: Math.round(fileInfo.size / 1024),
          compressedSize: null
        };
      } catch (error) {
        console.error('获取图片信息失败:', error);
        return null;
      }
    };
  });

  // 使用请求队列处理图片，控制并发数量
  for (const task of processTasks) {
    try {
      const result = await requestQueue.add(task);
      if (result) {
        this.imagesList.push(result);
      }
    } catch (error) {
      console.error('处理图片任务失败:', error);
    }
  }

  // 隐藏加载提示
  uni.hideLoading();
}
```

## 完整代码示例

完整的批量压缩页面图片显示实现代码可参考以下文件：

- `pages/batchImageCompression/index.vue` - 页面主文件
- `utils/networkUtils.js` - 网络工具类
- `utils/imageUtils.js` - 图片处理工具类

以上就是批量压缩页面图片显示的完整实现过程，可根据需要在其他套壳程序中进行复用和定制。
