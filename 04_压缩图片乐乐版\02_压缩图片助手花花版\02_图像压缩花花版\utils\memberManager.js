// utils/memberManager.js - 会员状态管理工具

import { getMemberStatusUrl, getOrderListUrl, getPaymentCreateUrl } from '@/config/api.js'
import { apiGet, apiPost, handleApiError } from '@/utils/apiRequest.js'
import { getOpenId } from '@/utils/auth.js'
import { APP_CONFIG } from '@/config/api.js'

// 存储键名
const MEMBER_INFO_KEY = 'member_info'
const MEMBER_ORDERS_KEY = 'member_orders'

// 会员类型枚举
export const MEMBER_TYPES = {
  NORMAL: 'normal',      // 普通用户
  DAY: 'day',           // 日卡会员
  PERMANENT: 'permanent' // 永久会员
}

// 获取今天的日期字符串（YYYY-MM-DD格式）
function getTodayDateString() {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取指定天数后的日期字符串
function getDateAfterDays(days) {
  const date = new Date()
  date.setDate(date.getDate() + days)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 检查日期是否已过期（自然日比较，用于显示）
function isDateExpired(dateString) {
  if (!dateString) return false
  const today = new Date(getTodayDateString())
  const expireDate = new Date(dateString)
  return today > expireDate
}

// 检查精确时间是否已过期（严格24小时制，用于日卡会员判断）
function isTimeExpired(timeString) {
  if (!timeString) return false
  const now = new Date()
  const expireTime = new Date(timeString)
  return now > expireTime
}

// 获取会员信息
export async function getMemberInfo() {
  try {
    console.log('🔍 开始获取会员信息...');

    // 首先尝试从后端API获取最新的会员信息
    try {
      // 获取必要的参数
      const openid = await getOpenId();
      const appId = APP_CONFIG.APP_ID;

      console.log('📋 查询参数:', { openid, appId });

      const result = await apiGet(getMemberStatusUrl(), {
        openid,
        appId
      });

      if (result.success && result.data) {
        const memberData = result.data;
        console.log('🔍 后端返回的原始数据:', memberData);

        // 转换后端数据格式为前端格式
        const memberInfo = {
          type: convertBackendMemberType(memberData.type),
          expireDate: memberData.expireTime ? formatDateString(memberData.expireTime) : null,
          expireTime: memberData.expireTime || null, // 保留精确的过期时间用于严格判断
          isExpired: memberData.isExpired || false,
          remainingDays: memberData.remainingDays || 0
        };

        console.log('🔄 数据转换结果:', {
          原始type: memberData.type,
          转换后type: memberInfo.type,
          原始expireTime: memberData.expireTime,
          转换后expireDate: memberInfo.expireDate
        });

        // 缓存到本地存储
        uni.setStorageSync(MEMBER_INFO_KEY, memberInfo);

        console.log('✅ 从API获取会员信息成功:', memberInfo);
        return memberInfo;
      }
    } catch (apiError) {
      console.warn('⚠️ API获取会员信息失败，使用本地缓存:', apiError.message);
    }

    // 如果API失败，从本地存储获取会员信息
    const memberInfo = uni.getStorageSync(MEMBER_INFO_KEY)

    if (!memberInfo) {
      // 如果没有会员信息，返回默认的普通用户状态
      const defaultInfo = {
        type: MEMBER_TYPES.NORMAL,
        expireDate: null,
        isExpired: false,
        remainingDays: 0
      };

      console.log('📝 返回默认会员信息:', defaultInfo);
      return defaultInfo;
    }

    // 检查会员是否过期（仅对日卡会员使用严格24小时制）
    if (memberInfo.type === MEMBER_TYPES.DAY) {
      // 对日卡会员使用精确时间判断（严格24小时）
      const isExpired = memberInfo.expireTime ? isTimeExpired(memberInfo.expireTime) : isDateExpired(memberInfo.expireDate)
      if (isExpired) {
        // 如果已过期，更新为普通用户
        const expiredMemberInfo = {
          type: MEMBER_TYPES.NORMAL,
          expireDate: null,
          expireTime: null,
          isExpired: true,
          remainingDays: 0
        }
        uni.setStorageSync(MEMBER_INFO_KEY, expiredMemberInfo)
        console.log('⏰ 日卡会员已过期（严格24小时），更新状态:', expiredMemberInfo);
        return expiredMemberInfo
      }
    }

    console.log('📱 使用本地缓存会员信息:', memberInfo);
    return {
      ...memberInfo,
      isExpired: false
    }
  } catch (error) {
    console.error('❌ 获取会员信息失败:', error)
    handleApiError(error, { showToast: false });

    return {
      type: MEMBER_TYPES.NORMAL,
      expireDate: null,
      isExpired: false,
      remainingDays: 0
    }
  }
}

// 更新会员信息
export async function updateMemberInfo(memberType, days = null, expireTime = null) {
  try {
    let memberInfo = {
      type: memberType,
      expireDate: null,
      expireTime: null,
      isExpired: false
    }

    // 如果是日卡会员，计算到期时间
    if (memberType === MEMBER_TYPES.DAY) {
      if (expireTime) {
        // 如果提供了精确过期时间，使用精确时间
        memberInfo.expireTime = expireTime
        memberInfo.expireDate = formatDateString(expireTime)
      } else if (days) {
        // 否则使用天数计算（兼容旧逻辑）
        memberInfo.expireDate = getDateAfterDays(days)
      }
    }

    // 保存到本地存储
    uni.setStorageSync(MEMBER_INFO_KEY, memberInfo)

    return memberInfo
  } catch (error) {
    console.error('更新会员信息失败:', error)
    throw error
  }
}

// 检查用户是否为有效会员
export async function isValidMember() {
  const memberInfo = await getMemberInfo()
  return memberInfo.type !== MEMBER_TYPES.NORMAL && !memberInfo.isExpired
}

// 检查用户是否为永久会员
export async function isPermanentMember() {
  const memberInfo = await getMemberInfo()
  return memberInfo.type === MEMBER_TYPES.PERMANENT
}

// 检查用户是否为日卡会员
export async function isDayMember() {
  const memberInfo = await getMemberInfo()
  if (memberInfo.type !== MEMBER_TYPES.DAY) return false

  // 对日卡会员进行严格的时间检查
  if (memberInfo.expireTime) {
    return !isTimeExpired(memberInfo.expireTime)
  }

  // 兼容旧数据，使用日期判断
  return !memberInfo.isExpired
}

// 获取会员剩余时间（仅对日卡会员有效）
export async function getMemberRemainingTime() {
  const memberInfo = await getMemberInfo()

  if (memberInfo.type !== MEMBER_TYPES.DAY) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 }
  }

  let expireTime
  if (memberInfo.expireTime) {
    // 使用精确过期时间
    expireTime = new Date(memberInfo.expireTime)
  } else if (memberInfo.expireDate) {
    // 兼容旧数据，使用日期（设为当天23:59:59）
    expireTime = new Date(memberInfo.expireDate + ' 23:59:59')
  } else {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 }
  }

  const now = new Date()
  const diffTime = Math.max(0, expireTime - now)

  const days = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diffTime % (1000 * 60)) / 1000)

  return { days, hours, minutes, seconds }
}

// 获取会员剩余天数（兼容旧接口）
export async function getMemberRemainingDays() {
  const timeInfo = await getMemberRemainingTime()
  return timeInfo.days
}

// 添加会员订单记录
export async function addMemberOrder(orderInfo) {
  try {
    const orders = uni.getStorageSync(MEMBER_ORDERS_KEY) || []
    
    const newOrder = {
      id: generateOrderId(),
      ...orderInfo,
      createTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      status: 'completed' // 默认为已完成状态
    }
    
    orders.unshift(newOrder) // 添加到数组开头
    uni.setStorageSync(MEMBER_ORDERS_KEY, orders)
    
    return newOrder
  } catch (error) {
    console.error('添加会员订单失败:', error)
    throw error
  }
}

// 获取会员订单列表
export async function getMemberOrders() {
  try {
    console.log('🔍 开始获取会员订单...');

    // 首先尝试从后端API获取最新的订单信息
    try {
      const result = await apiGet(getOrderListUrl());

      if (result.success && result.data && Array.isArray(result.data.orders)) {
        const orders = result.data.orders.map(order => ({
          id: order.orderId,
          title: order.productName,
          originalPrice: order.amount, // 使用 amount 作为原价
          actualPrice: order.amount,   // 使用 amount 作为实际价格
          payMethod: '微信支付',
          createTime: formatDateTimeString(order.createdAt), // 修正字段名
          status: order.paymentStatus // 直接使用后端返回的支付状态
        }));

        // 缓存到本地存储
        uni.setStorageSync(MEMBER_ORDERS_KEY, orders);

        console.log('✅ 从API获取订单成功:', orders.length, '条');
        return orders;
      }
    } catch (apiError) {
      console.warn('⚠️ API获取订单失败，使用本地缓存:', apiError.message);
    }

    // 如果API失败，从本地存储获取订单信息
    const orders = uni.getStorageSync(MEMBER_ORDERS_KEY) || [];
    console.log('📱 使用本地缓存订单:', orders.length, '条');
    return orders;
  } catch (error) {
    console.error('❌ 获取会员订单失败:', error);
    handleApiError(error, { showToast: false });
    return [];
  }
}

// 生成订单ID
function generateOrderId() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `M${year}${month}${day}${random}`
}

import { getDayCardProduct, getPermanentProduct } from '@/utils/productManager.js'

// 获取会员套餐配置（从API动态获取）
export async function getMemberPackages(forceRefresh = true) {
  try {
    console.log('🔍 [MemberManager] 开始获取会员套餐...', forceRefresh ? '(强制刷新)' : '(使用缓存)');

    const [dayCardProduct, permanentProduct] = await Promise.all([
      getDayCardProduct(forceRefresh),
      getPermanentProduct(forceRefresh)
    ]);

    console.log('📦 [MemberManager] 日卡产品:', JSON.stringify(dayCardProduct, null, 2));
    console.log('📦 [MemberManager] 永久产品:', JSON.stringify(permanentProduct, null, 2));

    const packages = {};

    if (dayCardProduct) {
      packages.DAY_CARD = {
        type: MEMBER_TYPES.DAY,
        name: dayCardProduct.name,
        days: Math.floor(dayCardProduct.durationHours / 24) || 1,
        originalPrice: dayCardProduct.originalPrice,
        currentPrice: dayCardProduct.price,
        description: dayCardProduct.description,
        productId: dayCardProduct.id
      };
      console.log('✅ [MemberManager] 日卡套餐配置:', JSON.stringify(packages.DAY_CARD, null, 2));
    } else {
      console.log('❌ [MemberManager] 日卡产品为空');
    }

    if (permanentProduct) {
      packages.PERMANENT = {
        type: MEMBER_TYPES.PERMANENT,
        name: permanentProduct.name,
        days: null,
        originalPrice: permanentProduct.originalPrice,
        currentPrice: permanentProduct.price,
        description: permanentProduct.description,
        productId: permanentProduct.id
      };
      console.log('✅ [MemberManager] 永久套餐配置:', JSON.stringify(packages.PERMANENT, null, 2));
    } else {
      console.log('❌ [MemberManager] 永久产品为空');
    }

    console.log('🎯 [MemberManager] 最终套餐配置:', JSON.stringify(packages, null, 2));
    return packages;
  } catch (error) {
    console.error('获取会员套餐配置失败:', error);

    // 返回默认配置
    return {
      DAY_CARD: {
        type: MEMBER_TYPES.DAY,
        name: '日卡会员',
        days: 1,
        originalPrice: 6.99,
        currentPrice: 4.99,
        description: '24小时无限压缩',
        productId: 'day_card_default'
      },
      PERMANENT: {
        type: MEMBER_TYPES.PERMANENT,
        name: '永久会员',
        days: null,
        originalPrice: 29.99,
        currentPrice: 19.99,
        description: '永久无限压缩',
        productId: 'permanent_default'
      }
    };
  }
}

// 购买会员（真实微信支付）
export async function purchaseMember(packageType) {
  try {
    console.log('🛒 开始购买会员套餐:', packageType);

    // 获取最新的套餐配置
    const packages = await getMemberPackages();
    const packageInfo = packages[packageType];

    if (!packageInfo) {
      throw new Error('无效的会员套餐类型');
    }

    // 获取用户OpenID
    const openid = await getOpenId();
    if (!openid) {
      throw new Error('获取用户信息失败，请重新登录');
    }

    console.log('💰 创建支付订单...', packageInfo);

    // 映射套餐类型到产品代码
    const productCodeMap = {
      'day': 'day_card',
      'permanent': 'permanent'
    };

    const productCode = packageInfo.productCode || productCodeMap[packageInfo.type];

    if (!productCode) {
      throw new Error('无法确定产品代码');
    }

    console.log('📦 产品代码:', productCode);

    // 调用后端创建支付订单
    const orderResult = await apiPost(getPaymentCreateUrl(), {
      openid: openid,
      appId: APP_CONFIG.APP_ID,
      productCode: productCode
    });

    if (!orderResult.success) {
      throw new Error(orderResult.error || '创建支付订单失败');
    }

    console.log('📦 支付订单创建成功:', orderResult.data);

    // 调用微信支付
    const paymentResult = await callWechatPay(orderResult.data.paymentParams);

    if (paymentResult.success) {
      // 支付成功，刷新会员信息
      await refreshMemberInfo();

      return {
        success: true,
        orderId: orderResult.data.orderId,
        message: '支付成功，会员已激活'
      };
    } else {
      throw new Error(paymentResult.error || '支付失败');
    }
  } catch (error) {
    console.error('❌ 购买会员失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 调用微信支付
 */
async function callWechatPay(paymentParams) {
  return new Promise((resolve) => {
    console.log('💳 调用微信支付:', paymentParams);

    uni.requestPayment({
      provider: 'wxpay',
      timeStamp: paymentParams.timeStamp,
      nonceStr: paymentParams.nonceStr,
      package: paymentParams.package,
      signType: paymentParams.signType,
      paySign: paymentParams.paySign,
      success: (res) => {
        console.log('✅ 微信支付成功:', res);
        resolve({
          success: true,
          data: res
        });
      },
      fail: (err) => {
        console.error('❌ 微信支付失败:', err);
        let errorMessage = '支付失败';

        if (err.errMsg) {
          if (err.errMsg.includes('cancel')) {
            errorMessage = '用户取消支付';
          } else if (err.errMsg.includes('fail')) {
            errorMessage = '支付失败，请重试';
          }
        }

        resolve({
          success: false,
          error: errorMessage
        });
      }
    });
  });
}

// 重置会员状态（用于测试）
export async function resetMemberStatus() {
  try {
    uni.removeStorageSync(MEMBER_INFO_KEY)
    uni.removeStorageSync(MEMBER_ORDERS_KEY)
    return true
  } catch (error) {
    console.error('重置会员状态失败:', error)
    return false
  }
}

// 辅助函数：转换后端会员类型为前端类型
function convertBackendMemberType(backendType) {
  const typeMap = {
    'normal': MEMBER_TYPES.NORMAL,
    'day_card': MEMBER_TYPES.DAY,
    'permanent': MEMBER_TYPES.PERMANENT
  };

  return typeMap[backendType] || MEMBER_TYPES.NORMAL;
}

// 辅助函数：格式化日期字符串
function formatDateString(dateString) {
  if (!dateString) return null;

  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return null;
  }
}

// 辅助函数：格式化日期时间字符串
function formatDateTimeString(dateTimeString) {
  if (!dateTimeString) return '';

  try {
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('日期时间格式化失败:', error);
    return '';
  }
}

// 强制刷新会员信息（清除缓存后重新获取）
export async function refreshMemberInfo() {
  try {
    // 清除本地缓存
    uni.removeStorageSync(MEMBER_INFO_KEY);

    // 重新获取会员信息
    return await getMemberInfo();
  } catch (error) {
    console.error('刷新会员信息失败:', error);
    throw error;
  }
}

// 强制刷新订单信息（清除缓存后重新获取）
export async function refreshMemberOrders() {
  try {
    // 清除本地缓存
    uni.removeStorageSync(MEMBER_ORDERS_KEY);

    // 重新获取订单信息
    return await getMemberOrders();
  } catch (error) {
    console.error('刷新订单信息失败:', error);
    throw error;
  }
}
