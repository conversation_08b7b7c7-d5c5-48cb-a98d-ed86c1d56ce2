{"name": "2_imageCompression", "version": "1.0.0", "description": "图片压缩应用", "main": "main.js", "scripts": {"dev": "cross-env NODE_ENV=development", "build": "cross-env NODE_ENV=production", "compress": "node build.js", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {}, "devDependencies": {"cross-env": "^7.0.3", "terser-webpack-plugin": "^5.3.10"}}