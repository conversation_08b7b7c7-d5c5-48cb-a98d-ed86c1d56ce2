# 微信内容安全检测实现指南（续）

## 轮询机制实现

为了解决微信内容安全检测API异步返回结果的问题，我们实现了一个轮询机制，定期查询检测结果：

```javascript
/**
 * 开始轮询检测结果
 * @param {string} traceId 检测请求ID
 */
function startPollingCheckResult(traceId) {
  // 轮询参数
  let attempts = 0;
  const interval = 1000; // 每1000毫秒查询一次，相当于每秒1次

  // 轮询函数
  const poll = async () => {
    attempts++;

    try {
      console.log(`[安全检测轮询] 第${attempts}次查询检测结果: ${traceId}`);

      // 发起查询请求
      const res = await uni.request({
        url: 'https://media-check.gbw8848.cn/api/check-result',
        method: 'POST',
        data: { trace_id: traceId }
      });

      // 处理响应
      if (res.statusCode === 200 && res.data.code === 0) {
        const { status, label } = res.data.data;

        console.log(`[安全检测轮询] 收到检测结果: trace_id=${traceId}, status=${status}, label=${label}`);

        // 如果检测完成，更新本地状态并停止轮询
        if (status !== 'checking') {
          updateCheckResult(traceId, status, label);
          console.log(`[安全检测轮询] 检测完成，停止轮询: ${traceId}，共查询${attempts}次`);
          return;
        }
      }

      // 继续轮询，固定间隔1秒
      setTimeout(poll, interval);
    } catch (error) {
      console.error('[安全检测轮询] 查询失败:', error);

      // 即使失败也继续轮询，短暂延迟后重试
      setTimeout(poll, interval);
    }
  };

  // 开始第一次轮询，延迟1秒，给服务器一些处理时间
  console.log(`[安全检测轮询] 将在1秒后开始轮询: ${traceId}`);
  setTimeout(poll, 1000);
}
```

## 服务器端查询API实现

为了支持轮询机制，我们在服务器端添加了查询API：

```javascript
/**
 * 查询内容安全检测结果
 * POST /api/check-result
 */
router.post('/check-result', (req, res) => {
  try {
    const { trace_id } = req.body;

    if (!trace_id) {
      return res.status(400).json({
        code: -1,
        msg: '缺少trace_id参数'
      });
    }

    // 从Map中获取检测结果
    const result = checkResults.get(trace_id);

    if (result) {
      res.json({
        code: 0,
        data: {
          status: result.suggest === 'risky' ? 'risky' : 'pass',
          label: result.label,
          timestamp: result.timestamp
        },
        msg: 'success'
      });
    } else {
      // 如果没有找到结果，返回checking状态
      res.json({
        code: 0,
        data: {
          status: 'checking',
          label: null,
          timestamp: null
        },
        msg: 'checking'
      });
    }
  } catch (error) {
    console.error('查询检测结果出错:', error);
    res.status(500).json({
      code: -1,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});
```

## 批量查询API实现

为了优化网络请求，我们还实现了批量查询API：

```javascript
/**
 * 批量查询内容安全检测结果
 * POST /api/batch-check-result
 */
router.post('/batch-check-result', (req, res) => {
  try {
    const { trace_ids } = req.body;

    if (!trace_ids || !Array.isArray(trace_ids) || trace_ids.length === 0) {
      return res.status(400).json({
        code: -1,
        msg: '缺少trace_ids参数或格式不正确'
      });
    }

    // 批量查询结果
    const results = {};

    trace_ids.forEach(trace_id => {
      const result = checkResults.get(trace_id);

      if (result) {
        results[trace_id] = {
          status: result.suggest === 'risky' ? 'risky' : 'pass',
          label: result.label,
          timestamp: result.timestamp
        };
      } else {
        results[trace_id] = {
          status: 'checking',
          label: null,
          timestamp: null
        };
      }
    });

    res.json({
      code: 0,
      data: results,
      msg: 'success'
    });
  } catch (error) {
    console.error('批量查询检测结果出错:', error);
    res.status(500).json({
      code: -1,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});
```

## 其他优化建议

1. **调整清理频率**：
   - 可以将检测结果的保留时间从1小时调整为24小时，与上传文件的清理周期保持一致
   - 将清理频率从每小时一次调整为每6小时一次，减少不必要的CPU消耗
   ```javascript
   export function cleanupResults(maxAge = 86400000) { // 24小时 = 86400000毫秒
     // 清理逻辑...
   }

   // 每6小时清理一次过期结果
   setInterval(cleanupResults, 21600000); // 6小时 = 21600000毫秒
   ```

2. **添加记录数量限制**：
   - 设置一个最大记录数量限制，避免内存占用过大
   ```javascript
   // 如果记录数量超过1000条，清理最旧的记录
   if (securityResults.size > 1000) {
     const entries = Array.from(securityResults.entries());
     // 按时间戳排序
     entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
     // 清理最旧的记录，直到剩下800条
     const toDelete = entries.slice(0, entries.length - 800);
     for (const [filePath] of toDelete) {
       securityResults.delete(filePath);
     }
     console.log(`[安全检测存储] 记录数量超过限制，清理了 ${toDelete.length} 条最旧记录，当前剩余 ${securityResults.size} 条`);
   }
   ```

3. **使用持久化存储**：
   - 对于生产环境，可以考虑使用数据库或文件系统来持久化存储检测结果
   - 这样即使服务器重启，也不会丢失检测结果

## 微信内容安全检测API说明

微信提供了两种内容安全检测API：

1. **文本内容安全检测**：`security.msgSecCheck`
   - 用于检测文本内容是否包含违规内容
   - 同步API，调用后立即返回检测结果

2. **媒体内容安全检测**：`security.mediaCheckAsync`
   - 用于检测图片、音频、视频等媒体内容是否包含违规内容
   - 异步API，检测结果通过消息推送的方式返回

### 媒体内容安全检测API参数说明

调用 `security.mediaCheckAsync` API 需要提供以下参数：

- `media_url`：要检测的媒体文件URL
- `media_type`：媒体类型（1:音频;2:图片;3:视频）
- `version`：接口版本号（2.0版本为固定值2）
- `scene`：场景枚举值（1:资料;2:评论;3:论坛;4:社交日志）
- `openid`：用户的openid（用户需在近两小时访问过小程序）

### 检测结果说明

检测结果通过消息推送的方式返回，包含以下字段：

- `trace_id`：检测请求ID，用于关联请求和结果
- `result.suggest`：检测结果建议，取值为：
  - `pass`：通过
  - `risky`：可能违规
  - `review`：需要人工审核
- `result.label`：违规标签，取值为：
  - `100`：正常
  - `10001`：广告
  - `20001`：时政
  - `20002`：色情
  - `20003`：辱骂
  - `20006`：违法犯罪
  - `20008`：欺诈
  - `20012`：低俗
  - `20013`：版权
  - `21000`：其他

### 检测结果处理

在收到检测结果后，应根据 `result.suggest` 的值进行相应处理：

- `pass`：允许内容通过
- `risky`：拦截内容，显示提示信息
- `review`：可以根据业务需求决定是否拦截，或者进入人工审核流程

## 常见问题与解决方案

1. **检测结果未返回**：
   - 确保微信公众平台已正确配置消息推送URL
   - 确保服务器能够接收微信的消息推送
   - 检查服务器日志，确认是否有接收到消息推送的记录

2. **检测API返回61010错误**：
   - 这表明用户未在近两小时内访问过小程序
   - 解决方案：确保用户已登录并使用小程序

3. **检测结果未正确关联到图片**：
   - 检查 `recordCheckRequest` 和 `updateCheckResult` 函数的实现
   - 确保 `trace_id` 正确关联到图片路径

4. **内存占用过大**：
   - 调整检测结果的保留时间
   - 设置最大记录数量限制
   - 考虑使用持久化存储

5. **服务器重启后检测结果丢失**：
   - 考虑使用数据库或文件系统来持久化存储检测结果
   - 或者在服务器重启后，要求用户重新上传图片

## 注意事项

- 需要在每个微信小程序的管理后台配置回调URL
- 系统会自动管理每个小程序的access_token，无需手动刷新
- 请确保服务器有公网IP或域名，以便微信服务器能够访问回调接口
- 每个小程序的内容安全检测配额是独立计算的
- 内容安全检测应与人工审核相结合，不应完全依赖自动检测
- 建议定期检查内容过滤效果，确保没有违规内容漏检
- 需要屏蔽过滤色情、敏感、涉政等各类违规文本及图片
- 上传的文件将在24小时后自动删除，清理任务每6小时运行一次
- 用户必须在近两小时内访问过小程序，否则内容安全检测API会返回61010错误
- 单个AppID调用上限为2000次/分钟，200,000次/天
- 必须在微信小程序后台的"开发"->"开发设置"->"服务器域名"中配置正确的域名，包括request合法域名、uploadFile合法域名和downloadFile合法域名
- 域名必须是备案过的域名，且必须使用HTTPS协议

## 服务器部署指南

部署内容安全检测服务的步骤如下：

### 1. 上传代码到服务器

通过宝塔面板上传新的代码文件：
- 登录宝塔面板
- 进入文件管理器
- 导航到 `/www/wwwroot/media-check` 目录
- 上传或替换相关文件

### 2. 安装依赖并重启服务

通过宝塔面板的终端执行：
```bash
cd /www/wwwroot/media-check
npm install express-xml-bodyparser --save
pm2 restart media-check
```

### 3. 查看服务日志

如果需要查看服务运行情况，可以通过以下命令查看日志：
```bash
pm2 logs media-check
```

### 4. 验证服务是否正常运行

可以通过以下方式验证服务是否正常运行：

1. 检查服务状态：
   ```bash
   pm2 status
   ```

2. 测试API是否可访问：
   ```bash
   curl https://media-check.yourdomain.com/api/health
   ```

3. 在小程序中上传图片，检查服务器日志是否记录了检测请求和结果
