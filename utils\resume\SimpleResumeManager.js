/**
 * 简化的简历管理器
 * 只维护当前简历索引和简历数据映射
 */

const { ResumeData } = require('./ResumeData.js');

// 获取错误上报器（延迟加载避免循环依赖）
function getErrorReporter() {
  try {
    return require('../error/errorReporter');
  } catch (e) {
    console.warn('错误上报器未找到:', e);
    return null;
  }
}

/**
 * 简历管理器类
 */
class SimpleResumeManager {
  constructor() {
    this.currentResumeIndex = null;
    this.resumeDataMap = new Map(); // {resumeIndex: ResumeData实例}
    this.isInitialized = false;
  }

  /**
   * 初始化管理器
   */
  initialize() {
    if (this.isInitialized) return;

    console.log('初始化简历管理器...');

    try {
      // 从本地存储加载数据
      this.loadFromStorage();

      // 确保至少有一个简历
      this.ensureAtLeastOneResume();

      this.isInitialized = true;
      console.log('简历管理器初始化完成');
      console.log('当前简历索引:', this.currentResumeIndex);
      console.log('简历数量:', this.resumeDataMap.size);
    } catch (error) {
      console.error('简历管理器初始化失败:', error);
      // 创建默认简历
      this.createDefaultResume();
      this.isInitialized = true;
    }
  }

  /**
   * 从本地存储加载数据
   */
  loadFromStorage() {
    try {
      // 加载当前简历索引
      const savedCurrentIndex = wx.getStorageSync('currentResumeIndex');

      // 加载简历数据映射
      const savedResumeMap = wx.getStorageSync('resumeDataMap');

      if (savedCurrentIndex && savedResumeMap) {
        this.currentResumeIndex = savedCurrentIndex;

        // 将保存的对象转换为ResumeData实例
        for (const [index, resumeObj] of Object.entries(savedResumeMap)) {
          this.resumeDataMap.set(index, ResumeData.fromObject(resumeObj));
        }

        console.log('从本地存储加载简历数据成功');
      }
    } catch (error) {
      console.error('从本地存储加载数据失败:', error);

      // 上报数据加载失败错误
      const errorReporter = getErrorReporter();
      if (errorReporter) {
        errorReporter.reportManualError('resume_data_load_error', error, {
          module: 'SimpleResumeManager',
          action: 'loadFromStorage',
          has_current_index: !!wx.getStorageSync('currentResumeIndex'),
          has_resume_map: !!wx.getStorageSync('resumeDataMap')
        });
      }
    }
  }

  /**
   * 保存到本地存储
   */
  saveToStorage() {
    try {
      // 保存当前简历索引
      wx.setStorageSync('currentResumeIndex', this.currentResumeIndex);

      // 将ResumeData实例转换为普通对象保存
      const resumeMapObj = {};
      for (const [index, resumeData] of this.resumeDataMap.entries()) {
        resumeMapObj[index] = resumeData.toObject();
      }
      wx.setStorageSync('resumeDataMap', resumeMapObj);

      console.log('简历数据保存到本地存储成功');
    } catch (error) {
      console.error('保存到本地存储失败:', error);

      // 上报数据保存失败错误
      const errorReporter = getErrorReporter();
      if (errorReporter) {
        errorReporter.reportManualError('resume_data_save_error', error, {
          module: 'SimpleResumeManager',
          action: 'saveToStorage',
          current_index: this.currentResumeIndex,
          resume_count: this.resumeDataMap.size
        });
      }
    }
  }

  /**
   * 确保至少有一个简历
   */
  ensureAtLeastOneResume() {
    if (this.resumeDataMap.size === 0 || !this.currentResumeIndex) {
      this.createDefaultResume();
    }
  }

  /**
   * 创建默认简历
   */
  createDefaultResume() {
    const defaultIndex = this.generateResumeIndex();
    const defaultResume = new ResumeData({
      id: defaultIndex,
      title: '我的简历',
      createTime: Date.now(),
      updateTime: Date.now()
    });

    this.resumeDataMap.set(defaultIndex, defaultResume);
    this.currentResumeIndex = defaultIndex;

    // 保存到本地存储
    this.saveToStorage();

    console.log('创建默认简历:', defaultIndex);
  }

  /**
   * 生成简历索引
   */
  generateResumeIndex() {
    return 'resume_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
  }

  /**
   * 获取当前简历
   */
  getCurrentResume() {
    if (!this.currentResumeIndex || !this.resumeDataMap.has(this.currentResumeIndex)) {
      console.warn('当前简历不存在，创建默认简历');
      this.createDefaultResume();
    }

    return this.resumeDataMap.get(this.currentResumeIndex);
  }

  /**
   * 更新当前简历数据
   * @param {Object} updates - 要更新的数据
   */
  updateCurrentResume(updates) {
    const currentResume = this.getCurrentResume();

    // 更新数据
    Object.assign(currentResume, updates);

    // 更新时间戳
    currentResume.touch();

    // 自动保存
    this.saveToStorage();

    console.log('简历数据已更新并保存');
  }

  /**
   * 更新简历的特定字段
   * @param {string} fieldPath - 字段路径，如 'basicInfo.name'
   * @param {any} value - 新值
   */
  updateField(fieldPath, value) {
    const currentResume = this.getCurrentResume();

    // 使用点号分隔的路径更新字段
    const paths = fieldPath.split('.');
    let target = currentResume;

    for (let i = 0; i < paths.length - 1; i++) {
      if (!target[paths[i]]) {
        target[paths[i]] = {};
      }
      target = target[paths[i]];
    }

    const fieldName = paths[paths.length - 1];

    // 对特定字段进行类型转换
    const convertedValue = this.convertFieldValue(fieldName, value);
    target[fieldName] = convertedValue;

    // 更新时间戳
    currentResume.touch();

    // 自动保存
    this.saveToStorage();

    console.log(`字段 ${fieldPath} 已更新为:`, value);
  }

  /**
   * 转换字段值为正确的类型
   * @param {string} fieldName - 字段名
   * @param {any} value - 原始值
   * @returns {any} 转换后的值
   */
  convertFieldValue(fieldName, value) {
    const { BasicInfo, JobIntention, EducationItem, WorkItem, ProjectItem, CustomItem, SchoolExperienceItem, InternshipItem, SkillsManager, AwardsManager, InterestsManager } = require('./ResumeData.js');

    // 如果值已经是正确的类实例，直接返回
    if (value && typeof value.toObject === 'function') {
      return value;
    }

    // 如果值已经是管理器实例，直接返回
    if (value && typeof value.toArray === 'function') {
      return value;
    }

    // 根据字段名转换为相应的类实例
    switch (fieldName) {
      case 'basicInfo':
        return new BasicInfo(value);
      case 'jobIntention':
        return new JobIntention(value);
      case 'education':
        return Array.isArray(value) ? value.map(item => new EducationItem(item)) : [];
      case 'school':
        return Array.isArray(value) ? value.map(item => new SchoolExperienceItem(item)) : [];
      case 'internship':
        return Array.isArray(value) ? value.map(item => new InternshipItem(item)) : [];
      case 'work':
        return Array.isArray(value) ? value.map(item => new WorkItem(item)) : [];
      case 'project':
        return Array.isArray(value) ? value.map(item => new ProjectItem(item)) : [];
      case 'custom1':
      case 'custom2':
      case 'custom3':
        return Array.isArray(value) ? value.map(item => new CustomItem(item)) : [];
      case 'skills':
        return new SkillsManager(value);
      case 'awards':
        return new AwardsManager(value);
      case 'interests':
        return new InterestsManager(value);
      default:
        // 对于其他字段（如 evaluation），直接返回原值
        return value;
    }
  }

  /**
   * 创建新简历
   * @param {string} title - 简历标题
   */
  createNewResume(title = '新建简历') {
    const newIndex = this.generateResumeIndex();
    const newResume = new ResumeData({
      id: newIndex,
      title: title,
      createTime: Date.now(),
      updateTime: Date.now()
    });

    this.resumeDataMap.set(newIndex, newResume);
    this.currentResumeIndex = newIndex;

    // 保存到本地存储
    this.saveToStorage();

    console.log('创建新简历:', newIndex);
    return newResume;
  }

  /**
   * 切换到指定简历
   * @param {string} resumeIndex - 简历索引
   */
  switchToResume(resumeIndex) {
    if (this.resumeDataMap.has(resumeIndex)) {
      this.currentResumeIndex = resumeIndex;
      this.saveToStorage();
      console.log('切换到简历:', resumeIndex);
      return true;
    } else {
      console.error('简历不存在:', resumeIndex);
      return false;
    }
  }

  /**
   * 删除简历
   * @param {string} resumeIndex - 简历索引
   */
  deleteResume(resumeIndex) {
    if (this.resumeDataMap.size <= 1) {
      console.warn('不能删除最后一个简历');
      return false;
    }

    if (this.resumeDataMap.has(resumeIndex)) {
      this.resumeDataMap.delete(resumeIndex);

      // 如果删除的是当前简历，切换到第一个简历
      if (this.currentResumeIndex === resumeIndex) {
        this.currentResumeIndex = this.resumeDataMap.keys().next().value;
      }

      this.saveToStorage();
      console.log('删除简历:', resumeIndex);
      return true;
    } else {
      console.error('简历不存在:', resumeIndex);
      return false;
    }
  }

  /**
   * 获取所有简历列表
   */
  getAllResumes() {
    const resumes = [];
    for (const [index, resumeData] of this.resumeDataMap.entries()) {
      resumes.push({
        index: index,
        title: resumeData.title,
        createTime: resumeData.createTime,
        updateTime: resumeData.updateTime,
        isCurrent: index === this.currentResumeIndex
      });
    }

    // 按更新时间排序
    resumes.sort((a, b) => b.updateTime - a.updateTime);

    return resumes;
  }

  /**
   * 获取当前简历的JSON表示
   */
  getCurrentResumeJSON() {
    const currentResume = this.getCurrentResume();
    return currentResume ? currentResume.toJSON() : '{}';
  }

  /**
   * 验证当前简历
   */
  validateCurrentResume() {
    const currentResume = this.getCurrentResume();
    return currentResume ? currentResume.validate() : ['没有简历数据'];
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalResumes: this.resumeDataMap.size,
      currentResumeIndex: this.currentResumeIndex,
      isInitialized: this.isInitialized
    };
  }
}

// 创建全局单例
const simpleResumeManager = new SimpleResumeManager();

module.exports = simpleResumeManager;
