/**
 * 错误上报API模块
 * 提供错误上报相关的API接口
 */

const request = require('./request');
const apiConfig = require('../../config/apiConfig');

/**
 * 上报错误信息到服务器
 * @param {Object} errorData 错误数据
 * @returns {Promise<Object>} API响应结果
 */
function reportError(errorData) {
  console.log('📤 上报错误信息:', errorData.error_type);
  
  return request.request({
    url: apiConfig.errorReportUrl,
    method: 'POST',
    data: errorData,
    showLoading: false,
    showError: false,
    needAuth: false, // 错误上报不需要认证，避免认证失败导致无法上报
    timeout: 5000
  });
}

/**
 * 批量上报错误信息
 * @param {Array} errorList 错误列表
 * @returns {Promise<Object>} API响应结果
 */
function reportErrorBatch(errorList) {
  console.log('📤 批量上报错误信息:', errorList.length, '条');
  
  return request.request({
    url: apiConfig.errorReportUrl + '/batch',
    method: 'POST',
    data: {
      errors: errorList,
      batch_size: errorList.length,
      timestamp: new Date().toISOString()
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: 10000
  });
}

/**
 * 获取错误统计信息（可选，用于调试）
 * @returns {Promise<Object>} API响应结果
 */
function getErrorStats() {
  return request.request({
    url: apiConfig.errorReportUrl + '/stats',
    method: 'GET',
    showLoading: false,
    showError: false,
    needAuth: true,
    timeout: 5000
  });
}

module.exports = {
  reportError,
  reportErrorBatch,
  getErrorStats
};
