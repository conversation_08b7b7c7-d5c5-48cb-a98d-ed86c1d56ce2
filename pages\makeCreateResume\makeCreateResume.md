makeCreateResume/
├── components/                # 组件目录
│   ├── resumePreview/        # 预览区组件
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   ├── resumeHtmlGenerator/  # HTML生成器组件
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   ├── resumeTemplates/      # 简历模板组件
│   │   ├── templateA01/
│   │   │   ├── index.js
│   │   │   ├── index.json
│   │   │   ├── index.wxml
│   │   │   └── index.wxss
│   │   ├── templateA02/
│   │   │   ├── index.js
│   │   │   ├── index.json
│   │   │   ├── index.wxml
│   │   │   └── index.wxss
│   │   └── templateA03/
│   │       ├── index.js
│   │       ├── index.json
│   │       ├── index.wxml
│   │       └── index.wxss
│   ├── templateSelector/     # 模板选择器组件
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   └── toolBar/             # 工具栏组件
│       ├── index.js
│       ├── index.json
│       ├── index.wxml
│       └── index.wxss
├── makeCreateResume.js       # 页面主文件
├── makeCreateResume.json     # 页面配置文件
├── makeCreateResume.wxml     # 页面结构文件
└── makeCreateResume.wxss     # 页面样式文件

[makeCreateResume页面]
    ↓
用户填写各模块数据
    ↓
保存到本地存储(wx.setStorageSync)
    ↓
点击生成简历
    ↓
[makeCreateResume页面]
    ↓
读取本地存储数据
    ↓
templateSelector (选择模板布局)
    ↓
toolBar (配置样式细节)
    ↓
resumePreview (最终渲染)


[makeCreateResume页面]
用户填写表单 -> 本地存储(Storage)
    ↓
[makeCreateResume页面]
读取本地存储 -> resumePreview (展示数据)
    ↓
templateSelector (选择模板) -> 更新布局
toolBar (调整样式) -> 更新样式