<template>
	<view class="profile-container">
		<!-- 自定义导航栏 -->
		<view class="custom-nav glassmorphism">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-content">
				<view class="back-button-container">
					<button class="back-btn" @tap="goBack">
						<text class="back-icon">‹</text>
					</button>
				</view>
				<text class="nav-title">我的</text>
				<view class="nav-right-buttons">
					<!-- 移除自定义按钮，使用微信官方按钮 -->
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content" :style="{ paddingTop: navHeight + 'px' }">
			<!-- 用户信息和会员卡片 -->
			<view class="user-info-card neumorphism">
				<!-- 用户基本信息 -->
				<view class="user-header">
					<view class="user-avatar">
						<image src="/static/user-avatar.svg" mode="aspectFit" class="avatar-image"></image>
					</view>
					<view class="user-details">
						<view class="user-name">微信用户</view>
						<view class="member-badge" :class="'member-' + memberInfo.type">{{ getMemberTypeText() }}</view>
					</view>
					<view class="member-icon">
						<image src="/static/crown.svg" mode="aspectFit" class="crown-image"></image>
					</view>
				</view>

				<!-- 会员特权信息 -->
				<view class="member-section">
					<view class="member-header">
						<text class="member-title">{{ getMemberTypeText() }}</text>
						<text class="member-subtitle">尊享会员特权</text>
					</view>
					<!-- 分隔线 -->
					<view class="divider-line"></view>
					<view class="privileges-grid">
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">无限次图片压缩</text>
						</view>
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">批量压缩功能</text>
						</view>
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">高质量压缩</text>
						</view>
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">无广告体验</text>
						</view>
					</view>
					<!-- 分隔线 -->
					<view class="divider-line"></view>
					<view class="member-validity">
						<text>会员有效期: {{ getMemberExpireText() }}</text>
					</view>
					<button
						class="renew-btn"
						:class="{ 'heartbeat-animation': memberInfo.type === 'normal' }"
						@tap="handleRenewMember"
					>
						{{ getMemberButtonText() }}
					</button>
				</view>
			</view>

			<!-- 功能菜单 -->
			<view class="menu-section">
				<view class="menu-item neumorphism" @tap="handleMenuClick('order')">
					<view class="menu-icon">
						<image src="/static/order.svg" mode="aspectFit" class="icon-image"></image>
					</view>
					<text class="menu-text">我的订单</text>
				</view>

				<view class="menu-item neumorphism" @tap="handleMenuClick('feedback')">
					<view class="menu-icon">
						<image src="/static/feedback.svg" mode="aspectFit" class="icon-image"></image>
					</view>
					<text class="menu-text">意见反馈</text>
				</view>

				<view class="menu-item neumorphism" @tap="handleMenuClick('about')">
					<view class="menu-icon">
						<image src="/static/about.svg" mode="aspectFit" class="icon-image"></image>
					</view>
					<text class="menu-text">关于我们</text>
				</view>
			</view>

			<!-- 会员套餐选择弹窗 -->
			<member-package-modal
				:visible="showMemberModal"
				@close="showMemberModal = false"
				@purchase-success="handlePurchaseSuccess"
			></member-package-modal>

		</view>
	</view>
</template>

<script>
import { getMemberInfo, refreshMemberInfo, getMemberRemainingTime } from '@/utils/memberManager.js'
import { loginToImageCompressionService } from '@/utils/auth.js'
import { clearProductCache } from '@/utils/productManager.js'
import MemberPackageModal from '@/components/memberPackageModal.vue'

export default {
	components: {
		'member-package-modal': MemberPackageModal
	},
	data() {
		return {
			statusBarHeight: 0,
			navHeight: 0,
			// 会员信息
			memberInfo: {
				type: 'normal', // normal: 普通用户, day: 日卡会员, permanent: 永久会员
				expireDate: null, // 到期时间，永久会员为null
				isExpired: false // 是否已过期
			},
			// 控制会员套餐选择弹窗显示
			showMemberModal: false
		}
	},
	async onReady() {
		// 获取状态栏高度
		const windowInfo = uni.getWindowInfo()
		this.statusBarHeight = windowInfo.statusBarHeight
		// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
		this.navHeight = this.statusBarHeight + 44

		// 确保用户已登录
		await this.ensureUserLoggedIn()

		// 加载会员信息
		await this.loadMemberInfo()
	},
	methods: {
		// 确保用户已登录
		async ensureUserLoggedIn() {
			try {
				await loginToImageCompressionService()
			} catch (error) {
				console.error('用户登录失败:', error)
				uni.showToast({
					title: '登录失败，请重试',
					icon: 'none'
				})
			}
		},

		// 加载会员信息
		async loadMemberInfo() {
			try {
				console.log('🔄 刷新会员信息...')
				this.memberInfo = await refreshMemberInfo()
				console.log('✅ 会员信息加载完成:', this.memberInfo)
			} catch (error) {
				console.error('❌ 加载会员信息失败:', error)
				// 如果加载失败，使用本地缓存
				this.memberInfo = await getMemberInfo()
			}
		},

		// 获取会员类型文本
		getMemberTypeText() {
			switch (this.memberInfo.type) {
				case 'day':
					return '日卡会员'
				case 'permanent':
					return '永久会员'
				default:
					return '普通用户'
			}
		},

		// 获取会员剩余时间文本
		getMemberRemainingTimeText() {
			if (this.memberInfo.type === 'permanent') {
				return '永久有效'
			}

			if (this.memberInfo.type === 'day' && this.memberInfo.expireTime) {
				const remainingTime = getMemberRemainingTime(this.memberInfo.expireTime)
				if (remainingTime.isExpired) {
					return '已过期'
				}

				if (remainingTime.hours > 0) {
					return `剩余 ${remainingTime.hours} 小时 ${remainingTime.minutes} 分钟`
				} else {
					return `剩余 ${remainingTime.minutes} 分钟`
				}
			}

			return '未开通'
		},

		// 获取会员到期时间显示文本
		getMemberExpireText() {
			if (this.memberInfo.type === 'permanent') {
				return '永久有效'
			} else if (this.memberInfo.type === 'day') {
				if (this.memberInfo.expireTime) {
					// 显示精确的过期时间
					const expireTime = new Date(this.memberInfo.expireTime)
					const year = expireTime.getFullYear()
					const month = String(expireTime.getMonth() + 1).padStart(2, '0')
					const day = String(expireTime.getDate()).padStart(2, '0')
					const hour = String(expireTime.getHours()).padStart(2, '0')
					const minute = String(expireTime.getMinutes()).padStart(2, '0')
					return `${year}-${month}-${day} ${hour}:${minute}`
				} else if (this.memberInfo.expireDate) {
					// 兼容旧数据
					return this.memberInfo.expireDate
				}
			}
			return '未开通会员'
		},

		// 获取会员按钮文本
		getMemberButtonText() {
			// 根据用户会员状态返回不同的按钮文本
			if (this.memberInfo.type === 'normal') {
				return '开通会员'
			} else {
				return '续费会员'
			}
		},

		// 处理续费会员按钮点击
		handleRenewMember() {
			this.showMemberModal = true
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 跳转到订单页面
		goToOrders() {
			uni.navigateTo({
				url: '/pages/profile/orders'
			})
		},

		// 跳转到订单页面
		goToOrders() {
			uni.navigateTo({
				url: '/pages/profile/orders'
			})
		},

		// 显示意见反馈
		showFeedback() {
			// 联系我们 - 询问是否复制客服微信号
			uni.showModal({
				title: '客服微信',
				content: '客服微信号：gbw6646\n是否复制到剪贴板？',
				confirmText: '复制',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 用户点击复制
						uni.setClipboardData({
							data: 'gbw6646',
							success: () => {
								uni.showToast({
									title: '已复制到剪贴板',
									icon: 'success',
									duration: 2000
								})
							}
						})
					}
				}
			})
		},

		// 处理购买成功
		async handlePurchaseSuccess(data) {
			console.log('购买成功:', data)

			// 重新加载会员信息
			await this.loadMemberInfo()

			// 显示成功提示
			uni.showToast({
				title: '会员开通成功',
				icon: 'success',
				duration: 2000
			})
		},

		// 处理菜单点击
		handleMenuClick(type) {
			if (type === 'order') {
				// 跳转到订单查询页面
				uni.navigateTo({
					url: '/pages/profile/orders'
				})
				return
			}

			if (type === 'feedback') {
				// 联系我们 - 询问是否复制客服微信号
				uni.showModal({
					title: '客服微信',
					content: '客服微信号：gbw6646\n是否复制到剪贴板？',
					confirmText: '复制',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 用户点击复制
							uni.setClipboardData({
								data: 'gbw6646',
								success: () => {
									uni.showToast({
										title: '已复制到剪贴板',
										icon: 'success',
										duration: 2000
									})
								}
							})
						}
					}
				})
				return
			}

			if (type === 'about') {
				// 关于我们 - 显示应用信息
				uni.showModal({
					title: '关于我们',
					content: `压缩图片助手花花版 v1.0

🌸 让图片压缩变得简单高效

📱 支持多种压缩方式
💾 智能优化图片大小
✨ 为您节省珍贵的存储空间

谢谢使用，祝您生活愉快！`,
					showCancel: false,
					confirmText: '知道了'
				})
				return
			}
		},

		// 处理购买成功
		async handlePurchaseSuccess(result) {
			console.log('🎉 购买成功:', result)
			
			// 清除产品缓存，确保下次获取最新数据
			clearProductCache()
			
			// 重新加载会员信息
			await this.loadMemberInfo()
			
			uni.showToast({
				title: '购买成功！',
				icon: 'success'
			})
		}
	}
}
</script>

<style lang="scss">
// 定义主题色变量，与主页保持一致
$primary-color: #0EA5E9; // 主题色：科技蓝
$primary-gradient: linear-gradient(145deg, #0EA5E9, #0284C7);
$bg-color: #F7F7F7; // 背景色：微信灰
$text-primary: #2C2C2C; // 主要文字颜色
$text-secondary: #666666; // 次要文字颜色
$text-tertiary: #999999;
$border-color: #E5E5E5;

// 自定义阴影变量
$shadow-dark: rgba(163, 177, 198, 0.6);
$shadow-light: rgba(255, 255, 255, 0.7);

page {
	background-color: $bg-color;
}

page {
	background-color: $bg-color;
}

// 新拟物风格的混入
@mixin neumorphism {
	background: $bg-color;
	box-shadow: 12px 12px 24px $shadow-dark,
				-8px -8px 20px $shadow-light,
				inset 2px 2px 4px rgba(255, 255, 255, 0.5),
				inset -2px -2px 4px rgba(0, 0, 0, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

// 磨砂玻璃风格的混入
@mixin glassmorphism {
	background: rgba($bg-color, 0.98);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.neumorphism {
	@include neumorphism;
}

.glassmorphism {
	@include glassmorphism;
}

.profile-container {
	padding: 30rpx;

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		padding: 0 30rpx;

		.nav-content {
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.back-button-container {
				min-width: 44px;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.back-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 44px;
					height: 44px;
					background: transparent !important;
					background-color: transparent !important;
					border: none !important;
					border-radius: 0 !important;
					box-shadow: none !important;
					padding: 0;
					margin: 0;
					outline: none;
					transition: opacity 0.2s ease;

					&::after {
						display: none !important;
					}

					&:active {
						opacity: 0.6;
						background: transparent !important;
						background-color: transparent !important;
					}

					.back-icon {
						font-size: 36px;
						color: #000000;
						font-weight: normal;
						line-height: 1;
						margin-left: -2px;
					}
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: $text-primary;
				font-weight: 500;
			}

			.nav-right-buttons {
				min-width: 44px;
			}
		}
	}

	.main-content {
		position: relative;
		width: 100%;
		padding-bottom: 40rpx;
		margin-top: -40rpx;

		.user-info-card {
			border-radius: 30rpx;
			padding: 30rpx;
			margin: 20rpx 0;
			color: #333333;

			.user-header {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;

				.user-avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 50%;
					overflow: hidden;
					margin-right: 20rpx;
					background: rgba(255, 255, 255, 0.9);
					display: flex;
					align-items: center;
					justify-content: center;

					.avatar-image {
						width: 90rpx;
						height: 90rpx;
					}
				}

				.user-details {
					flex: 1;

					.user-name {
						font-size: 32rpx;
						font-weight: 600;
						color: #333333;
						margin-bottom: 8rpx;
					}

					.member-badge {
						font-size: 24rpx;
						padding: 6rpx 16rpx;
						border-radius: 20rpx;
						display: inline-block;

						&.member-normal {
							background: rgba(108, 117, 125, 0.1);
							color: #6c757d;
						}

						&.member-day {
							background: linear-gradient(135deg, #ffd700, #ffed4e);
							color: #8b6914;
						}

						&.member-permanent {
							background: linear-gradient(135deg, #B8860B, #DAA520, #FFD700);
							color: #2C1810;
							font-weight: 600;
							box-shadow: 0 2rpx 8rpx rgba(218, 165, 32, 0.4);
							border: 1rpx solid rgba(255, 215, 0, 0.3);
							position: relative;
							overflow: hidden;

							// 添加微妙的闪光效果
							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: -100%;
								width: 100%;
								height: 100%;
								background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
								animation: goldShine 3s infinite;
							}
						}
					}
				}

				.member-icon {
					width: 90rpx;
					height: 90rpx;

					.crown-image {
						width: 100%;
						height: 100%;
					}
				}
			}
			.member-section {
				border-top: 1px solid #f0f0f0;
				padding-top: 30rpx;

				.member-header {
					text-align: center;
					margin-bottom: 20rpx;

					.member-title {
						font-size: 40rpx;
						font-weight: bold;
						display: block;
						margin-bottom: 10rpx;
					}

					.member-subtitle {
						font-size: 28rpx;
						color: #666666;
					}
				}

				.divider-line {
					width: 100%;
					height: 1px;
					border-top: 1px solid #f0f0f0;
					margin: 20rpx 0;
				}

				.privileges-grid {
					display: grid;
					grid-template-columns: 1fr 1fr;
					gap: 20rpx;
					margin-bottom: 30rpx;

					.privilege-item {
						display: flex;
						align-items: center;
						justify-content: flex-start;
						width: 100%;
						padding-left: 60rpx;

						.check-icon {
							width: 32rpx;
							height: 32rpx;
							margin-right: 12rpx;
							background: linear-gradient(135deg, #0EA5E9, #0284C7);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							flex-shrink: 0;

							.check-mark {
								color: white;
								font-size: 20rpx;
								font-weight: bold;
							}
						}

						.privilege-text {
							font-size: 26rpx;
							text-align: left;
							line-height: 1.4;
							white-space: nowrap;
							flex: 1;
						}
					}
				}

				.member-validity {
					text-align: center;
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 30rpx;
				}

				.renew-btn {
					width: 100%;
					height: 80rpx;
					background: linear-gradient(135deg, #0EA5E9, #0284C7);
					border: none;
					border-radius: 40rpx;
					color: white;
					font-size: 32rpx;
					font-weight: 500;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease;
					box-shadow: 0 4rpx 16rpx rgba(14, 165, 233, 0.3);
					position: relative;
					overflow: hidden;

					// 添加呼吸效果
					animation: breatheEffect 3s ease-in-out infinite;

					// 闪光效果 - 更频繁的闪光
					&::before {
						content: '';
						position: absolute;
						top: -2rpx;
						left: -100%;
						width: 100%;
						height: calc(100% + 4rpx);
						background: linear-gradient(
							90deg,
							transparent 0%,
							rgba(255, 255, 255, 0.2) 30%,
							rgba(255, 255, 255, 0.8) 50%,
							rgba(255, 255, 255, 0.2) 70%,
							transparent 100%
						);
						animation: shineEffect 3s ease-in-out infinite;
						border-radius: inherit;
						z-index: 1;
					}

					&:hover {
						box-shadow: 0 6rpx 20rpx rgba(14, 165, 233, 0.4);
					}

					&:active {
						background: linear-gradient(135deg, #0284C7, #0369A1);
						transform: scale(0.98);
					}

					/* 心跳脉冲动效 - 仅对普通用户显示 */
					&.heartbeat-animation {
						animation: breatheEffect 3s ease-in-out infinite;

						&::before {
							animation: shineEffect 3s ease-in-out infinite;
						}
					}
				}
			}

			.member-actions {
				.upgrade-section {
					text-align: center;
					padding: 20rpx 0;

					.upgrade-text {
						font-size: 28rpx;
						color: #666666;
						margin-bottom: 20rpx;
						display: block;
					}

					.upgrade-btn {
						background: linear-gradient(135deg, #0EA5E9, #0284C7);
						color: white;
						border: none;
						border-radius: 50rpx;
						padding: 20rpx 60rpx;
						font-size: 32rpx;
						font-weight: 600;

						.upgrade-btn-text {
							color: white;
						}
					}
				}

				.member-status {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx 0;

					.status-text {
						font-size: 32rpx;
						font-weight: 600;
						color: #333333;
					}

					.expire-text {
						font-size: 24rpx;
						color: #666666;
						flex: 1;
						text-align: center;
					}

					.renew-btn {
						background: linear-gradient(135deg, #0EA5E9, #0284C7);
						color: white;
						border: none;
						border-radius: 30rpx;
						padding: 12rpx 30rpx;
						font-size: 24rpx;
						font-weight: 600;

						.renew-btn-text {
							color: #333;
						}
					}
				}
			}
		}

		.menu-section {
			margin-top: 40rpx;

			.menu-item {
				display: flex;
				align-items: center;
				padding: 30rpx;
				margin-bottom: 20rpx;
				border-radius: 20rpx;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				.menu-icon {
					width: 60rpx;
					height: 60rpx;
					margin-right: 30rpx;

					.icon-image {
						width: 100%;
						height: 100%;
					}
				}

				.menu-text {
					flex: 1;
					font-size: 32rpx;
					color: #333333;
					font-weight: 500;
				}

				.menu-arrow {
					font-size: 40rpx;
					color: #999999;
					font-weight: 300;
				}
			}
		}
	}
}

/* 心跳动画 */
@keyframes heartbeat {
	0% {
		transform: scale(1);
		box-shadow: 0 4rpx 16rpx rgba(14, 165, 233, 0.3);
	}
	50% {
		transform: scale(1.05);
		box-shadow: 0 8rpx 24rpx rgba(14, 165, 233, 0.5);
	}
	100% {
		transform: scale(1);
		box-shadow: 0 4rpx 16rpx rgba(14, 165, 233, 0.3);
	}
}

/* 永久会员金色闪光动画 */
@keyframes goldShine {
	0% {
		left: -100%;
	}
	100% {
		left: 100%;
	}
}

/* 呼吸效果 - 模拟自然呼吸节奏 */
@keyframes breatheEffect {
	0%, 100% {
		transform: scale(1);
		box-shadow: 0 4rpx 16rpx rgba(14, 165, 233, 0.3);
	}
	50% {
		transform: scale(1.02);
		box-shadow: 0 6rpx 24rpx rgba(14, 165, 233, 0.5);
	}
}

/* 闪光效果 - 更频繁的闪光 */
@keyframes shineEffect {
	0% {
		left: -100%;
		opacity: 0;
	}
	15% {
		opacity: 1;
	}
	35% {
		left: 100%;
		opacity: 0;
	}
	100% {
		left: 100%;
		opacity: 0;
	}
}
</style>
