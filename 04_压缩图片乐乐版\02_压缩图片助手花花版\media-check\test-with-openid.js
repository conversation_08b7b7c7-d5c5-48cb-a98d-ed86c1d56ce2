/**
 * 测试带OpenID的内容安全检测API
 * 注意：此测试需要提供有效的OpenID，该OpenID必须是近两小时内访问过小程序的用户
 */
const axios = require('axios');
const { getAccessToken } = require('./utils/access_token');
const config = require('./config');

// 测试图片URL
const TEST_IMAGE_URL = 'https://developers.weixin.qq.com/miniprogram/assets/images/<EMAIL>';

// 测试OpenID - 需要替换为有效的OpenID
const TEST_OPENID = '替换为有效的OpenID';

/**
 * 测试内容安全检测API
 */
async function testSecurityCheck() {
  try {
    console.log('开始测试内容安全检测API...');
    console.log('===================================');
    
    // 获取access_token
    const accessToken = await getAccessToken(config.wechat.appId, config.wechat.appSecret);
    console.log('获取access_token成功');
    
    // 调用内容安全检测API
    console.log(`使用OpenID: ${TEST_OPENID}`);
    console.log(`测试图片URL: ${TEST_IMAGE_URL}`);
    
    const response = await axios.post(
      `https://api.weixin.qq.com/wxa/media_check_async?access_token=${accessToken}`,
      {
        media_url: TEST_IMAGE_URL,
        media_type: 2, // 2表示图片
        version: 2, // API版本，目前为2
        scene: 1, // 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
        openid: TEST_OPENID
      }
    );
    
    console.log('API响应结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.errcode === 0) {
      console.log('测试成功! trace_id:', response.data.trace_id);
      console.log('微信将异步推送检测结果到消息接收服务器');
    } else {
      console.error('测试失败! 错误码:', response.data.errcode);
      console.error('错误信息:', response.data.errmsg);
      
      // 提供错误码解释
      switch (response.data.errcode) {
        case 40001:
          console.error('解释: access_token无效或已过期');
          break;
        case 40003:
          console.error('解释: OpenID无效，请确保提供了有效的OpenID');
          break;
        case 43104:
          console.error('解释: OpenID与AppID不匹配');
          break;
        case 61010:
          console.error('解释: 用户访问记录超时（用户未在近两小时访问小程序）');
          break;
        case 44991:
          console.error('解释: 超出接口每分钟调用限制');
          break;
        case 45009:
          console.error('解释: 超出接口每日调用限制');
          break;
        default:
          console.error('解释: 未知错误');
      }
    }
    
    console.log('===================================');
  } catch (error) {
    console.error('测试出错:', error.message);
    if (error.response) {
      console.error('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
    console.log('===================================');
  }
}

// 执行测试
testSecurityCheck();
