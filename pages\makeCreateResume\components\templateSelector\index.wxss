.template-selector {
  width: 100%;
  white-space: nowrap;
  padding: 10rpx 20rpx 15rpx 20rpx;
  background-color: #fff;
  position: fixed;
  bottom: 110rpx;
  left: 0;
  z-index: 99;
  height: 200rpx;
  display: flex;
  align-items: center;
}

.template-list {
  display: inline-flex;
  padding: 5rpx;
  align-items: center;
}

.template-item {
  display: flex;
  margin-right: 30rpx;
  width: 140rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.template-item.selected {
  transform: scale(1.05);
}

.template-item .thumbnail {
  width: 140rpx;
  height: 198rpx;
  border: 2rpx solid #eee;
}

.template-item.selected .thumbnail {
  border-color: #4B8BF5;
}