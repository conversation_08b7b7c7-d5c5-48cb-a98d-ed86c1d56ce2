// 用于配置JS压缩的webpack插件
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  configureWebpack: {
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true
            },
            output: {
              comments: false
            }
          },
          extractComments: false,
          parallel: true
        })
      ]
    }
  }
}; 