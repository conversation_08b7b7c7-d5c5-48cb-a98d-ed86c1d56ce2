Component({
  properties: {
    selected: {
      type: String,
      value: ''
    }
  },

  data: {
    templates: [
      {
        id: 'templateA01',
        name: '模板一',
        thumbnail: '../images/templateA01.jpg'
      },
      {
        id: 'templateA02',
        name: '模板二',
        thumbnail: '../images/templateA02.jpg'
      },
      {
        id: 'templateA03',
        name: '模板三',
        thumbnail: '../images/templateA03.jpg'
      }
    ]
  },

  methods: {
    onTemplateSelect(e) {
      const index = e.currentTarget.dataset.index;
      console.log('e', e)
      const template = this.data.templates[index];
      
      this.triggerEvent('select', template);
      
    }
  }
}); 