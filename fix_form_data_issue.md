# 修复Form数据格式问题

## 问题描述

在调用 `generate_add_color` API时出现422错误，原因是服务端使用了 `Form` 参数，需要 `application/x-www-form-urlencoded` 格式，而不是JSON格式。

## 错误信息
```
POST http://*************:18080/idphoto/generate_add_color 422 (Unprocessable Content)
```

## 服务端API定义
```python
@router.post("/generate_add_color", response_model=AddColorResponse)
async def generate_add_color(
    image_base64: str = Form(..., description="透明背景证件照的base64编码"),
    color: IDPhotoColor = Form(..., description="背景颜色"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
```

## 修复方案

### 修改前（错误的JSON格式）
```javascript
wx.request({
  url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoGenerateAddColorUrl,
  method: 'POST',
  data: {
    image_base64: base64Data,
    color: params.color
  },
  header: {
    'Authorization': 'Bearer ' + userToken,
    'Content-Type': 'application/json'  // 错误的Content-Type
  }
})
```

### 修改后（正确的Form格式）
```javascript
wx.request({
  url: apiConfig.idPhotoBaseUrl + apiConfig.idPhotoGenerateAddColorUrl,
  method: 'POST',
  data: `image_base64=${encodeURIComponent(base64Data)}&color=${encodeURIComponent(params.color)}`,
  header: {
    'Authorization': 'Bearer ' + userToken,
    'Content-Type': 'application/x-www-form-urlencoded'  // 正确的Content-Type
  }
})
```

## 关键修改点

1. **Content-Type**: 从 `application/json` 改为 `application/x-www-form-urlencoded`
2. **数据格式**: 从JSON对象改为URL编码的字符串格式
3. **参数编码**: 使用 `encodeURIComponent` 对参数进行URL编码，确保base64数据正确传输

## 测试验证

修复后应该能够正常调用API，不再出现422错误。可以通过以下步骤验证：

1. 选择证件照尺寸并上传照片
2. 在结果页面选择非透明的背景色
3. 点击保存到相册
4. 验证是否成功调用 `generate_add_color` API
5. 验证是否成功保存带背景色的照片

## 注意事项

- base64数据可能很长，确保URL编码正确处理
- 服务端使用Form参数时，前端必须使用对应的Content-Type
- 保持Authorization头部不变，继续使用Bearer token认证
