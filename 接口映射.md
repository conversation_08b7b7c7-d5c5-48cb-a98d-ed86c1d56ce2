总结
我已经根据服务端API接口文档完成了所有API接口的错误检查和修正，确保前端API调用与服务端接口完全匹配。

主要修正内容：
1. 用户认证模块 (utils/api/userApi.js)
✅ 接口路径：/user/login → /auth/login
✅ 用户信息：/user/info → /auth/user
✅ 登录参数格式：支持user_info对象
✅ 新增API：updateUserInfo()、refreshToken()、validateToken()
2. 简历处理模块 (utils/api/resumeApi.js)
✅ 参数格式：CONFIG → theme_config
✅ 参数格式：RESUME_DATA → resume_data
✅ 参数格式：TEMPLATE_ID → template_id
✅ 认证设置：改为可选认证（符合文档）
3. 反馈模块 (utils/api/feedbackApi.js)
✅ 接口路径：/feedback/submit → /feedback
✅ 参数格式：deviceInfo → device_info
✅ 认证设置：改为需要认证（符合文档）
4. 自动登录工具 (utils/user/autoLogin.js)
✅ 响应解析：token → access_token
✅ 用户ID获取：从user_info.id获取
✅ 用户信息存储：存储完整user_info对象
5. 请求工具 (utils/api/request.js)
✅ 成功判断：基于HTTP状态码而非success字段
✅ 错误处理：优先使用detail字段（符合服务端文档）
✅ 支持arraybuffer响应类型
接口映射表：
模块	前端方法	服务端接口	方式	认证
用户	userApi.login()	/auth/login	POST	否
用户	userApi.getUserInfo()	/auth/user	GET	是
用户	userApi.updateUserInfo()	/auth/user	PUT	是
用户	userApi.refreshToken()	/auth/refresh	POST	是
简历	resumeApi.generatePDF()	/resume/export-pdf	POST	可选
简历	resumeApi.generatePreviewImage()	/resume/export-jpeg	POST	可选
反馈	feedbackApi.submitFeedback()	/feedback	POST	是
关键改进：
1. 参数格式标准化
// 登录请求
{
  "code": "微信code",
  "user_info": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1
  }
}


2. 响应格式处理
3. 认证机制
验证要点：