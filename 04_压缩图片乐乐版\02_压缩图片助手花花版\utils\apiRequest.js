/**
 * API请求工具
 * 统一处理所有API请求，包含错误处理、重试机制等
 */

import { TIMEOUT_CONFIG, RETRY_CONFIG, APP_CONFIG } from '@/config/api.js'
import { requestWithRetry } from '@/utils/networkUtils.js'

/**
 * 统一的API请求方法
 * @param {Object} options 请求选项
 * @param {string} options.url 请求URL
 * @param {string} options.method 请求方法 GET/POST/PUT/DELETE
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {number} options.timeout 超时时间
 * @param {boolean} options.needAuth 是否需要认证
 * @returns {Promise} 请求结果
 */
export async function apiRequest(options = {}) {
  try {
    // 默认配置
    const defaultOptions = {
      method: 'GET',
      timeout: TIMEOUT_CONFIG.DEFAULT,
      needAuth: true,
      header: {
        'Content-Type': 'application/json'
      }
    }

    // 合并配置
    const requestOptions = { ...defaultOptions, ...options }

    // 如果需要认证，添加认证信息
    if (requestOptions.needAuth) {
      const authInfo = await getAuthInfo()
      if (authInfo) {
        requestOptions.data = {
          ...requestOptions.data,
          ...authInfo
        }
      }
    }

    console.log('🚀 发起API请求:', {
      url: requestOptions.url,
      method: requestOptions.method,
      needAuth: requestOptions.needAuth
    })

    // 使用带重试机制的网络请求
    const response = await requestWithRetry(requestOptions, RETRY_CONFIG.MAX_RETRIES, requestOptions.timeout)

    console.log('📥 API响应:', {
      url: requestOptions.url,
      statusCode: response.statusCode,
      success: response.statusCode === 200
    })

    // 检查HTTP状态码
    if (response.statusCode !== 200) {
      throw new Error(`HTTP错误: ${response.statusCode}`)
    }

    // 检查业务状态码
    const result = response.data
    if (result && typeof result === 'object') {
      // 兼容不同的响应格式
      if (result.success === false) {
        throw new Error(result.error || result.message || '请求失败')
      }
      if (result.code !== undefined && result.code !== 0) {
        throw new Error(result.msg || result.message || '请求失败')
      }
    }

    return result
  } catch (error) {
    console.error('❌ API请求失败:', {
      url: options.url,
      error: error.message
    })
    throw error
  }
}

/**
 * GET请求
 */
export async function apiGet(url, params = {}, options = {}) {
  // 将参数拼接到URL中
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    url += (url.includes('?') ? '&' : '?') + queryString
  }

  return apiRequest({
    url,
    method: 'GET',
    needAuth: false, // 已经手动添加认证信息到URL参数中
    ...options
  })
}

/**
 * POST请求
 */
export async function apiPost(url, data = {}, options = {}) {
  return apiRequest({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 */
export async function apiPut(url, data = {}, options = {}) {
  return apiRequest({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
export async function apiDelete(url, options = {}) {
  return apiRequest({
    url,
    method: 'DELETE',
    ...options
  })
}

/**
 * 获取认证信息
 * @returns {Promise<Object>} 认证信息
 */
async function getAuthInfo() {
  try {
    // 从缓存获取OpenID（使用正确的存储key）
    const openid = uni.getStorageSync('user_openid')

    if (openid) {
      return {
        openid: openid,
        appId: APP_CONFIG.APP_ID
      }
    }

    // 如果没有OpenID，返回基本信息
    return {
      appId: APP_CONFIG.APP_ID
    }
  } catch (error) {
    console.warn('⚠️ 获取认证信息失败:', error.message)
    return {
      appId: APP_CONFIG.APP_ID
    }
  }
}

/**
 * 统一的错误处理
 */
export function handleApiError(error, options = {}) {
  const { showToast = true, toastDuration = 2000 } = options
  
  console.error('🚨 API错误处理:', error)
  
  let errorMessage = '网络请求失败'
  
  if (error.message) {
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      errorMessage = '请求超时，请检查网络连接'
    } else if (error.message.includes('network') || error.message.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络设置'
    } else if (error.message.includes('HTTP错误')) {
      errorMessage = '服务器响应异常，请稍后重试'
    } else {
      errorMessage = error.message
    }
  }
  
  if (showToast) {
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: toastDuration
    })
  }
  
  return {
    success: false,
    error: errorMessage,
    originalError: error
  }
}

/**
 * 包装API调用，自动处理错误
 */
export async function safeApiCall(apiFunction, options = {}) {
  try {
    const result = await apiFunction()
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return handleApiError(error, options)
  }
}

/**
 * 批量API请求
 */
export async function batchApiRequest(requests = []) {
  try {
    console.log('📦 批量API请求:', requests.length, '个请求')
    
    const results = await Promise.allSettled(
      requests.map(request => apiRequest(request))
    )
    
    const successResults = []
    const failedResults = []
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successResults.push({
          index,
          data: result.value
        })
      } else {
        failedResults.push({
          index,
          error: result.reason.message
        })
      }
    })
    
    console.log('✅ 批量请求完成:', {
      total: requests.length,
      success: successResults.length,
      failed: failedResults.length
    })
    
    return {
      success: failedResults.length === 0,
      successResults,
      failedResults,
      total: requests.length
    }
  } catch (error) {
    console.error('❌ 批量请求失败:', error)
    throw error
  }
}
