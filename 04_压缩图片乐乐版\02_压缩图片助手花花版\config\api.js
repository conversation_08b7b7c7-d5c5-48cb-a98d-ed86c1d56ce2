/**
 * API配置文件
 * 统一管理所有API接口地址和配置
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
}

// 当前环境（可根据需要切换）
const CURRENT_ENV = ENV.PRODUCTION

// 服务器配置
const SERVER_CONFIG = {
  [ENV.DEVELOPMENT]: {
    // 开发环境 - 本地服务器
    IMAGE_COMPRESSION: 'http://localhost:8851',
    MEDIA_CHECK: 'http://localhost:3000'
  },
  [ENV.PRODUCTION]: {
    // 生产环境 - 线上服务器
    IMAGE_COMPRESSION: 'https://image-compression.gbw8848.cn',
    MEDIA_CHECK: 'https://media-check.gbw8848.cn'
  }
}

// 获取当前环境的服务器配置
const getCurrentConfig = () => SERVER_CONFIG[CURRENT_ENV]

// 小程序配置
export const APP_CONFIG = {
  // 当前小程序的AppID - 压缩图片助手花花版
  APP_ID: 'wx76b4aebe290bf455',
  // 小程序名称
  APP_NAME: '压缩图片助手花花版'
}

// API接口配置
export const API_CONFIG = {
  // 图像压缩服务接口
  IMAGE_COMPRESSION: {
    BASE_URL: getCurrentConfig().IMAGE_COMPRESSION,
    // 用户相关接口
    USER: {
      LOGIN: '/api/user/login',
      INFO: '/api/user/info',
      UPDATE: '/api/user/update'
    },
    // 会员相关接口
    MEMBERSHIP: {
      STATUS: '/api/membership/status',
      PRODUCTS: '/api/membership/products',
      CHECK_PERMISSION: '/api/membership/check-permission'
    },
    // 订单相关接口
    ORDER: {
      LIST: '/api/order/list',
      DETAIL: '/api/order/detail',
      CREATE: '/api/order/create'
    },
    // 支付相关接口
    PAYMENT: {
      CREATE: '/api/payment/create',
      QUERY: '/api/payment/query',
      NOTIFY: '/api/payment/notify'
    }
  },
  
  // 内容安全检测服务接口
  MEDIA_CHECK: {
    BASE_URL: getCurrentConfig().MEDIA_CHECK,
    // OpenID获取接口
    GET_OPENID: '/api/get-openid',
    // 图片安全检测接口
    CHECK_IMAGE: '/api/check-image'
  }
}

// 请求超时配置
export const TIMEOUT_CONFIG = {
  DEFAULT: 10000,      // 默认超时时间 10秒
  UPLOAD: 30000,       // 上传超时时间 30秒
  PAYMENT: 15000       // 支付超时时间 15秒
}

// 重试配置
export const RETRY_CONFIG = {
  MAX_RETRIES: 2,      // 最大重试次数
  RETRY_DELAY: 1000    // 重试延迟时间（毫秒）
}

// 构建完整的API URL
export function buildApiUrl(service, endpoint) {
  const config = API_CONFIG[service]
  if (!config) {
    throw new Error(`未知的服务: ${service}`)
  }
  
  return config.BASE_URL + endpoint
}

// 图像压缩服务相关URL构建函数
export const getUserLoginUrl = () => buildApiUrl('IMAGE_COMPRESSION', API_CONFIG.IMAGE_COMPRESSION.USER.LOGIN)
export const getUserInfoUrl = () => buildApiUrl('IMAGE_COMPRESSION', API_CONFIG.IMAGE_COMPRESSION.USER.INFO)
export const getMemberStatusUrl = () => buildApiUrl('IMAGE_COMPRESSION', API_CONFIG.IMAGE_COMPRESSION.MEMBERSHIP.STATUS)
export const getProductListUrl = () => buildApiUrl('IMAGE_COMPRESSION', API_CONFIG.IMAGE_COMPRESSION.MEMBERSHIP.PRODUCTS)
export const getOrderListUrl = () => buildApiUrl('IMAGE_COMPRESSION', API_CONFIG.IMAGE_COMPRESSION.ORDER.LIST)
export const getPaymentCreateUrl = () => buildApiUrl('IMAGE_COMPRESSION', API_CONFIG.IMAGE_COMPRESSION.PAYMENT.CREATE)

// 内容安全检测服务相关URL构建函数
export const getOpenIdUrl = () => buildApiUrl('MEDIA_CHECK', API_CONFIG.MEDIA_CHECK.GET_OPENID)
export const getCheckImageUrl = () => buildApiUrl('MEDIA_CHECK', API_CONFIG.MEDIA_CHECK.CHECK_IMAGE)

// 导出环境配置，供其他模块使用
export { ENV, CURRENT_ENV, getCurrentConfig }
