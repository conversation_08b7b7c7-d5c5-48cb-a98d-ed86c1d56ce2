/**
 * 获取用户OpenID的工具函数
 */

// 缓存OpenID的键名
const OPENID_STORAGE_KEY = 'user_openid';

/**
 * 获取用户OpenID
 * 如果本地已有缓存，直接返回缓存值
 * 否则调用服务器接口获取
 */
export async function getOpenId() {
  // 先尝试从缓存获取
  const cachedOpenId = uni.getStorageSync(OPENID_STORAGE_KEY);
  if (cachedOpenId) {
    console.log('使用缓存的OpenID:', cachedOpenId);
    return cachedOpenId;
  }

  // 缓存中没有，需要重新获取
  try {
    // 获取登录凭证
    const loginResult = await uni.login();
    if (!loginResult.code) {
      throw new Error('获取微信登录凭证失败');
    }

    // 发送code到服务器换取OpenID
    const openIdResult = await requestOpenId(loginResult.code);

    // 缓存OpenID
    uni.setStorageSync(OPENID_STORAGE_KEY, openIdResult);
    console.log('获取新的OpenID:', openIdResult);

    return openIdResult;
  } catch (error) {
    console.error('获取OpenID失败:', error);
    throw error;
  }
}

/**
 * 向服务器请求OpenID
 * @param {string} code 微信登录凭证
 * @returns {Promise<string>} OpenID
 */
async function requestOpenId(code) {
  try {
    // 导入网络工具
    const { requestWithRetry, checkNetworkStatus } = require('./networkUtils.js');

    // 检查网络状态
    await checkNetworkStatus();

    // 使用带重试机制的请求
    const res = await requestWithRetry({
      url: 'https://media-check.gbw8848.cn/api/get-openid',
      method: 'POST',
      data: { code }
    }, 2, 10000); // 最多重试2次，超时时间10秒

    if (res.statusCode === 200 && res.data.code === 0) {
      return res.data.data.openid;
    } else {
      throw new Error(res.data.msg || '获取OpenID失败');
    }
  } catch (error) {
    console.error('获取OpenID失败:', error);

    // 优化错误处理
    if (error.message === '无网络连接') {
      uni.showToast({
        title: '网络连接不可用，请检查网络设置',
        icon: 'none',
        duration: 2000
      });
    } else if (error.message.includes('timeout') || error.message.includes('超时')) {
      uni.showToast({
        title: '网络请求超时，请稍后重试',
        icon: 'none',
        duration: 2000
      });
    } else {
      uni.showToast({
        title: '获取用户信息失败，请稍后重试',
        icon: 'none',
        duration: 2000
      });
    }

    throw error;
  }
}

/**
 * 登录到图像压缩服务
 * 确保用户在图像压缩服务中有账户记录
 */
export async function loginToImageCompressionService() {
  try {
    console.log('🔐 开始登录到图像压缩服务...');

    // 获取OpenID
    const openid = await getOpenId();
    if (!openid) {
      throw new Error('获取OpenID失败');
    }

    // 导入API配置和请求工具
    const { getUserLoginUrl, APP_CONFIG } = require('@/config/api.js');
    const { apiPost } = require('./apiRequest.js');

    // 调用登录接口
    const loginResult = await apiPost(getUserLoginUrl(), {
      openid: openid,
      appId: APP_CONFIG.APP_ID,
      appName: APP_CONFIG.APP_NAME
    });

    if (loginResult.success) {
      console.log('✅ 登录图像压缩服务成功:', loginResult.data);
      return {
        success: true,
        userInfo: loginResult.data
      };
    } else {
      throw new Error(loginResult.error || '登录失败');
    }
  } catch (error) {
    console.error('❌ 登录图像压缩服务失败:', error);

    // 不显示错误提示，静默处理
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 清除认证缓存
 */
export function clearAuthCache() {
  try {
    uni.removeStorageSync(OPENID_STORAGE_KEY);
    console.log('🗑️ 认证缓存已清除');
  } catch (error) {
    console.error('清除认证缓存失败:', error);
  }
}

/**
 * 检查是否已登录
 */
export function isLoggedIn() {
  const openid = uni.getStorageSync(OPENID_STORAGE_KEY);
  return !!openid;
}
