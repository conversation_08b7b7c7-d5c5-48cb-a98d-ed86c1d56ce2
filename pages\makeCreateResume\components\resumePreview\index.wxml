<!-- 带滚动功能的预览容器 -->
<scroll-view
  class="resume-preview resume-theme"
  style="{{customStyle}}"
  scroll-y="true"
  enhanced="true"
  show-scrollbar="false"
  bounces="true"
>



  <!-- 加载状态 -->
  <view wx:if="{{imageLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在生成预览图片...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{imageError}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">预览图片生成失败</text>
    <button class="retry-button" bindtap="retryGeneratePreview">重新生成</button>
  </view>

  <!-- 服务端生成的图片预览 - 可滚动查看 -->
  <view wx:elif="{{previewImageUrl}}" class="scrollable-image-container">
    <image
      class="scrollable-preview-image"
      src="{{previewImageUrl}}"
      mode="widthFix"
      binderror="onImageError"
      bindload="onImageLoad"
      lazy-load="{{false}}"
      show-menu-by-longpress="{{true}}"
    />
  </view>

  <!-- 原始模板显示（作为后备） -->
  <view wx:else class="template-container">
    <text class="placeholder-text">正在初始化预览...</text>
  </view>

</scroll-view>