const express = require('express');
const router = express.Router();
const { query } = require('../config/database');

/**
 * 获取商品列表
 * GET /api/products/list?app_id=xxx
 */
router.get('/list', async (req, res) => {
  try {
    const { app_id } = req.query;

    if (!app_id) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数 app_id'
      });
    }

    // 查询指定小程序的商品
    const products = await query(
      `SELECT
        product_id,
        app_id,
        product_code,
        product_name,
        description,
        price,
        duration_hours,
        sort_order,
        is_active,
        created_at,
        updated_at
      FROM products
      WHERE app_id = ? AND is_active = 1
      ORDER BY sort_order ASC, product_id ASC`,
      [app_id]
    );

    res.json({
      success: true,
      data: products,
      count: products.length
    });

  } catch (error) {
    console.error('获取商品列表失败:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
});

/**
 * 获取单个商品详情
 * GET /api/products/detail/:id
 */
router.get('/detail/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const products = await query(
      `SELECT
        product_id,
        app_id,
        product_code,
        product_name,
        description,
        price,
        duration_hours,
        sort_order,
        is_active,
        created_at,
        updated_at
      FROM products
      WHERE product_id = ? AND is_active = 1`,
      [id]
    );

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        error: '商品不存在'
      });
    }

    res.json({
      success: true,
      data: products[0]
    });

  } catch (error) {
    console.error('获取商品详情失败:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
});

/**
 * 根据商品代码获取商品
 * GET /api/products/by-code?app_id=xxx&product_code=xxx
 */
router.get('/by-code', async (req, res) => {
  try {
    const { app_id, product_code } = req.query;

    if (!app_id || !product_code) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数 app_id 或 product_code'
      });
    }

    const products = await query(
      `SELECT
        product_id,
        app_id,
        product_code,
        product_name,
        description,
        price,
        duration_hours,
        sort_order,
        is_active,
        created_at,
        updated_at
      FROM products
      WHERE app_id = ? AND product_code = ? AND is_active = 1`,
      [app_id, product_code]
    );

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        error: '商品不存在'
      });
    }

    res.json({
      success: true,
      data: products[0]
    });

  } catch (error) {
    console.error('获取商品失败:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
});

/**
 * 获取所有小程序的商品统计
 * GET /api/products/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await query(`
      SELECT 
        app_id,
        COUNT(*) as product_count,
        MIN(price) as min_price,
        MAX(price) as max_price,
        AVG(price) as avg_price
      FROM products 
      WHERE is_active = 1
      GROUP BY app_id
      ORDER BY app_id
    `);

    const totalProducts = await query(`
      SELECT COUNT(*) as total FROM products WHERE is_active = 1
    `);

    res.json({
      success: true,
      data: {
        by_app: stats,
        total_products: totalProducts[0].total,
        total_apps: stats.length
      }
    });

  } catch (error) {
    console.error('获取商品统计失败:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
});

module.exports = router;
