from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any, Union
from base64 import b64decode

# class ModuleOrders(BaseModel):
#     moduleOrder: int

class BasicInfo(BaseModel):
    # moduleOrder: int = 0
    # title: str
    name: Optional[str] = None
    gender: Optional[str] = None
    phone: Optional[str] = None
    photoUrl: Optional[str] = None  # Base64编码的图片数据
    city: Optional[str] = None
    email: Optional[str] = None
    wechat: Optional[str] = None
    age: Optional[str] = None
    birthday: Optional[str] = None
    marriage: Optional[str] = None
    politics: Optional[str] = None
    nation: Optional[str] = None
    hometown: Optional[str] = None
    height: Optional[str] = None
    weight: Optional[str] = None
    educationLevel: Optional[str] = None  # 添加学历字段
    customTitle1: Optional[str] = None
    customContent1: Optional[str] = None
    customTitle2: Optional[str] = None
    customContent2: Optional[str] = None

class JobIntention(BaseModel):
    # moduleOrder: int = 1
    # title: str
    position: Optional[str] = None
    city: Optional[str] = None
    salary: Optional[str] = None
    status: Optional[str] = None

class EducationItem(BaseModel):
    school: str
    major: str
    degree: str
    startDate: str
    endDate: str
    description: Optional[str] = None

class WorkItem(BaseModel):
    company: str
    position: str
    startDate: str
    endDate: str
    description: Optional[str] = None

class ProjectItem(BaseModel):
    projectName: str
    role: str
    startDate: str
    endDate: str
    description: Optional[str] = None
    
class CustomItem(BaseModel):
    customName: str
    startDate: str
    endDate: str
    role: str = None
    content: str = None

class SchoolExperienceItem(BaseModel):
    role: Optional[str] = None
    startDate: str
    endDate: str
    content: str

class InternshipItem(BaseModel):
    company: str
    position: str
    startDate: str
    endDate: str
    content: str

class ResumeData(BaseModel):
    moduleOrders: Dict[str, int]
    basicInfo: BasicInfo
    jobIntention: JobIntention
    education: List[EducationItem] = []
    school: List[SchoolExperienceItem] = []
    internship: List[InternshipItem] = []
    work: List[WorkItem] = []
    project: List[ProjectItem] = []
    skills: List[str] = []
    awards: List[str] = []
    interests: List[str] = []
    evaluation: List[Dict[str, Any]] = []    # [{'content': '内容', 'moduleOrder': 1}]
    custom1: List[CustomItem] = []
    custom2: List[CustomItem] = []
    custom3: List[CustomItem] = []