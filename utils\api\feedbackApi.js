/**
 * 反馈相关API接口
 */
const request = require('./request');

/**
 * 提交用户反馈
 * @param {Object} feedbackData 反馈数据
 * @param {string} feedbackData.type 反馈类型
 * @param {string} feedbackData.content 反馈内容
 * @param {string} feedbackData.contact 联系方式
 * @param {Object} feedbackData.deviceInfo 设备信息
 */
function submitFeedback(feedbackData) {
  // 按照服务端API文档格式构建请求数据
  const requestData = {
    type: feedbackData.type,
    content: feedbackData.content,
    contact: feedbackData.contact || '',
    device_info: feedbackData.deviceInfo || {},
    timestamp: Date.now()
  };

  return request.post('/feedback', requestData, {
    showLoading: true,
    loadingText: '提交中...',
    needAuth: false // 根据文档，认证是可选的
  });
}

module.exports = {
  submitFeedback
};
