<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aboutGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF8066;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55A3C;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 信息圆圈背景 -->
  <circle cx="12" cy="12" r="10" fill="url(#aboutGradient)"/>

  <!-- 信息符号 -->
  <circle cx="12" cy="8" r="2" fill="white"/>
  <rect x="10.5" y="11" width="3" height="8" rx="1.5" fill="white"/>

  <!-- 高光效果 -->
  <circle cx="12" cy="12" r="8" fill="white" opacity="0.1"/>
</svg>
