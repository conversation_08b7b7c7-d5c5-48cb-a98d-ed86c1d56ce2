/* pages/makeCreateResume/makeCreateResume.wxss */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.resume-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 固定的预览容器 - 占据主要空间，为底部组件留出空间 */
.fixed-preview-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  /* 为底部模板选择器(200rpx)和工具栏(110rpx)留出空间 */
  padding-bottom: 330rpx;
}

.preview-area {
  width: 100%;
  height: 100%;
}

/* 隐藏HTML生成器 */
.hidden-generator {
  position: absolute;
  left: -9999px;
  visibility: hidden;
  pointer-events: none;
}
