# 微信小程序组件按需注入设置

## 问题描述
在使用uniapp开发微信小程序时，在微信开发者工具中的代码质量检查提示"启用组件按需注入未通过"。

## 解决方案
在项目的`manifest.json`文件中的`mp-weixin`配置节点下添加以下配置：

```json
"lazyCodeLoading": "requiredComponents"
```

## 具体修改
修改前的`mp-weixin`节点：
```json
"mp-weixin" : {
    "appid" : "wx260a731e77e8ba36",
    "setting" : {
        "urlCheck" : false,
        "minified": true,
        "uglifyFileName": true
    },
    "usingComponents" : true
}
```

修改后的`mp-weixin`节点：
```json
"mp-weixin" : {
    "appid" : "wx260a731e77e8ba36",
    "setting" : {
        "urlCheck" : false,
        "minified": true,
        "uglifyFileName": true
    },
    "usingComponents" : true,
    "lazyCodeLoading": "requiredComponents"
}
```

## 功能说明
启用组件按需注入后，微信小程序编译系统会分析代码，只将页面实际使用的组件打包到该页面的代码包中：

1. 如果页面A使用了组件X，那么组件X的代码会被打包到页面A的代码包中
2. 如果页面B没有使用组件X，那么页面B的代码包中就不会包含组件X的代码
3. 如果多个页面都使用了同一个组件，那么这个组件会被分别打包到每个使用它的页面中

这种优化可以有效减小小程序的总体积，提高小程序的加载速度和性能。
