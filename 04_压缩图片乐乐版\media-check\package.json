{"name": "media-check", "version": "1.1.0", "description": "微信内容安全检测API", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "node test.js", "test:multi": "node test-multi-app.js"}, "keywords": ["wechat", "media", "security", "check"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "body-parser": "^1.20.2", "crypto": "^1.0.1", "express": "^4.18.2", "express-xml-bodyparser": "^0.3.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-schedule": "^2.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}