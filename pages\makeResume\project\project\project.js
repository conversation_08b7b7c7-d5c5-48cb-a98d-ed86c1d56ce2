const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();
Page({
  data: {
    projectFormData: [],
    currentIndex: -1,
    startY: 0,
    moveY: 0
  },
  onLoad() {
    this.loadProjectData();
  },
  onShow() {
    this.loadProjectData();
  },
  /**
   * 从全局管理器加载项目经历数据
   */
  loadProjectData() {
    try {
      const projectData = ResumeFormHelper.loadFieldData('project', app);
      this.setData({
        projectFormData: projectData
      });
    } catch (error) {
      console.error('❌ 加载项目经历数据失败:', error);
      // 出错时使用空数据
      this.setData({
        projectFormData: []
      });
    }
  },
  addProject() {
    wx.navigateTo({
      url: '../projectEdit/projectEdit'
    })
  },
  editProject(e) {
    const index = e.currentTarget.dataset.index
    wx.navigateTo({
      url: `../projectEdit/projectEdit?index=${index}`
    })
  },
  /**
   * 删除项目经历
   */
  deleteProject(e) {
    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条项目经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            const projectFormData = [...this.data.projectFormData];
            projectFormData.splice(index, 1);
            // 使用 ResumeFormHelper 统一保存
            const success = ResumeFormHelper.saveFieldData('project', projectFormData, app);
            if (success) {
              this.setData({ projectFormData });
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('❌ 删除项目经历时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },
  // 长按触发移动
  handleLongPress(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY
    })
    wx.vibrateShort() // 添加震动反馈
  },
  // 触摸移动中
  touchMove(e) {
    if (this.data.currentIndex < 0) return
    const moveY = e.touches[0].clientY
    const moveDistance = moveY - this.data.startY
    // 计算目标位置
    const itemHeight = 50 // 每个项目的高度，根据实际调整
    const moveIndex = Math.round(moveDistance / itemHeight)
    let targetIndex = this.data.currentIndex + moveIndex
    targetIndex = Math.max(0, Math.min(targetIndex, this.data.projectFormData.length - 1))
    if (targetIndex !== this.data.currentIndex) {
      // 交换位置
      const projectFormData = [...this.data.projectFormData]
      const temp = projectFormData[this.data.currentIndex]
      projectFormData[this.data.currentIndex] = projectFormData[targetIndex]
      projectFormData[targetIndex] = temp
      this.setData({
        projectFormData,
        currentIndex: targetIndex,
        startY: moveY
      })
    }
  },
  // 触摸结束
  touchEnd() {
    if (this.data.currentIndex >= 0) {
      // 更新所有项的sortIndex
      const projectFormData = this.data.projectFormData.map((item, index) => ({
        ...item,
        sortIndex: index
      }))
      // 保存排序后的数据
      this.setData({
        projectFormData,
        currentIndex: -1,
        startY: 0,
        moveY: 0
      })
      // 使用 ResumeFormHelper 统一保存
      ResumeFormHelper.saveFieldData('project', projectFormData, app);
      // 添加排序成功的提示
      wx.showToast({
        title: '排序成功',
        icon: 'success',
        duration: 1500
      })
    }
  }
})