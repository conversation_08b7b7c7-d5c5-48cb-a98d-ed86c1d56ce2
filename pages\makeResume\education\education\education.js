const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    educationFormData: [],  // 教育经历表单数据
    currentIndex: -1,      // 当前拖动项的索引
    startY: 0,            // 开始触摸的纵坐标
    moveY: 0             // 移动的纵坐标
  },

  onLoad() {
    this.loadEducationData();
  },

  /**
   * 从全局管理器加载教育经历数据
   */
  loadEducationData() {
    try {
      const educationData = ResumeFormHelper.loadFieldData('education', app);

      this.setData({
        educationFormData: educationData
      });


    } catch (error) {
      console.error('❌ 加载教育经历数据失败:', error);
      // 出错时使用空数据
      this.setData({
        educationFormData: []
      });
    }
  },

  // 长按触发
  handleLongPress(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY
    });
  },

  // 触摸移动
  touchMove(e) {
    if (this.data.currentIndex < 0) return;

    const moveY = e.touches[0].clientY;
    const moveDistance = moveY - this.data.startY;

    // 计算目标位置
    const itemHeight = 50; // 每个项目的高度，根据实际调整
    const moveIndex = Math.round(moveDistance / itemHeight);

    let targetIndex = this.data.currentIndex + moveIndex;
    targetIndex = Math.max(0, Math.min(targetIndex, this.data.educationFormData.length - 1));

    if (targetIndex !== this.data.currentIndex) {
      // 交换位置
      const educationFormData = [...this.data.educationFormData];
      const temp = educationFormData[this.data.currentIndex];
      educationFormData[this.data.currentIndex] = educationFormData[targetIndex];
      educationFormData[targetIndex] = temp;

      this.setData({
        educationFormData,
        currentIndex: targetIndex,
        startY: moveY
      });
    }
  },

  // 触摸结束
  touchEnd() {
    if (this.data.currentIndex >= 0) {
      // 更新所有项的sortIndex
      const educationFormData = this.data.educationFormData.map((item, index) => ({
        ...item,
        sortIndex: index,
        moduleOrder: item.moduleOrder || 0  // 保留原有的moduleOrder，如果没有则默认为0
      }));

      // 保存排序后的数据
      this.setData({
        educationFormData,
        currentIndex: -1,
        startY: 0,
        moveY: 0
      });

      // 使用 ResumeFormHelper 统一保存
      ResumeFormHelper.saveFieldData('education', educationFormData, app);
    }
  },

  // 添加教育经历
  addEducation() {
    wx.navigateTo({
      url: '../educationEdit/educationEdit'
    });
  },

  // 编辑教育经历
  editEducation(e) {
    const index = e.currentTarget.dataset.index;
    wx.navigateTo({
      url: `../educationEdit/educationEdit?index=${index}`
    });
  },

  /**
   * 删除教育经历
   */
  deleteEducation(e) {
    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '提示',
      content: '确定要删除这条教育经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            const educationFormData = [...this.data.educationFormData];
            educationFormData.splice(index, 1);

            // 使用 ResumeFormHelper 统一保存
            const success = ResumeFormHelper.saveFieldData('education', educationFormData, app);

            if (success) {
              this.setData({ educationFormData });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });


            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });

            }
          } catch (error) {
            console.error('❌ 删除教育经历时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    this.loadEducationData();
  }
});