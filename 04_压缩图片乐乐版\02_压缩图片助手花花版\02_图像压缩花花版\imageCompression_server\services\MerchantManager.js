const fs = require('fs');
const path = require('path');
const { query } = require('../config/database');

/**
 * 小程序配置管理器
 * 负责加载、管理和匹配小程序配置
 */
class AppManager {
  constructor() {
    this.apps = new Map(); // appId -> config
    this.appNameToConfig = new Map(); // appName -> config
    this.miniappsDir = path.join(__dirname, '../miniapps');
    this.loadAllApps();
  }

  /**
   * 加载所有小程序配置
   */
  loadAllApps() {
    console.log('🔄 正在加载小程序配置...');

    try {
      if (!fs.existsSync(this.miniappsDir)) {
        console.warn('⚠️ 小程序配置目录不存在:', this.miniappsDir);
        return;
      }

      const appDirs = fs.readdirSync(this.miniappsDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      let loadedCount = 0;
      let activeCount = 0;

      appDirs.forEach(appName => {
        try {
          const config = this.loadAppConfig(appName);
          if (config) {
            this.apps.set(config.appid, config);
            this.appNameToConfig.set(appName, config);

            console.log(`✅ 加载小程序: ${config.appid} -> ${appName} (${config.businessName})`);

            loadedCount++;
            if (config.isActive) {
              activeCount++;
            }
          }
        } catch (error) {
          console.error(`❌ 加载小程序 ${appName} 失败:`, error.message);
        }
      });

      console.log(`📊 小程序配置加载完成: ${loadedCount} 个小程序，${activeCount} 个激活`);

    } catch (error) {
      console.error('❌ 加载小程序配置失败:', error);
    }
  }

  /**
   * 加载单个小程序配置
   * @param {string} appName 小程序名称
   * @returns {object|null} 小程序配置
   */
  loadAppConfig(appName) {
    const configPath = path.join(this.miniappsDir, appName, 'config.json');

    if (!fs.existsSync(configPath)) {
      console.warn(`⚠️ 小程序配置文件不存在: ${configPath}`);
      return null;
    }

    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configContent);

      // 验证必要字段
      if (!config.appid || !config.businessName) {
        throw new Error('缺少必要字段: appid, businessName');
      }

      // 补充字段
      config.appName = appName; // 从文件夹名获取

      // 补充默认值
      config.isActive = config.isActive !== false; // 默认激活
      config.loadedAt = new Date().toISOString();
      config.configPath = configPath;

      console.log(`✅ 加载小程序配置: ${appName} (${config.businessName})`);
      return config;

    } catch (error) {
      console.error(`❌ 解析小程序配置失败 ${appName}:`, error.message);
      return null;
    }
  }

  /**
   * 根据AppID获取小程序配置
   * @param {string} appId 小程序AppID
   * @returns {object|null} 小程序配置
   */
  getAppByAppId(appId) {
    if (!appId) {
      console.warn('⚠️ AppID为空，使用默认小程序');
      return this.getDefaultApp();
    }

    const config = this.apps.get(appId);
    if (config && config.isActive) {
      console.log(`🎯 AppID ${appId} 匹配到小程序: ${config.appName} (${config.businessName})`);
      return config;
    }

    console.warn(`⚠️ 未找到AppID ${appId} 对应的激活小程序，使用默认小程序`);
    return this.getDefaultApp();
  }

  /**
   * 根据小程序名称获取配置
   * @param {string} appName 小程序名称
   * @returns {object|null} 小程序配置
   */
  getAppByName(appName) {
    const config = this.appNameToConfig.get(appName);
    if (config && config.isActive) {
      return config;
    }

    console.warn(`⚠️ 小程序 ${appName} 不存在或未激活`);
    return null;
  }

  /**
   * 获取默认小程序（第一个激活的小程序）
   * @returns {object|null} 默认小程序配置
   */
  getDefaultApp() {
    // 优先返回激活的小程序
    for (const [appId, config] of this.apps) {
      if (config.isActive) {
        console.log(`🔧 使用默认激活小程序: ${config.appName} (${config.businessName})`);
        return config;
      }
    }

    // 如果没有激活的小程序，返回第一个
    const firstApp = this.apps.values().next().value;
    if (firstApp) {
      console.log(`🔧 使用第一个可用小程序: ${firstApp.appName} (${firstApp.businessName})`);
      return firstApp;
    }

    console.error('❌ 没有可用的小程序配置');
    return null;
  }

  /**
   * 获取所有小程序列表
   * @param {boolean} activeOnly 是否只返回激活的小程序
   * @returns {Array} 小程序列表
   */
  getAllApps(activeOnly = false) {
    const apps = Array.from(this.apps.values());

    if (activeOnly) {
      return apps.filter(config => config.isActive);
    }

    return apps;
  }

  /**
   * 获取小程序的商品配置（从数据库）
   * @param {string} appId 小程序AppID
   * @returns {Promise<Array>} 商品列表
   */
  async getAppProducts(appId) {
    try {
      const products = await query(
        `SELECT product_id, product_code, product_name, description, price, duration_hours, is_active, sort_order
         FROM products
         WHERE app_id = ? AND is_active = TRUE
         ORDER BY sort_order ASC`,
        [appId]
      );

      return products.map(product => ({
        productId: product.product_id,
        productCode: product.product_code,
        productName: product.product_name,
        description: product.description,
        price: parseFloat(product.price),
        durationHours: product.duration_hours,
        isPermanent: product.product_code === 'permanent',
        isActive: product.is_active,
        sortOrder: product.sort_order
      }));
    } catch (error) {
      console.error(`❌ 获取小程序 ${appId} 商品失败:`, error);
      return [];
    }
  }

  /**
   * 根据AppID获取商品配置（别名方法，保持兼容性）
   * @param {string} appId 小程序AppID
   * @returns {Promise<Array>} 商品列表
   */
  async getProductsByAppId(appId) {
    return await this.getAppProducts(appId);
  }

  /**
   * 根据AppID和商品代码获取单个商品信息
   * @param {string} appId 小程序AppID
   * @param {string} productCode 商品代码
   * @returns {Promise<Object|null>} 商品信息
   */
  async getProductByCode(appId, productCode) {
    try {
      const products = await query(
        `SELECT product_id, product_code, product_name, description, price, duration_hours, is_active
         FROM products
         WHERE app_id = ? AND product_code = ? AND is_active = TRUE`,
        [appId, productCode]
      );

      if (products.length === 0) {
        return null;
      }

      const product = products[0];
      return {
        productId: product.product_id,
        productCode: product.product_code,
        productName: product.product_name,
        description: product.description,
        price: parseFloat(product.price),
        durationHours: product.duration_hours,
        isPermanent: product.product_code === 'permanent',
        isActive: product.is_active
      };
    } catch (error) {
      console.error(`❌ 获取商品 ${appId}:${productCode} 失败:`, error);
      return null;
    }
  }

  /**
   * 重新加载小程序配置
   */
  reload() {
    console.log('🔄 重新加载小程序配置...');
    this.apps.clear();
    this.appNameToConfig.clear();
    this.loadAllApps();
  }

  /**
   * 验证小程序配置
   * @param {string} appId 小程序AppID
   * @returns {object} 验证结果
   */
  validateApp(appId) {
    const config = this.apps.get(appId);
    if (!config) {
      return { valid: false, errors: ['小程序配置不存在'] };
    }

    const errors = [];

    // 检查必要字段
    if (!config.appid) errors.push('缺少 appid');
    if (!config.businessName) errors.push('缺少 businessName');

    // 检查微信配置
    if (config.wechat_config) {
      if (!config.wechat_config.app_id) errors.push('缺少 wechat_config.app_id');
      if (!config.wechat_config.app_secret || config.wechat_config.app_secret.includes('请填写')) {
        errors.push('wechat_config.app_secret 未配置');
      }
    }

    // 检查支付配置（公钥验签）
    if (!config.mchid || config.mchid.includes('请填写')) {
      errors.push('mchid 未配置');
    }
    if (!config.public_key_id || config.public_key_id.includes('请填写')) {
      errors.push('public_key_id 未配置');
    }

    // 检查商品配置
    if (!config.products || Object.keys(config.products).length === 0) {
      errors.push('缺少商品配置');
    }

    const warnings = [];
    if (config.api_v3_key?.includes('请填写')) warnings.push('API V3密钥未配置');
    if (!config.public_key_path) warnings.push('公钥文件路径未配置');
    if (!config.private_key_path) warnings.push('私钥文件路径未配置');

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 获取统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    const total = this.apps.size;
    const active = this.getAllApps(true).length;

    return {
      totalApps: total,
      activeApps: active,
      apps: Array.from(this.apps.values()).map(config => ({
        appid: config.appid,
        appName: config.appName,
        businessName: config.businessName,
        mchid: config.mchid,
        isActive: config.isActive,
        hasWechatConfig: !!(config.wechat_config?.app_secret && !config.wechat_config.app_secret.includes('请填写'))
      }))
    };
  }


}

// 创建单例实例
const appManager = new AppManager();

module.exports = appManager;
