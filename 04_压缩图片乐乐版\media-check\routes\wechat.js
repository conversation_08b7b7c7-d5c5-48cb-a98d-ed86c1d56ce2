const express = require('express');
const crypto = require('crypto');
const config = require('../config');
const { storeCheckResult } = require('./api');

const router = express.Router();

/**
 * 微信服务器验证接口
 * GET /wechat
 */
router.get('/', (req, res) => {
  const { signature, timestamp, nonce, echostr } = req.query;

  // 验证逻辑
  const arr = [config.wechat.token, timestamp, nonce].sort();
  const str = arr.join('');
  const sha1Str = crypto.createHash('sha1').update(str).digest('hex');

  if (sha1Str === signature) {
    res.send(echostr);
  } else {
    res.status(403).send('验证失败');
  }
});

/**
 * 接收微信消息和事件推送
 * POST /wechat
 */
router.post('/', (req, res) => {
  try {
    // 微信消息是XML格式，express-xml-bodyparser会将其转换为JS对象
    const xmlMsg = req.body;
    console.log('收到微信消息:', JSON.stringify(xmlMsg));

    // 提取XML消息中的关键字段
    const xml = xmlMsg.xml || {};
    const event = xml.event && xml.event[0];

    // 处理内容安全检测事件
    if (event === 'wxa_media_check') {
      console.log('收到内容安全检测事件');

      // 提取检测结果
      const traceId = xml.trace_id && xml.trace_id[0];
      const result = xml.result && xml.result[0];
      const suggest = result && result.suggest && result.suggest[0];
      const label = result && result.label && result.label[0];

      console.log(`内容检测结果: trace_id=${traceId}, suggest=${suggest}, label=${label}`);

      // 存储检测结果，无论是否违规
      storeCheckResult(traceId, suggest, label);

      // 如果检测结果为违规(risky)，记录下来
      if (suggest === 'risky') {
        console.log(`发现违规内容: trace_id=${traceId}, label=${label}`);
      }
    }

    // 返回成功响应给微信服务器
    res.send('success');
  } catch (error) {
    console.error('处理微信消息出错:', error);
    // 即使处理出错，也要返回成功，避免微信服务器重试
    res.send('success');
  }
});

/**
 * 接收微信内容安全检测结果的回调接口
 * POST /wechat/security-check-callback
 *
 * 注意：微信官方文档中的异步检测结果是推送到根路径的，
 * 但为了兼容可能的其他实现，我们保留这个路由
 */
router.post('/security-check-callback', (req, res) => {
  try {
    // 可能是JSON格式
    if (req.body && typeof req.body === 'object' && !req.body.xml) {
      const data = req.body;
      console.log('收到JSON格式内容安全检测回调:', JSON.stringify(data));

      // 提取检测结果
      const traceId = data.trace_id;
      const suggest = data.result && data.result.suggest;
      const label = data.result && data.result.label;

      console.log(`内容检测结果: trace_id=${traceId}, suggest=${suggest}, label=${label}`);

      // 存储检测结果，无论是否违规
      storeCheckResult(traceId, suggest, label);

      // 如果检测结果为违规(risky)，记录下来
      if (suggest === 'risky') {
        console.log(`发现违规内容: trace_id=${traceId}, label=${label}`);
      }
    }
    // 可能是XML格式
    else if (req.body && req.body.xml) {
      const xml = req.body.xml;
      console.log('收到XML格式内容安全检测回调:', JSON.stringify(xml));

      // 提取检测结果
      const traceId = xml.trace_id && xml.trace_id[0];
      const result = xml.result && xml.result[0];
      const suggest = result && result.suggest && result.suggest[0];
      const label = result && result.label && result.label[0];

      console.log(`内容检测结果: trace_id=${traceId}, suggest=${suggest}, label=${label}`);

      // 存储检测结果，无论是否违规
      storeCheckResult(traceId, suggest, label);

      // 如果检测结果为违规(risky)，记录下来
      if (suggest === 'risky') {
        console.log(`发现违规内容: trace_id=${traceId}, label=${label}`);
      }
    }

    // 返回成功响应给微信服务器
    res.send('success');
  } catch (error) {
    console.error('处理回调出错:', error);
    // 即使处理出错，也要返回成功，避免微信服务器重试
    res.send('success');
  }
});

module.exports = router;
