<!-- 模块管理页面 -->
<view class="module-manage">
  <view class="module-list">
    <!-- 所有模块列表（包括 basicInfo，但 basicInfo 有特殊样式提示） -->
    <view class="module-container">
      <view
        wx:for="{{activeModules}}"
        wx:key="id"
        class="module-item {{item.type === 'basicInfo' ? 'basic-info-item' : ''}}"
        data-index="{{index}}">
        <view class="module-content">
          <view class="module-name">{{item.name}}</view>
          <view class="module-tip" wx:if="{{item.type === 'basicInfo'}}">（置顶）</view>
        </view>
        <view class="module-controls" wx:if="{{item.type !== 'basicInfo'}}">
          <view class="control-btn up-btn" bindtap="moveUp" data-index="{{index}}" wx:if="{{index > 0}}">
            <text class="icon">↑</text>
          </view>
          <view class="control-btn down-btn" bindtap="moveDown" data-index="{{index}}" wx:if="{{index < activeModules.length - 1}}">
            <text class="icon">↓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="tip-text" wx:if="{{!activeModules.length}}">暂无已填写的模块内容</view>
  <view class="save-btn" bindtap="saveModuleSettings" wx:if="{{activeModules.length}}">保存设置</view>
</view>