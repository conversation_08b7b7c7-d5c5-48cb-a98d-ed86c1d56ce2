// utils/freeCounter.js - 免费次数管理（纯免费模式，无广告）
const FREE_COUNT_KEY = 'free_compression_count'; // 免费次数
const LAST_DATE_KEY = 'last_compression_date'; // 上次使用日期

// 获取今天的日期字符串（YYYY-MM-DD格式）
function getTodayDateString() {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 重置免费次数
function resetFreeCount(count = 1) {
  const today = getTodayDateString();
  uni.setStorageSync(FREE_COUNT_KEY, count);
  uni.setStorageSync(LAST_DATE_KEY, today);
}

// 获取用户剩余免费次数
export function getFreeCount() {
  // 检查是否需要重置（新的一天）
  checkAndResetDaily();

  // 获取当前免费次数
  const freeCount = uni.getStorageSync(FREE_COUNT_KEY) || 0;
  return freeCount;
}

// 减少免费次数
export function decreaseFreeCount() {
  // 先检查是否需要重置
  checkAndResetDaily();

  // 获取当前免费次数
  let freeCount = uni.getStorageSync(FREE_COUNT_KEY) || 0;

  // 如果有免费次数，则减少
  if (freeCount > 0) {
    freeCount--;
    uni.setStorageSync(FREE_COUNT_KEY, freeCount);
  }

  return freeCount;
}

// 检查并根据日期重置免费次数
function checkAndResetDaily() {
  const today = getTodayDateString();
  const lastDate = uni.getStorageSync(LAST_DATE_KEY) || '';

  // 如果日期不同，说明是新的一天
  if (today !== lastDate) {
    resetFreeCount(1); // 重置为1次免费机会
  }
}

// 获取免费次数状态文本
export function getFreeCountText() {
  const freeCount = getFreeCount();
  if (freeCount > 0) {
    return `剩余 ${freeCount} 次免费压缩`;
  } else {
    return '免费次数已用完，请购买会员';
  }
}
