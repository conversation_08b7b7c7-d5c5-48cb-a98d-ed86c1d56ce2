/**
 * 错误测试辅助工具
 * 提供开发调试时的错误测试功能
 */

class ErrorTestHelper {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
  }

  /**
   * 添加到首页进行快速测试
   */
  static addToHomePage() {
    // 在首页添加一个隐藏的测试按钮
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/index/index') {
      // 添加测试方法到首页
      currentPage.quickErrorTest = this.quickErrorTest.bind(this);
      
      console.log('✅ 错误测试工具已添加到首页，可调用 getCurrentPages()[0].quickErrorTest()');
    }
  }

  /**
   * 快速错误测试
   */
  static quickErrorTest() {
    const app = getApp();
    
    console.log('🧪 开始快速错误测试...');
    
    // 测试1: 手动错误上报
    app.reportError('quick_test_error', '快速测试错误', {
      test_type: 'quick_test',
      timestamp: new Date().toISOString()
    });
    
    // 测试2: 模拟网络错误
    setTimeout(() => {
      app.reportError('quick_network_test', '模拟网络错误', {
        test_type: 'network_simulation',
        error_code: 'NETWORK_TIMEOUT'
      });
    }, 1000);
    
    // 测试3: 模拟业务错误
    setTimeout(() => {
      app.reportError('quick_business_test', '模拟业务逻辑错误', {
        test_type: 'business_simulation',
        operation: 'data_validation'
      });
    }, 2000);
    
    console.log('✅ 快速错误测试完成，请检查控制台和服务端日志');
    
    wx.showToast({
      title: '错误测试已发送',
      icon: 'success'
    });
  }

  /**
   * 批量测试错误上报
   */
  static async batchErrorTest(count = 5) {
    const app = getApp();
    
    console.log(`🧪 开始批量错误测试，数量: ${count}`);
    
    for (let i = 0; i < count; i++) {
      app.reportError(`batch_test_error_${i}`, `批量测试错误 ${i + 1}`, {
        test_type: 'batch_test',
        batch_index: i,
        batch_total: count,
        timestamp: new Date().toISOString()
      });
      
      // 间隔100ms发送
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('✅ 批量错误测试完成');
    
    wx.showToast({
      title: `${count}个测试错误已发送`,
      icon: 'success'
    });
  }

  /**
   * 测试不同类型的错误
   */
  static testErrorTypes() {
    const app = getApp();
    
    const errorTypes = [
      {
        type: 'validation_error',
        message: '数据验证失败',
        context: { field: 'phone', value: 'invalid' }
      },
      {
        type: 'permission_error',
        message: '权限不足',
        context: { permission: 'camera', action: 'denied' }
      },
      {
        type: 'network_error',
        message: '网络连接失败',
        context: { url: '/api/test', timeout: 5000 }
      },
      {
        type: 'storage_error',
        message: '存储空间不足',
        context: { operation: 'save', size: '10MB' }
      },
      {
        type: 'render_error',
        message: '页面渲染失败',
        context: { component: 'list', data_size: 1000 }
      }
    ];
    
    errorTypes.forEach((error, index) => {
      setTimeout(() => {
        app.reportError(error.type, error.message, {
          test_type: 'error_type_test',
          ...error.context
        });
      }, index * 500);
    });
    
    console.log('✅ 错误类型测试已启动');
    
    wx.showToast({
      title: '错误类型测试已启动',
      icon: 'success'
    });
  }

  /**
   * 模拟真实场景错误
   */
  static simulateRealScenarios() {
    const app = getApp();
    
    // 场景1: 用户操作失败
    setTimeout(() => {
      app.reportError('user_action_failed', '用户操作失败', {
        page: 'makeResume',
        action: 'save_resume',
        user_id: 'test_user_123',
        error_reason: 'network_timeout'
      });
    }, 1000);
    
    // 场景2: 数据同步失败
    setTimeout(() => {
      app.reportError('data_sync_failed', '数据同步失败', {
        operation: 'sync_resume_data',
        local_version: '1.2.3',
        server_version: '1.2.4',
        conflict_fields: ['basicInfo', 'education']
      });
    }, 2000);
    
    // 场景3: 图片处理失败
    setTimeout(() => {
      app.reportError('image_process_failed', '图片处理失败', {
        operation: 'compress_image',
        original_size: '2.5MB',
        target_size: '500KB',
        format: 'jpeg',
        error_stage: 'canvas_export'
      });
    }, 3000);
    
    // 场景4: PDF生成失败
    setTimeout(() => {
      app.reportError('pdf_generation_failed', 'PDF生成失败', {
        template_id: 'template_A01',
        data_size: '150KB',
        timeout: true,
        retry_count: 3
      });
    }, 4000);
    
    console.log('✅ 真实场景错误模拟已启动');
    
    wx.showToast({
      title: '场景模拟已启动',
      icon: 'success'
    });
  }

  /**
   * 检查错误上报状态
   */
  static checkErrorReporterStatus() {
    try {
      const errorReporter = require('../error/errorReporter');
      const status = errorReporter.getQueueStatus();
      
      console.log('📊 错误上报器状态:', {
        队列长度: status.queue_length,
        正在上报: status.is_reporting,
        已启用: status.is_enabled
      });
      
      return status;
    } catch (error) {
      console.error('❌ 获取错误上报器状态失败:', error);
      return null;
    }
  }

  /**
   * 清空错误队列
   */
  static clearErrorQueue() {
    try {
      const errorReporter = require('../error/errorReporter');
      errorReporter.clearQueue();
      
      console.log('🗑️ 错误队列已清空');
      
      wx.showToast({
        title: '队列已清空',
        icon: 'success'
      });
    } catch (error) {
      console.error('❌ 清空错误队列失败:', error);
      
      wx.showToast({
        title: '清空失败',
        icon: 'none'
      });
    }
  }

  /**
   * 启用/禁用错误上报
   */
  static toggleErrorReporter(enabled) {
    try {
      const errorReporter = require('../error/errorReporter');
      errorReporter.setEnabled(enabled);
      
      console.log(`${enabled ? '✅' : '❌'} 错误上报已${enabled ? '启用' : '禁用'}`);
      
      wx.showToast({
        title: `错误上报已${enabled ? '启用' : '禁用'}`,
        icon: 'success'
      });
    } catch (error) {
      console.error('❌ 切换错误上报状态失败:', error);
    }
  }
}

// 在开发环境下自动添加到全局
if (typeof getApp !== 'undefined') {
  try {
    const app = getApp();
    if (app) {
      app.errorTestHelper = ErrorTestHelper;
      console.log('🧪 错误测试工具已添加到全局 app.errorTestHelper');
    }
  } catch (e) {
    // 忽略错误，可能是在模块加载阶段
  }
}

module.exports = ErrorTestHelper;
