<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<defs>
  <!-- 主题色渐变 -->
  <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:0.1" />
    <stop offset="100%" style="stop-color:#0284C7;stop-opacity:0.2" />
  </linearGradient>
  <!-- 用户图标渐变 -->
  <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
    <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:1" />
    <stop offset="100%" style="stop-color:#0284C7;stop-opacity:1" />
  </linearGradient>
</defs>
<circle cx="30" cy="30" r="30" fill="url(#avatarGradient)" stroke="#0EA5E9" stroke-width="1" stroke-opacity="0.3"/>
<circle cx="30" cy="22" r="8" fill="url(#userGradient)"/>
<path d="M15 45C15 38.3726 20.3726 33 27 33H33C39.6274 33 45 38.3726 45 45V50H15V45Z" fill="url(#userGradient)"/>
</svg>
