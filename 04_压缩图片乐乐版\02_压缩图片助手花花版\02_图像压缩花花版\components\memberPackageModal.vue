<template>
	<view class="modal-overlay" v-if="visible" @tap="handleOverlayTap">
		<view class="modal-content neumorphism" @tap.stop>
			<!-- 模态框头部 -->
			<view class="modal-header">
				<text class="modal-title">选择会员套餐</text>
				<button class="close-btn" @tap="closeModal">
					<text class="close-icon">×</text>
				</button>
			</view>
			
			<!-- 套餐列表 -->
			<view class="packages-list">
				<!-- 日卡会员 -->
				<view 
					class="package-item" 
					:class="{ 'selected': selectedPackage === 'DAY_CARD' }"
					@tap="selectPackage('DAY_CARD')"
				>
					<view class="package-info">
						<view class="package-header">
							<text class="package-name">日卡会员</text>
							<view class="package-badge">
								<text class="badge-text">24小时</text>
							</view>
						</view>
						<text class="package-desc">{{ dayCardPackage.description }}</text>
						<view class="package-price">
							<text class="current-price">¥{{ dayCardPackage.currentPrice }}</text>
							<text class="original-price" v-if="dayCardPackage.originalPrice && dayCardPackage.originalPrice !== dayCardPackage.currentPrice">¥{{ dayCardPackage.originalPrice }}</text>
						</view>
					</view>
					<view class="package-radio">
						<view class="radio-circle" :class="{ 'checked': selectedPackage === 'DAY_CARD' }">
							<view class="radio-dot" v-if="selectedPackage === 'DAY_CARD'"></view>
						</view>
					</view>
				</view>
				
				<!-- 永久会员 -->
				<view 
					class="package-item" 
					:class="{ 'selected': selectedPackage === 'PERMANENT' }"
					@tap="selectPackage('PERMANENT')"
				>
					<view class="package-info">
						<view class="package-header">
							<text class="package-name">永久会员</text>
							<view class="package-badge permanent">
								<text class="badge-text">推荐</text>
							</view>
						</view>
						<text class="package-desc">{{ permanentPackage.description }}</text>
						<view class="package-price">
							<text class="current-price">¥{{ permanentPackage.currentPrice }}</text>
							<text class="original-price" v-if="permanentPackage.originalPrice && permanentPackage.originalPrice !== permanentPackage.currentPrice">¥{{ permanentPackage.originalPrice }}</text>
						</view>
					</view>
					<view class="package-radio">
						<view class="radio-circle" :class="{ 'checked': selectedPackage === 'PERMANENT' }">
							<view class="radio-dot" v-if="selectedPackage === 'PERMANENT'"></view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 支付按钮 -->
			<view class="modal-footer">
				<button class="pay-btn" @tap="handlePurchase" :disabled="!selectedPackage">
					<text>确认购买</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getMemberPackages, purchaseMember } from '@/utils/memberManager.js'

export default {
	name: 'MemberPackageModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			selectedPackage: 'PERMANENT', // 默认选择永久会员
			packages: {}, // 存储从API获取的套餐数据
			loading: false
		}
	},
	computed: {
		// 日卡套餐数据
		dayCardPackage() {
			return this.packages.DAY_CARD || {}
		},
		// 永久套餐数据
		permanentPackage() {
			return this.packages.PERMANENT || {}
		}
	},
	async mounted() {
		await this.loadPackages()
	},
	methods: {
		// 加载套餐数据
		async loadPackages() {
			try {
				this.loading = true
				console.log('🔍 [Modal] 开始加载会员套餐数据...')

				// 强制从服务器刷新价格数据
				this.packages = await getMemberPackages(true)
				console.log('✅ [Modal] 套餐数据加载成功:', JSON.stringify(this.packages, null, 2))
				console.log('📊 [Modal] 日卡数据:', JSON.stringify(this.dayCardPackage, null, 2))
				console.log('📊 [Modal] 永久数据:', JSON.stringify(this.permanentPackage, null, 2))
			} catch (error) {
				console.error('❌ 加载套餐数据失败:', error)
				// 不使用默认数据，让错误暴露出来便于测试
				uni.showToast({
					title: '加载套餐失败',
					icon: 'none',
					duration: 2000
				})
			} finally {
				this.loading = false
			}
		},

		// 选择套餐
		selectPackage(packageType) {
			this.selectedPackage = packageType
		},
		
		// 关闭模态框
		closeModal() {
			this.$emit('close')
		},
		
		// 点击遮罩层关闭
		handleOverlayTap() {
			this.closeModal()
		},
		
		// 处理购买
		async handlePurchase() {
			if (!this.selectedPackage) {
				uni.showToast({
					title: '请选择会员套餐',
					icon: 'none'
				})
				return
			}
			
			try {
				// 显示加载提示
				uni.showLoading({
					title: '创建订单中...'
				})

				// 调用真实的微信支付
				const result = await purchaseMember(this.selectedPackage)

				uni.hideLoading()

				if (result.success) {
					// 支付成功
					uni.showToast({
						title: result.message || '支付成功',
						icon: 'success'
					})

					// 通知父组件支付成功
					this.$emit('purchase-success', {
						packageType: this.selectedPackage,
						orderId: result.orderId
					})

					// 关闭模态框
					this.closeModal()
				} else {
					// 支付失败
					uni.showToast({
						title: result.error || '支付失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('购买会员失败:', error)
				uni.showToast({
					title: '购买失败，请重试',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style lang="scss">
// 使用uni.scss中定义的主题色变量
$primary-color: $uni-color-primary;
$bg-color: $uni-bg-color-grey;
$text-primary: $uni-text-color;
$text-secondary: $theme-text-secondary;
$text-tertiary: $uni-text-color-grey;
$shadow-dark: $theme-shadow-dark;
$shadow-light: $theme-shadow-light;

// 新拟物风格的混入
@mixin neumorphism {
	background: $bg-color;
	box-shadow: 12px 12px 24px $shadow-dark,
				-8px -8px 20px $shadow-light,
				inset 2px 2px 4px rgba(255, 255, 255, 0.5),
				inset -2px -2px 4px rgba(0, 0, 0, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10000;
	padding: 60rpx 40rpx;

	.modal-content {
		@include neumorphism;
		border-radius: 30rpx;
		width: 100%;
		max-width: 600rpx;
		max-height: 80vh;
		overflow: hidden;

		.modal-header {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 40rpx 40rpx 20rpx;
			border-bottom: 1px solid rgba(0, 0, 0, 0.05);

			.modal-title {
				font-size: 36rpx;
				font-weight: 600;
				color: $text-primary;
			}

			.close-btn {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				width: 60rpx;
				height: 60rpx;
				background: transparent;
				border: none;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				transition: background-color 0.2s ease;

				&::after {
					display: none;
				}

				&:active {
					background: rgba(0, 0, 0, 0.05);
				}

				.close-icon {
					font-size: 40rpx;
					color: $text-tertiary;
					line-height: 1;
					font-weight: 300;
				}
			}
		}

		.packages-list {
			padding: 20rpx 40rpx;

			.package-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;
				margin-bottom: 20rpx;
				border-radius: 20rpx;
				border: 2px solid transparent;
				background: rgba(255, 255, 255, 0.5);
				transition: all 0.3s ease;

				&:last-child {
					margin-bottom: 0;
				}

				&.selected {
					border-color: $primary-color;
					background: rgba($primary-color, 0.05);
				}

				&:active {
					transform: scale(0.98);
				}

				.package-info {
					flex: 1;

					.package-header {
						display: flex;
						align-items: center;
						margin-bottom: 8rpx;

						.package-name {
							font-size: 32rpx;
							font-weight: 600;
							color: $text-primary;
							margin-right: 16rpx;
						}

						.package-badge {
							background: linear-gradient(135deg, $primary-color, #06AD56);
							color: white;
							font-size: 20rpx;
							padding: 4rpx 12rpx;
							border-radius: 12rpx;

							&.permanent {
								background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
							}

							.badge-text {
								font-weight: 500;
							}
						}
					}

					.package-desc {
						font-size: 26rpx;
						color: $text-secondary;
						margin-bottom: 12rpx;
					}

					.package-price {
						display: flex;
						align-items: center;

						.current-price {
							font-size: 36rpx;
							font-weight: 600;
							color: $primary-color;
							margin-right: 16rpx;
						}

						.original-price {
							font-size: 24rpx;
							color: $text-tertiary;
							text-decoration: line-through;
						}
					}
				}

				.package-radio {
					.radio-circle {
						width: 40rpx;
						height: 40rpx;
						border: 2px solid #ddd;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						transition: all 0.2s ease;

						&.checked {
							border-color: $primary-color;
							background: $primary-color;
						}

						.radio-dot {
							width: 20rpx;
							height: 20rpx;
							background: white;
							border-radius: 50%;
						}
					}
				}
			}
		}

		.modal-footer {
			padding: 20rpx 40rpx 40rpx;

			.pay-btn {
				width: 100%;
				height: 80rpx;
				background: linear-gradient(135deg, $primary-color, #06AD56);
				border: none;
				border-radius: 40rpx;
				color: white;
				font-size: 32rpx;
				font-weight: 500;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);

				&::after {
					display: none;
				}

				&:active {
					background: linear-gradient(135deg, #06AD56, #059C4F);
					transform: scale(0.98);
				}

				&:disabled {
					background: #ccc;
					box-shadow: none;
					transform: none;
				}
			}
		}
	}
}
</style>
