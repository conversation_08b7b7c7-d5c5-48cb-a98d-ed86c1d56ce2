/**
 * 网络请求优化工具
 * 用于优化小程序的网络请求性能
 */

/**
 * 检查网络状态
 * @returns {Promise<string>} 网络类型
 */
export function checkNetworkStatus() {
  return new Promise((resolve, reject) => {
    uni.getNetworkType({
      success: (res) => {
        if (res.networkType === 'none') {
          console.log('网络连接不可用');
          reject(new Error('无网络连接'));
        } else {
          resolve(res.networkType);
        }
      },
      fail: () => {
        reject(new Error('获取网络状态失败'));
      }
    });
  });
}

/**
 * 带重试机制的网络请求
 * @param {Object} options - uni.request的选项
 * @param {number} maxRetries - 最大重试次数
 * @param {number} timeout - 超时时间(毫秒)
 * @returns {Promise} 请求结果
 */
export function requestWithRetry(options, maxRetries = 2, timeout = 15000) {
  let retryCount = 0;
  
  return new Promise((resolve, reject) => {
    function attemptRequest() {
      // 克隆选项对象，避免修改原对象
      const requestOptions = {...options};
      
      // 添加超时设置
      requestOptions.timeout = timeout;
      
      // 保存原始的success和fail回调
      const originalSuccess = requestOptions.success;
      const originalFail = requestOptions.fail;
      
      // 重写success回调
      requestOptions.success = (res) => {
        if (originalSuccess) originalSuccess(res);
        resolve(res);
      };
      
      // 重写fail回调
      requestOptions.fail = (err) => {
        console.log('请求失败:', err);
        
        // 如果是超时错误或网络错误，尝试重试
        if ((err.errMsg && (
            err.errMsg.includes('timeout') || 
            err.errMsg.includes('fail') || 
            err.errMsg.includes('network')
          )) && retryCount < maxRetries) {
          retryCount++;
          console.log(`请求失败，第${retryCount}次重试...`);
          
          // 使用指数退避策略，延迟时间随重试次数增加
          setTimeout(attemptRequest, 1000 * Math.pow(2, retryCount - 1));
        } else {
          // 超过重试次数或其他错误，调用原始fail回调并拒绝promise
          if (originalFail) originalFail(err);
          reject(err);
        }
      };
      
      // 发起请求
      uni.request(requestOptions);
    }
    
    // 首次请求前检查网络状态
    checkNetworkStatus()
      .then(() => attemptRequest())
      .catch(reject);
  });
}

/**
 * 带重试机制的上传文件请求
 * @param {Object} options - uni.uploadFile的选项
 * @param {number} maxRetries - 最大重试次数
 * @param {number} timeout - 超时时间(毫秒)
 * @returns {Promise} 上传结果
 */
export function uploadFileWithRetry(options, maxRetries = 2, timeout = 30000) {
  let retryCount = 0;
  
  return new Promise((resolve, reject) => {
    function attemptUpload() {
      // 克隆选项对象，避免修改原对象
      const uploadOptions = {...options};
      
      // 保存原始的success和fail回调
      const originalSuccess = uploadOptions.success;
      const originalFail = uploadOptions.fail;
      
      // 重写success回调
      uploadOptions.success = (res) => {
        if (originalSuccess) originalSuccess(res);
        resolve(res);
      };
      
      // 重写fail回调
      uploadOptions.fail = (err) => {
        console.log('上传失败:', err);
        
        // 如果是超时错误或网络错误，尝试重试
        if ((err.errMsg && (
            err.errMsg.includes('timeout') || 
            err.errMsg.includes('fail') || 
            err.errMsg.includes('network')
          )) && retryCount < maxRetries) {
          retryCount++;
          console.log(`上传失败，第${retryCount}次重试...`);
          
          // 使用指数退避策略，延迟时间随重试次数增加
          setTimeout(attemptUpload, 1000 * Math.pow(2, retryCount - 1));
        } else {
          // 超过重试次数或其他错误，调用原始fail回调并拒绝promise
          if (originalFail) originalFail(err);
          reject(err);
        }
      };
      
      // 发起上传请求
      const uploadTask = uni.uploadFile(uploadOptions);
      
      // 设置超时
      const timeoutId = setTimeout(() => {
        try {
          // 尝试中断请求
          uploadTask.abort();
        } catch (e) {
          console.error('中断上传请求失败:', e);
        }
        
        // 如果还有重试次数，则重试
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`上传超时，第${retryCount}次重试...`);
          setTimeout(attemptUpload, 1000 * Math.pow(2, retryCount - 1));
        } else {
          // 超过重试次数，拒绝promise
          reject(new Error('上传请求超时'));
        }
      }, timeout);
      
      // 监听上传完成，清除超时定时器
      uploadTask.onProgressUpdate((res) => {
        if (res.progress === 100 && res.totalBytesExpectedToSend === res.totalBytesSent) {
          clearTimeout(timeoutId);
        }
      });
      
      return uploadTask;
    }
    
    // 首次上传前检查网络状态
    checkNetworkStatus()
      .then(() => attemptUpload())
      .catch(reject);
  });
}

/**
 * 请求队列管理器
 * 用于控制并发请求数量
 */
class RequestQueue {
  constructor(maxConcurrent = 2) {
    this.queue = [];
    this.running = 0;
    this.maxConcurrent = maxConcurrent;
  }
  
  /**
   * 添加请求任务到队列
   * @param {Function} requestFn - 返回Promise的请求函数
   * @returns {Promise} 请求结果
   */
  add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        requestFn,
        resolve,
        reject
      });
      this.processQueue();
    });
  }
  
  /**
   * 处理队列中的请求
   */
  processQueue() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    const { requestFn, resolve, reject } = this.queue.shift();
    this.running++;
    
    requestFn()
      .then(resolve)
      .catch(reject)
      .finally(() => {
        this.running--;
        this.processQueue();
      });
  }
}

// 创建一个全局请求队列实例
export const requestQueue = new RequestQueue(2);

/**
 * 图片预压缩处理
 * 在上传前对图片进行压缩，减小传输数据量
 * @param {string} filePath - 图片路径
 * @param {Object} options - 压缩选项
 * @returns {Promise<string>} 压缩后的图片路径
 */
export async function preprocessImageForUpload(filePath, options = {}) {
  try {
    // 默认压缩选项
    const defaultOptions = {
      maxWidth: 1280,      // 最大宽度
      maxHeight: 1280,     // 最大高度
      quality: 0.8,        // 压缩质量(0-1)
      fileType: 'jpg'      // 文件类型
    };
    
    // 合并选项
    const compressOptions = {...defaultOptions, ...options};
    
    // 获取图片信息
    const imageInfo = await new Promise((resolve, reject) => {
      uni.getImageInfo({
        src: filePath,
        success: resolve,
        fail: reject
      });
    });
    
    // 获取文件信息
    const fileInfo = await new Promise((resolve, reject) => {
      const fs = uni.getFileSystemManager();
      fs.getFileInfo({
        filePath: filePath,
        success: resolve,
        fail: reject
      });
    });
    
    // 如果图片尺寸小于阈值且文件大小小于1MB，直接返回原图
    if (imageInfo.width <= compressOptions.maxWidth && 
        imageInfo.height <= compressOptions.maxHeight && 
        fileInfo.size < 1024 * 1024) {
      return filePath;
    }
    
    // 计算压缩后的尺寸，保持宽高比
    let targetWidth, targetHeight;
    
    if (imageInfo.width > compressOptions.maxWidth || imageInfo.height > compressOptions.maxHeight) {
      const widthRatio = compressOptions.maxWidth / imageInfo.width;
      const heightRatio = compressOptions.maxHeight / imageInfo.height;
      const ratio = Math.min(widthRatio, heightRatio);
      
      targetWidth = Math.floor(imageInfo.width * ratio);
      targetHeight = Math.floor(imageInfo.height * ratio);
    } else {
      targetWidth = imageInfo.width;
      targetHeight = imageInfo.height;
    }
    
    // 创建canvas上下文
    const canvas = await new Promise(resolve => {
      const query = uni.createSelectorQuery();
      query.select('#uploadPreprocessCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0] && res[0].node) {
            resolve(res[0].node);
          } else {
            // 如果找不到canvas，可能是组件还未渲染完成
            console.warn('找不到预处理canvas，将使用原图');
            resolve(null);
          }
        });
    });
    
    // 如果找不到canvas，返回原图
    if (!canvas) {
      return filePath;
    }
    
    // 设置canvas尺寸
    canvas.width = targetWidth;
    canvas.height = targetHeight;
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 清空画布
    ctx.clearRect(0, 0, targetWidth, targetHeight);
    
    // 创建图片对象
    const image = canvas.createImage();
    await new Promise((resolve, reject) => {
      image.onload = resolve;
      image.onerror = reject;
      image.src = filePath;
    });
    
    // 绘制图片
    ctx.drawImage(image, 0, 0, targetWidth, targetHeight);
    
    // 导出压缩后的图片
    const result = await new Promise((resolve, reject) => {
      uni.canvasToTempFilePath({
        canvas: canvas,
        fileType: compressOptions.fileType,
        quality: compressOptions.quality,
        success: resolve,
        fail: reject
      });
    });
    
    return result.tempFilePath;
  } catch (error) {
    console.error('图片预处理失败:', error);
    // 如果压缩失败，返回原图
    return filePath;
  }
}
