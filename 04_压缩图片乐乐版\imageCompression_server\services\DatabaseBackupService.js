const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const COS = require('cos-nodejs-sdk-v5');
const moment = require('moment');
const { query } = require('../config/database');

class DatabaseBackupService {
  constructor() {
    // 初始化COS客户端
    this.cos = new COS({
      SecretId: process.env.COS_SECRET_ID || 'AKIDBiAXxIZhiYDj6OVSHvOkASqKv2OB1phU',
      SecretKey: process.env.COS_SECRET_KEY || 'f9TcgaHl4FG4g4HJAxgJl0683sX3Rm4A',
      Region: process.env.COS_REGION || 'ap-shanghai'
    });

    this.bucket = process.env.COS_BUCKET || 'imagecomp-db-backup-1253856911';
    this.backupDir = path.join(__dirname, '../backups');
    
    // 确保备份目录存在
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 执行数据库备份
   */
  async createDatabaseBackup() {
    try {
      console.log('🚀 开始数据库备份...');
      
      const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
      const backupFileName = `database_backup_${timestamp}.sql`;
      const backupFilePath = path.join(this.backupDir, backupFileName);

      // 获取数据库配置
      const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'image_compression_service',
        port: process.env.DB_PORT || 3306
      };

      // 执行mysqldump命令
      await this.executeMysqlDump(dbConfig, backupFilePath);
      
      // 验证备份文件
      const stats = fs.statSync(backupFilePath);
      if (stats.size === 0) {
        throw new Error('备份文件为空');
      }

      console.log(`✅ 数据库备份完成: ${backupFileName} (${this.formatFileSize(stats.size)})`);
      
      // 上传到COS
      await this.uploadToCOS(backupFilePath, backupFileName);
      
      // 清理本地备份文件（可选）
      await this.cleanupLocalBackups();
      
      return {
        success: true,
        fileName: backupFileName,
        fileSize: stats.size,
        timestamp: timestamp
      };

    } catch (error) {
      console.error('❌ 数据库备份失败:', error);
      throw error;
    }
  }

  /**
   * 执行mysqldump命令
   */
  async executeMysqlDump(dbConfig, outputPath) {
    return new Promise((resolve, reject) => {
      const args = [
        '--host=' + dbConfig.host,
        '--port=' + dbConfig.port,
        '--user=' + dbConfig.user,
        '--single-transaction',
        '--default-character-set=utf8mb4',
        '--skip-routines',
        '--skip-triggers',
        '--no-tablespaces',
        '--result-file=' + outputPath
      ];

      // 如果有密码，添加密码参数
      if (dbConfig.password) {
        args.push('--password=' + dbConfig.password);
      }

      // 添加数据库名
      args.push(dbConfig.database);

      console.log('📦 执行mysqldump命令...');
      
      const mysqldump = spawn('mysqldump', args, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stderr = '';

      mysqldump.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      mysqldump.on('close', (code) => {
        if (code === 0) {
          console.log('✅ mysqldump执行成功');
          resolve();
        } else {
          reject(new Error(`mysqldump执行失败，退出码: ${code}, 错误信息: ${stderr}`));
        }
      });

      mysqldump.on('error', (error) => {
        reject(new Error(`mysqldump命令执行错误: ${error.message}`));
      });
    });
  }

  /**
   * 上传备份文件到COS
   */
  async uploadToCOS(filePath, fileName) {
    try {
      console.log('☁️ 开始上传到腾讯云COS...');
      
      const cosKey = `database-backups/${moment().format('YYYY/MM')}/${fileName}`;
      
      const result = await new Promise((resolve, reject) => {
        this.cos.putObject({
          Bucket: this.bucket,
          Region: process.env.COS_REGION || 'ap-shanghai',
          Key: cosKey,
          Body: fs.createReadStream(filePath),
          ContentType: 'application/sql',
          onProgress: (progressData) => {
            const percent = Math.round(progressData.percent * 100);
            if (percent % 20 === 0) { // 每20%显示一次进度
              console.log(`📤 上传进度: ${percent}%`);
            }
          }
        }, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });

      console.log(`✅ 文件上传成功: ${cosKey}`);
      console.log(`🔗 访问地址: https://${this.bucket}.cos.${process.env.COS_REGION || 'ap-shanghai'}.myqcloud.com/${cosKey}`);
      
      return result;
    } catch (error) {
      console.error('❌ COS上传失败:', error);
      throw error;
    }
  }

  /**
   * 清理本地备份文件（保留最近3个）
   */
  async cleanupLocalBackups() {
    try {
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('database_backup_') && file.endsWith('.sql'))
        .map(file => ({
          name: file,
          path: path.join(this.backupDir, file),
          mtime: fs.statSync(path.join(this.backupDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      // 保留最近的3个备份文件
      const filesToDelete = files.slice(3);
      
      for (const file of filesToDelete) {
        fs.unlinkSync(file.path);
        console.log(`🗑️ 删除旧备份文件: ${file.name}`);
      }

      if (filesToDelete.length > 0) {
        console.log(`✅ 清理完成，删除了 ${filesToDelete.length} 个旧备份文件`);
      }
    } catch (error) {
      console.error('⚠️ 清理本地备份文件时出错:', error);
    }
  }

  /**
   * 获取备份统计信息
   */
  async getBackupStats() {
    try {
      // 获取数据库大小
      const [dbSizeResult] = await query(`
        SELECT 
          ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `);

      // 获取表数量
      const [tableCountResult] = await query(`
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `);

      return {
        databaseSize: dbSizeResult.db_size_mb || 0,
        tableCount: tableCountResult.table_count || 0,
        lastBackupTime: moment().format('YYYY-MM-DD HH:mm:ss')
      };
    } catch (error) {
      console.error('获取备份统计信息失败:', error);
      return null;
    }
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 测试COS连接
   */
  async testCOSConnection() {
    try {
      console.log('🔍 测试COS连接...');
      
      const result = await new Promise((resolve, reject) => {
        this.cos.headBucket({
          Bucket: this.bucket,
          Region: process.env.COS_REGION || 'ap-shanghai'
        }, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });

      console.log('✅ COS连接测试成功');
      return true;
    } catch (error) {
      console.error('❌ COS连接测试失败:', error);
      return false;
    }
  }
}

module.exports = DatabaseBackupService;
