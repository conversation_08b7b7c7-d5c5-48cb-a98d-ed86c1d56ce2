# 微信内容安全检测实现指南

这是一个用于处理微信小程序内容安全检测的完整实现指南，帮助小程序开发者满足微信平台的内容安全要求。特别适用于图片压缩类小程序，可以检测用户上传的图片是否包含违规内容。

## 功能

- 接收微信服务器的验证请求
- 提供内容安全检测API接口给小程序调用
- 接收微信内容安全检测的回调结果
- 支持多个微信小程序同时使用
- 自动管理access_token
- 支持图片、音频、视频等多种媒体类型的内容检测
- 支持获取用户OpenID进行内容安全检测
- 在压缩操作前拦截违规内容

## 内容安全检测详细流程

为了帮助您更好地理解内容安全检测的工作流程，以下是详细的步骤说明：

### 1. 服务端配置

1. **配置微信公众平台**：
   - 登录微信公众平台
   - 进入"开发"->"开发设置"->"消息推送"
   - 设置服务器地址为 `https://media-check.yourdomain.com/wechat`
   - 设置Token为您在配置文件中设置的 `token` 值
   - 设置消息加密方式为"明文模式"
   - 设置数据格式为"XML"
   - 点击"提交"按钮

2. **服务器代码配置**：
   - 确保 `app.js` 中已添加 XML 解析支持：
     ```javascript
     const xmlparser = require('express-xml-bodyparser');
     app.use(xmlparser()); // 解析XML请求体，用于微信消息
     ```
   - 确保 `package.json` 中已添加 `express-xml-bodyparser` 依赖：
     ```json
     "dependencies": {
       "express-xml-bodyparser": "^0.3.0"
     }
     ```
   - 确保 `routes/wechat.js` 中已实现对 `wxa_media_check` 事件的处理：
     ```javascript
     router.post('/', (req, res) => {
       try {
         const xml = req.body.xml || {};
         const event = xml.event && xml.event[0];

         if (event === 'wxa_media_check') {
           const traceId = xml.trace_id && xml.trace_id[0];
           const result = xml.result && xml.result[0];
           const suggest = result && result.suggest && result.suggest[0];
           const label = result && result.label && result.label[0];

           console.log(`内容检测结果: trace_id=${traceId}, suggest=${suggest}, label=${label}`);

           if (suggest === 'risky') {
             console.log(`发现违规内容: trace_id=${traceId}, label=${label}`);
           }
         }

         res.send('success');
       } catch (error) {
         console.error('处理微信消息出错:', error);
         res.send('success');
       }
     });
     ```

### 2. 小程序端实现

1. **上传图片时调用内容安全检测并启动轮询**：
   ```javascript
   // 选择图片后
   const filePath = res.tempFilePaths[0];

   // 设置图片路径并获取信息
   this.tempImagePath = filePath;
   await this.getOriginalImageInfo(this.tempImagePath);

   // 在后台进行内容安全检测（完全静默，不影响用户体验）
   Promise.resolve().then(async () => {
     try {
       // 调用内容安全检测API
       const checkResult = await checkImageSecurity(filePath);

       // 处理检测结果
       handleCheckResult(checkResult);

       // 启动轮询检测结果
       // 注意：轮询已在checkImageSecurity内部启动，这里不需要额外操作

       // 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果
     } catch (error) {
       // 只在控制台记录错误，不影响用户体验
       console.error('内容安全检测失败:', error);
     }
   });
   ```

2. **压缩前检查图片是否违规（改进版）**：
   ```javascript
   // 压缩按钮点击事件
   async handleCompressClick() {
     try {
       // 检查图片是否违规
       if (this.tempImagePath) {
         // 查询最新的检测结果
         const checkResult = await isImageRisky(this.tempImagePath, true);

         // 如果检测中，提示用户等待
         if (checkResult.isChecking) {
           uni.showModal({
             title: '提示',
             content: checkResult.message,
             showCancel: false
           });
           return;
         }

         // 如果违规，拦截压缩
         if (checkResult.isRisky) {
           uni.showModal({
             title: '提示',
             content: checkResult.message,
             showCancel: false
           });
           return;
         }
       }

       // 继续正常的压缩流程...
     } catch (error) {
       // 错误处理...
     }
   }
   ```

### 3. 内容安全检测结果存储

1. **记录检测请求**：
   ```javascript
   export function recordCheckRequest(filePath, traceId) {
     securityResults.set(filePath, {
       traceId: traceId,
       status: 'checking', // checking, pass, risky
       timestamp: Date.now()
     });
     console.log(`[安全检测存储] 记录检测请求: ${filePath} -> ${traceId}`);
     console.log(`[安全检测存储] 当前存储的检测结果数量: ${securityResults.size}`);
   }
   ```

2. **更新检测结果**：
   ```javascript
   export function updateCheckResult(traceId, status, label) {
     console.log(`[安全检测存储] 收到检测结果更新: trace_id=${traceId}, status=${status}, label=${label}`);

     // 查找对应的文件路径
     for (const [filePath, result] of securityResults.entries()) {
       if (result.traceId === traceId) {
         result.status = status;
         result.label = label;
         result.updateTime = Date.now();
         console.log(`[安全检测存储] 成功更新检测结果: ${filePath} -> ${status}, 标签: ${label}`);
         return true;
       }
     }

     console.warn(`[安全检测存储] 未找到对应的检测请求: ${traceId}`);
     return false;
   }
   ```

3. **查询检测结果**：
   ```javascript
   export function getCheckResult(filePath) {
     const result = securityResults.get(filePath) || null;

     if (result) {
       console.log(`[安全检测存储] 找到图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);
     } else {
       console.log(`[安全检测存储] 未找到图片 ${filePath} 的检测结果`);
     }

     return result;
   }
   ```

4. **检查图片是否违规（改进版）**：
   ```javascript
   /**
    * 检查图片是否违规
    * @param {string} filePath 图片路径
    * @param {boolean} checkLatest 是否查询最新结果
    * @returns {Promise<object>} 检测结果对象 {isRisky, isChecking, message}
    */
   export async function isImageRisky(filePath, checkLatest = false) {
     console.log(`[安全检测] 正在查询图片 ${filePath} 的检测结果`);

     // 获取本地存储的检测结果
     let result = getCheckResult(filePath);

     // 如果需要查询最新结果
     if (checkLatest && result && result.traceId) {
       try {
         console.log(`[安全检测] 查询最新检测结果: ${result.traceId}`);

         // 发起查询请求
         const res = await uni.request({
           url: 'https://media-check.gbw8848.cn/api/check-result',
           method: 'POST',
           data: { trace_id: result.traceId }
         });

         // 处理响应
         if (res.statusCode === 200 && res.data.code === 0) {
           const { status, label } = res.data.data;

           // 如果检测完成，更新本地状态
           if (status !== 'checking') {
             updateCheckResult(result.traceId, status, label);

             // 重新获取更新后的结果
             result = getCheckResult(filePath);
           }
         }
       } catch (error) {
         console.error('[安全检测] 查询最新结果失败:', error);
         // 出错时继续使用本地结果
       }
     }

     // 未找到检测结果
     if (!result) {
       console.log(`[安全检测] 未找到图片 ${filePath} 的检测结果，可能尚未完成检测`);
       return {
         isRisky: false,
         isChecking: false,
         message: '未找到检测结果'
       };
     }

     console.log(`[安全检测] 图片 ${filePath} 的检测结果: status=${result.status}, traceId=${result.traceId}`);

     // 检测中
     if (result.status === 'checking') {
       console.log(`[安全检测] 图片 ${filePath} 正在检测中，请稍候`);
       return {
         isRisky: false,
         isChecking: true,
         message: '内容安全检测尚未完成，请稍等片刻'
       };
     }

     // 违规内容
     if (result.status === 'risky') {
       console.log(`[安全检测] 图片 ${filePath} 被检测为违规内容，将拦截压缩`);
       return {
         isRisky: true,
         isChecking: false,
         message: '当前图片无法处理，请尝试其他图片'
       };
     }

     // 检测通过
     console.log(`[安全检测] 图片 ${filePath} 检测通过，允许压缩`);
     return {
       isRisky: false,
       isChecking: false,
       message: '检测通过'
     };
   }
   ```

5. **清理过期结果**：
   ```javascript
   export function cleanupResults(maxAge = 3600000) { // 默认1小时
     const now = Date.now();
     let cleanedCount = 0;

     for (const [filePath, result] of securityResults.entries()) {
       if (now - result.timestamp > maxAge) {
         securityResults.delete(filePath);
         cleanedCount++;
       }
     }

     if (cleanedCount > 0) {
       console.log(`[安全检测存储] 清理了 ${cleanedCount} 条过期检测结果，当前剩余 ${securityResults.size} 条`);
     }
   }

   // 每小时清理一次过期结果
   setInterval(cleanupResults, 3600000);
   ```
