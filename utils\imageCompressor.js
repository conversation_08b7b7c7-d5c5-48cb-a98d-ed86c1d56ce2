/**
 * 图片压缩工具
 * 用于压缩用户上传的图片，减少存储和传输大小
 */

const performanceMonitor = require('./performance/performanceMonitor');

// 获取错误上报器（延迟加载避免循环依赖）
function getErrorReporter() {
  try {
    return require('./error/errorReporter');
  } catch (e) {
    console.warn('错误上报器未找到:', e);
    return null;
  }
}

class ImageCompressor {
  constructor() {
    // 默认压缩配置
    this.defaultConfig = {
      quality: 0.8,        // 压缩质量 (0.1-1.0)
      maxWidth: 400,       // 最大宽度
      maxHeight: 600,      // 最大高度
      format: 'jpeg',      // 输出格式
      enableResize: true,  // 是否启用尺寸压缩
      enableQuality: true  // 是否启用质量压缩
    };
  }

  /**
   * 压缩图片
   * @param {string} filePath - 图片文件路径
   * @param {Object} options - 压缩选项
   * @returns {Promise<string>} - 压缩后的base64字符串
   */
  compressImage(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const config = { ...this.defaultConfig, ...options };

        // 记录压缩开始时间
        const startTime = Date.now();

        // 获取图片信息
        wx.getImageInfo({
          src: filePath,
          success: (imageInfo) => {
            console.log('原始图片信息:', {
              width: imageInfo.width,
              height: imageInfo.height,
              path: imageInfo.path
            });

            // 计算压缩后的尺寸
            const { width, height } = this.calculateCompressedSize(
              imageInfo.width,
              imageInfo.height,
              config.maxWidth,
              config.maxHeight
            );

            console.log('压缩后尺寸:', { width, height });

            // 尝试Canvas压缩，如果失败则使用简单压缩
            this.compressWithCanvas(filePath, width, height, config)
              .then((compressedBase64) => {
                const endTime = Date.now();

                // 计算压缩效果
                const originalSize = this.estimateOriginalSize(imageInfo.width, imageInfo.height);
                const compressedSize = this.getBase64Size(compressedBase64);
                const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

                console.log('图片压缩完成:', {
                  原始尺寸: `${imageInfo.width}x${imageInfo.height}`,
                  压缩尺寸: `${width}x${height}`,
                  预估原始大小: this.formatSize(originalSize),
                  压缩后大小: this.formatSize(compressedSize),
                  压缩率: `${compressionRatio}%`,
                  耗时: `${endTime - startTime}ms`
                });

                // 记录到性能监控
                performanceMonitor.recordImageCompression(
                  originalSize,
                  compressedSize,
                  endTime - startTime
                );

                resolve(compressedBase64);
              })
              .catch((error) => {
                console.warn('Canvas压缩失败，使用简单压缩方法:', error);

                // 使用简单压缩方法
                this.simpleCompress(filePath, config)
                  .then((compressedBase64) => {
                    const endTime = Date.now();

                    // 计算压缩效果
                    const originalSize = this.estimateOriginalSize(imageInfo.width, imageInfo.height);
                    const compressedSize = this.getBase64Size(compressedBase64);
                    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

                    console.log('简单压缩完成:', {
                      原始尺寸: `${imageInfo.width}x${imageInfo.height}`,
                      预估原始大小: this.formatSize(originalSize),
                      压缩后大小: this.formatSize(compressedSize),
                      压缩率: `${compressionRatio}%`,
                      耗时: `${endTime - startTime}ms`
                    });

                    // 记录到性能监控
                    performanceMonitor.recordImageCompression(
                      originalSize,
                      compressedSize,
                      endTime - startTime
                    );

                    resolve(compressedBase64);
                  })
                  .catch(reject);
              });
          },
          fail: (error) => {
            console.error('获取图片信息失败:', error);
            reject(error);
          }
        });
      } catch (error) {
        console.error('图片压缩过程出错:', error);

        // 上报图片压缩失败错误
        const errorReporter = getErrorReporter();
        if (errorReporter) {
          errorReporter.reportManualError('image_compression_error', error, {
            module: 'ImageCompressor',
            action: 'compressImage',
            file_path: filePath,
            config: options
          });
        }

        reject(error);
      }
    });
  }

  /**
   * 使用Canvas压缩图片
   * @param {string} filePath - 图片路径
   * @param {number} width - 目标宽度
   * @param {number} height - 目标高度
   * @param {Object} config - 压缩配置
   * @returns {Promise<string>} - 压缩后的base64
   */
  compressWithCanvas(filePath, width, height, config) {
    return new Promise((resolve, reject) => {
      try {
        console.log('开始Canvas压缩:', { filePath, width, height, config });

        // 创建canvas上下文
        const canvas = wx.createCanvasContext('compressCanvas');

        // 清空canvas
        canvas.clearRect(0, 0, width, height);

        // 绘制图片到canvas
        canvas.drawImage(filePath, 0, 0, width, height);

        // 绘制完成后导出
        canvas.draw(false, () => {
          console.log('Canvas绘制完成，开始导出...');

          // 导出为临时文件
          wx.canvasToTempFilePath({
            canvasId: 'compressCanvas',
            x: 0,
            y: 0,
            width: width,
            height: height,
            destWidth: width,
            destHeight: height,
            quality: config.quality,
            fileType: config.format,
            success: (res) => {
              console.log('Canvas导出成功:', res.tempFilePath);

              // 将压缩后的文件转为base64
              this.fileToBase64(res.tempFilePath, config.format)
                .then((base64) => {
                  console.log('Base64转换成功，长度:', base64.length);
                  resolve(base64);
                })
                .catch((error) => {
                  console.error('Base64转换失败:', error);
                  reject(error);
                });
            },
            fail: (error) => {
              console.error('Canvas导出失败:', error);

              // 上报Canvas导出失败错误
              const errorReporter = getErrorReporter();
              if (errorReporter) {
                errorReporter.reportManualError('canvas_export_error', error, {
                  module: 'ImageCompressor',
                  action: 'compressWithCanvas',
                  canvas_id: 'compressCanvas',
                  target_size: `${width}x${height}`,
                  quality: config.quality
                });
              }

              // 如果Canvas导出失败，尝试直接转换原文件
              this.fileToBase64(filePath, config.format)
                .then(resolve)
                .catch(reject);
            }
          });
        });
      } catch (error) {
        console.error('Canvas压缩失败:', error);
        // 如果Canvas压缩失败，尝试直接转换原文件
        this.fileToBase64(filePath, config.format)
          .then(resolve)
          .catch(reject);
      }
    });
  }

  /**
   * 简单压缩方法（不使用Canvas）
   * @param {string} filePath - 图片路径
   * @param {Object} config - 压缩配置
   * @returns {Promise<string>} - 压缩后的base64
   */
  simpleCompress(filePath, config) {
    return new Promise((resolve, reject) => {
      try {
        console.log('使用简单压缩方法:', { filePath, config });

        // 直接转换为base64，不进行Canvas压缩
        this.fileToBase64(filePath, config.format)
          .then((base64) => {
            console.log('简单压缩完成，Base64长度:', base64.length);
            resolve(base64);
          })
          .catch(reject);
      } catch (error) {
        console.error('简单压缩失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 文件转base64
   * @param {string} filePath - 文件路径
   * @param {string} format - 图片格式
   * @returns {Promise<string>} - base64字符串
   */
  fileToBase64(filePath, format = 'jpeg') {
    return new Promise((resolve, reject) => {
      const mimeType = `image/${format}`;

      wx.getFileSystemManager().readFile({
        filePath,
        encoding: 'base64',
        success: (res) => {
          const base64 = `data:${mimeType};base64,${res.data}`;
          resolve(base64);
        },
        fail: (error) => {
          console.error('文件转base64失败:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * 计算压缩后的尺寸
   * @param {number} originalWidth - 原始宽度
   * @param {number} originalHeight - 原始高度
   * @param {number} maxWidth - 最大宽度
   * @param {number} maxHeight - 最大高度
   * @returns {Object} - 压缩后的尺寸
   */
  calculateCompressedSize(originalWidth, originalHeight, maxWidth, maxHeight) {
    let { width, height } = { width: originalWidth, height: originalHeight };

    // 如果图片尺寸超过限制，按比例缩放
    if (width > maxWidth || height > maxHeight) {
      const widthRatio = maxWidth / width;
      const heightRatio = maxHeight / height;
      const ratio = Math.min(widthRatio, heightRatio);

      width = Math.floor(width * ratio);
      height = Math.floor(height * ratio);
    }

    return { width, height };
  }

  /**
   * 预估原始图片大小
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @returns {number} - 预估大小（字节）
   */
  estimateOriginalSize(width, height) {
    // 假设每像素3字节（RGB），再加上base64编码增加的33%
    return Math.floor(width * height * 3 * 1.33);
  }

  /**
   * 获取base64字符串的大小
   * @param {string} base64 - base64字符串
   * @returns {number} - 大小（字节）
   */
  getBase64Size(base64) {
    // 移除data:image/...;base64,前缀
    const base64Data = base64.split(',')[1] || base64;
    // base64每4个字符代表3个字节
    return Math.floor(base64Data.length * 3 / 4);
  }

  /**
   * 格式化文件大小显示
   * @param {number} bytes - 字节数
   * @returns {string} - 格式化后的大小
   */
  formatSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / (1024 * 1024)) + ' MB';
  }

  /**
   * 获取压缩配置建议
   * @param {number} originalWidth - 原始宽度
   * @param {number} originalHeight - 原始高度
   * @returns {Object} - 建议的压缩配置
   */
  getCompressionSuggestion(originalWidth, originalHeight) {
    const pixelCount = originalWidth * originalHeight;

    if (pixelCount > 2000000) { // 超过200万像素
      return {
        quality: 0.6,
        maxWidth: 300,
        maxHeight: 450,
        reason: '超高分辨率图片，建议强压缩'
      };
    } else if (pixelCount > 1000000) { // 超过100万像素
      return {
        quality: 0.7,
        maxWidth: 350,
        maxHeight: 525,
        reason: '高分辨率图片，建议中等压缩'
      };
    } else {
      return {
        quality: 0.8,
        maxWidth: 400,
        maxHeight: 600,
        reason: '普通分辨率图片，建议轻度压缩'
      };
    }
  }
}

// 创建全局实例
const imageCompressor = new ImageCompressor();

module.exports = imageCompressor;
