# 图像压缩小程序后端服务

## 🎯 项目概述

为图像压缩小程序提供完整的后端支持，包括用户管理、会员系统、订单处理和多商户支持。支持5套微信小程序共用一个后端服务。

## 💰 商业模式

### 免费模式
- **图像压缩**: 前端本地处理，每日仅1次免费
- **用完后**: 无任何替代方案，必须付费才能继续使用

### 付费方案（唯一选择）
- **日卡会员**: ¥4.99 - 24小时内无限制压缩
- **永久会员**: ¥19.99 - 永久无限制压缩

## 🏗️ 技术架构

- **后端框架**: Node.js + Express.js
- **数据库**: MySQL 8.0.36
- **认证方式**: 微信 openid + AppID
- **定时任务**: node-cron
- **部署方式**: PM2 + 宝塔面板

## 📊 数据库表结构

### 核心表
- `merchants` - 商户配置表
- `users` - 用户信息表
- `products` - 商品配置表
- `memberships` - 用户会员表
- `orders` - 订单表

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和微信参数
```

### 3. 初始化数据库
```bash
# 在MySQL中执行 database.sql 文件
mysql -u root -p < database.sql
```

### 4. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## 📋 API 接口

### 基础信息
- **服务器端口**: 8851
- **API 基础路径**: `/api`
- **健康检查**: `/health`

### 主要接口

#### 用户相关 (`/api/user`)
- `POST /login` - 用户登录/注册
- `GET /status` - 获取用户状态（主要是会员状态）
- `POST /check-membership` - 检查会员权限（替代广告功能）
- `GET /stats/:userId` - 获取用户统计

#### 会员相关 (`/api/membership`)
- `GET /products` - 获取商品列表
- `GET /status` - 获取会员状态
- `GET /history` - 获取会员历史
- `POST /check-permission` - 检查会员权限（用于替代广告逻辑）

#### 订单相关 (`/api/order`)
- `POST /create` - 创建订单
- `GET /list` - 查询订单列表
- `GET /:orderId` - 查询单个订单
- `POST /pay-success` - 支付成功回调

#### 商户相关 (`/api/merchant`)
- `GET /info/:appId` - 获取商户信息
- `GET /stats/:merchantId` - 获取商户统计
- `GET /list` - 获取商户列表
- `GET /check/:appId` - 检查AppID支持
- `GET /:merchantId/products` - 获取商户商品
- `GET /system/overview` - 系统概览统计

## 🕐 定时任务

### 自动任务
- **每小时30分**: 更新过期的会员状态
- **每小时**: 清理过期订单
- **每周日3点**: 清理90天前的旧数据

### 手动任务
可通过代码调用以下方法：
- `cronJobs.manualCleanTempData()` - 手动清理临时数据
- `cronJobs.manualUpdateMembershipStatus()` - 手动更新会员状态
- `cronJobs.manualCleanExpiredOrders()` - 手动清理过期订单
- `cronJobs.manualCleanOldData()` - 手动清理旧数据

## 🔧 多商户配置

### 添加新商户
1. 在 `merchants` 表中添加商户记录
2. 在 `products` 表中配置该商户的商品价格
3. 配置微信支付参数

### 示例配置
```sql
-- 添加商户
INSERT INTO merchants (merchant_id, app_id, business_name, wechat_app_id, wechat_app_secret, wechat_mch_id, wechat_api_key, is_active) 
VALUES ('merchant_001', 'wx123456789', '商户A', 'wx123456789', 'secret123', 'mch123', 'apikey123', TRUE);

-- 添加商品
INSERT INTO products (merchant_id, product_code, product_name, price, duration_hours, sort_order) VALUES
('merchant_001', 'day_card', '日卡会员', 4.99, 24, 1),
('merchant_001', 'permanent', '永久会员', 19.99, NULL, 2);
```

## 🔒 安全特性

- 基于 AppID 的多商户隔离
- 用户数据按商户分离
- 订单防重复提交
- 会员权限实时验证
- 免费次数原子性操作

## 📈 监控指标

### 业务指标
- 用户注册数和活跃度
- 订单转化率和收入
- 会员购买和续费情况
- 免费次数使用情况

### 技术指标
- API 响应时间
- 数据库连接状态
- 定时任务执行状态
- 系统资源使用情况

## 🚀 部署说明

### 宝塔面板部署
1. 上传项目文件到服务器
2. 安装 Node.js 环境
3. 配置 PM2 进程管理
4. 设置 Nginx 反向代理
5. 配置 SSL 证书

### PM2 配置示例
```javascript
module.exports = {
  apps: [{
    name: 'image-compression-server',
    script: 'app.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 8851
    }
  }]
};
```

## 📞 技术支持

如有问题，请检查：
1. 数据库连接配置
2. 环境变量设置
3. 微信支付配置
4. 端口占用情况

## 📄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持用户管理和会员系统
- 支持多商户配置
- 支持订单处理和支付回调
- 支持定时任务和数据清理
