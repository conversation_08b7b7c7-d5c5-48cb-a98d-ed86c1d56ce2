// pages/user/center/center.js
Page({
  data: {
    statusBarHeight: 0,
    navHeight: 0,
    // 会员信息
    memberInfo: {
      type: 'normal', // normal: 普通用户, day: 日卡会员, permanent: 永久会员
      expireDate: null, // 到期时间，永久会员为null
      isExpired: false // 是否已过期
    },
    // 显示文本
    memberTypeText: '普通用户',
    memberExpireText: '未开通会员',
    memberButtonText: '开通会员'
  },

  onLoad() {
    // 获取状态栏高度
    const windowInfo = wx.getWindowInfo()
    this.setData({
      statusBarHeight: windowInfo.statusBarHeight,
      // 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
      navHeight: windowInfo.statusBarHeight + 44
    })

    // 更新显示文本
    this.updateDisplayTexts()
  },

  onShow() {
    // 页面显示时可以刷新数据
    this.updateDisplayTexts()
  },

  // 更新显示文本
  updateDisplayTexts() {
    this.setData({
      memberTypeText: this.getMemberTypeText(),
      memberExpireText: this.getMemberExpireText(),
      memberButtonText: this.getMemberButtonText()
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 获取会员类型显示文本
  getMemberTypeText() {
    switch (this.data.memberInfo.type) {
      case 'day':
        return '日卡会员'
      case 'permanent':
        return '永久会员'
      default:
        return '普通用户'
    }
  },

  // 获取会员到期时间显示文本
  getMemberExpireText() {
    if (this.data.memberInfo.type === 'permanent') {
      return '永久有效'
    } else if (this.data.memberInfo.type === 'day') {
      if (this.data.memberInfo.expireTime) {
        // 显示精确的过期时间
        const expireTime = new Date(this.data.memberInfo.expireTime)
        const year = expireTime.getFullYear()
        const month = String(expireTime.getMonth() + 1).padStart(2, '0')
        const day = String(expireTime.getDate()).padStart(2, '0')
        const hour = String(expireTime.getHours()).padStart(2, '0')
        const minute = String(expireTime.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day} ${hour}:${minute}`
      } else if (this.data.memberInfo.expireDate) {
        // 兼容旧数据
        return this.data.memberInfo.expireDate
      }
    }
    return '未开通会员'
  },

  // 获取会员按钮文本
  getMemberButtonText() {
    // 根据用户会员状态返回不同的按钮文本
    if (this.data.memberInfo.type === 'normal') {
      return '开通会员'
    } else {
      return '续费会员'
    }
  },

  // 处理续费会员按钮点击
  handleRenewMember() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 处理菜单点击
  handleMenuClick(e) {
    const type = e.currentTarget.dataset.type

    if (type === 'order') {
      // 跳转到订单查询页面
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
      return
    }

    if (type === 'feedback') {
      // 联系我们 - 询问是否复制客服微信号
      wx.showModal({
        title: '客服微信',
        content: '客服微信号：gbw6646\n是否复制到剪贴板？',
        confirmText: '复制',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户点击复制
            wx.setClipboardData({
              data: 'gbw6646',
              success: () => {
                wx.showToast({
                  title: '已复制到剪贴板',
                  icon: 'success',
                  duration: 2000
                })
              }
            })
          }
        }
      })
      return
    }

    if (type === 'about') {
      // 关于我们 - 显示应用信息
      wx.showModal({
        title: '关于我们',
        content: `个人简历模板简历制作 v1.0

📄 专业简历制作工具

✨ 多种精美简历模板
🎨 自定义主题配色
📸 智能证件照制作
💼 助您求职成功

谢谢使用，祝您工作顺利！`,
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }
  }
})
