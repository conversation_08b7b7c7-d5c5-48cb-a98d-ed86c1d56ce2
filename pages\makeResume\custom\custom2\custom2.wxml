<view class="container2">
  <!-- 顶部标题已经在导航栏显示 -->

  <!-- 名称输入区域 -->
  <view class="formItem2">
    <view class="label2 required">自定义</view>
    <input class="input2"
           placeholder="请输入"
           value="{{custom2FormData.customName}}"
           bindinput="handleNameInput"/>
  </view>

  <!-- 时间选择区域 -->
  <view class="formItem2">
    <view class="label2">时间</view>
    <view class="datePicker2">
      <view class="dateSection2">
        <picker mode="date" fields="month" value="{{custom2FormData.startDate}}" bindchange="handleStartDateChange">
          <view class="picker2 {{custom2FormData.startDate ? '' : 'placeholder2'}}">
            {{custom2FormData.startDate || '请选择'}}
          </view>
        </picker>
      </view>
      <text class="separator2">至</text>
      <view class="dateSection2">
        <picker mode="date" fields="month" value="{{custom2FormData.endDate === '至今' ? currentDate : custom2FormData.endDate}}" bindchange="handleEndDateChange">
          <view class="picker2 {{custom2FormData.endDate ? '' : 'placeholder2'}}">
            {{custom2FormData.endDate || '请选择'}}
          </view>
        </picker>
      </view>
      <view class="nowBtn2" bindtap="setToNow">至今</view>
    </view>
  </view>

  <!-- 角色输入区域 -->
  <view class="formItem2">
    <view class="label2">角色</view>
    <input class="input2"
           placeholder="请输入(选填)"
           value="{{custom2FormData.role}}"
           bindinput="handleRoleInput"/>
  </view>

  <!-- 自定义内容区域 -->
  <view class="formItem2 contentArea2">
    <view class="label2">自定义内容</view>
    <textarea class="contentInput2"
              placeholder="请输入自定义的详细内容"
              value="{{custom2FormData.content}}"
              bindinput="handleContentInput"
              maxlength="-1"
              auto-height/>

  </view>

  <!-- 底部按钮组 -->
  <view class="buttonGroup2">
    <button class="saveBtn2" bindtap="saveContent">保存信息</button>
    <button class="deleteBtn2" bindtap="deleteContent">删除</button>
  </view>
</view>