const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const AppManager = require('../services/MerchantManager');

/**
 * 获取用户会员信息
 * GET /api/membership/info?app_id=xxx&openid=xxx
 */
router.get('/info', async (req, res) => {
  try {
    const { app_id, openid } = req.query;

    if (!app_id || !openid) {
      return res.status(400).json({
        success: false,
        error: 'app_id和openid不能为空'
      });
    }

    // 查询用户会员信息
    const membershipResult = await query(
      `SELECT
        m.membership_id,
        m.membership_type,
        m.start_time,
        m.expire_time,
        m.status,
        m.created_at,
        u.user_id,
        u.nickname,
        u.avatar_url
      FROM memberships m
      LEFT JOIN users u ON m.user_id = u.user_id
      WHERE u.app_id = ? AND u.openid = ? AND m.status = 'active'
      ORDER BY m.created_at DESC
      LIMIT 1`,
      [app_id, openid]
    );

    if (membershipResult.length === 0) {
      // 用户没有会员信息，返回默认状态
      return res.json({
        success: true,
        data: {
          has_membership: false,
          membership_type: null,
          start_time: null,
          expire_time: null,
          is_expired: true,
          remaining_days: 0
        }
      });
    }

    const membership = membershipResult[0];
    const now = new Date();
    const expireTime = membership.expire_time ? new Date(membership.expire_time) : null;
    const isExpired = membership.membership_type !== 'permanent' && expireTime && expireTime < now;

    let remainingDays = 0;
    if (membership.membership_type !== 'permanent' && !isExpired && expireTime) {
      remainingDays = Math.ceil((expireTime - now) / (1000 * 60 * 60 * 24));
    } else if (membership.membership_type === 'permanent') {
      remainingDays = -1; // 永久会员用-1表示
    }

    res.json({
      success: true,
      data: {
        has_membership: !isExpired,
        membership_id: membership.membership_id,
        membership_type: membership.membership_type,
        start_time: membership.start_time,
        expire_time: membership.expire_time,
        is_expired: isExpired,
        remaining_days: remainingDays,
        user_info: {
          user_id: membership.user_id,
          nickname: membership.nickname,
          avatar_url: membership.avatar_url
        }
      }
    });

  } catch (error) {
    console.error('获取会员信息失败:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
});

/**
 * 获取商品列表（根据AppID）
 */
router.get('/products', async (req, res) => {
  try {
    const { appId } = req.query;

    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'appId不能为空'
      });
    }

    // 使用小程序管理器获取商品列表（从数据库）
    const products = await AppManager.getProductsByAppId(appId);

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        error: '未找到对应的小程序配置或商品'
      });
    }

    // 获取小程序信息
    const appConfig = AppManager.getAppByAppId(appId);
    const mchid = appConfig ? appConfig.mchid : 'unknown';

    res.json({
      success: true,
      data: {
        appid: appId,
        appName: appConfig ? appConfig.appName : 'Unknown',
        businessName: appConfig ? appConfig.businessName : 'Unknown',
        mchid: mchid,
        products: products
      }
    });

  } catch (error) {
    console.error('获取商品列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户会员状态
 */
router.get('/status', async (req, res) => {
  try {
    const { openid, appId } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户ID
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userId = user[0].user_id;

    // 获取当前有效的会员信息
    const membership = await query(
      `SELECT * FROM memberships 
       WHERE user_id = ? AND status = 'active' 
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userId]
    );

    if (membership.length === 0) {
      return res.json({
        success: true,
        data: {
          isActive: false,
          type: null,
          startTime: null,
          expireTime: null,
          remainingTime: null
        }
      });
    }

    const memberData = membership[0];
    let remainingTime = null;

    // 计算剩余时间（仅对日卡会员）
    if (memberData.membership_type === 'day_card' && memberData.expire_time) {
      const now = new Date();
      const expireTime = new Date(memberData.expire_time);
      remainingTime = Math.max(0, Math.floor((expireTime - now) / 1000)); // 剩余秒数
    }

    res.json({
      success: true,
      data: {
        isActive: true,
        type: memberData.membership_type,
        startTime: memberData.start_time,
        expireTime: memberData.expire_time,
        remainingTime,
        isPermanent: memberData.membership_type === 'permanent'
      }
    });

  } catch (error) {
    console.error('获取会员状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户会员历史
 */
router.get('/history', async (req, res) => {
  try {
    const { openid, appId, page = 1, limit = 10 } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户ID
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userId = user[0].user_id;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 获取会员历史记录
    const memberships = await query(
      `SELECT m.*, o.amount, o.transaction_id 
       FROM memberships m
       LEFT JOIN orders o ON m.order_id = o.order_id
       WHERE m.user_id = ? 
       ORDER BY m.created_at DESC 
       LIMIT ? OFFSET ?`,
      [userId, parseInt(limit), parseInt(offset)]
    );

    // 获取总数
    const totalResult = await query(
      'SELECT COUNT(*) as total FROM memberships WHERE user_id = ?',
      [userId]
    );

    res.json({
      success: true,
      data: {
        memberships: memberships.map(m => ({
          membershipId: m.membership_id,
          type: m.membership_type,
          startTime: m.start_time,
          expireTime: m.expire_time,
          status: m.status,
          orderId: m.order_id,
          amount: m.amount ? parseFloat(m.amount) : null,
          transactionId: m.transaction_id,
          createdAt: m.created_at
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          totalPages: Math.ceil(totalResult[0].total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('获取会员历史失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 检查会员权限
 */
router.post('/check-permission', async (req, res) => {
  try {
    const { openid, appId } = req.body;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户信息
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userData = user[0];

    // 检查会员状态
    const membership = await query(
      `SELECT * FROM memberships
       WHERE user_id = ? AND status = 'active'
       AND (expire_time IS NULL OR expire_time > NOW())
       LIMIT 1`,
      [userData.user_id]
    );

    const hasActiveMembership = membership.length > 0;

    res.json({
      success: true,
      data: {
        hasActiveMembership,
        membershipType: hasActiveMembership ? membership[0].membership_type : null,
        expireTime: hasActiveMembership ? membership[0].expire_time : null,
        shouldShowMembershipOffer: !hasActiveMembership
      }
    });

  } catch (error) {
    console.error('检查会员权限失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
