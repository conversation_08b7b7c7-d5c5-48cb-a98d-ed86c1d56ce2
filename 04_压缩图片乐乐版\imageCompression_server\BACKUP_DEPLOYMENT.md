# 数据库备份系统部署指南

## 📋 概述

本系统实现了自动数据库备份功能，每天凌晨3点自动将MySQL数据库备份到腾讯云COS存储桶中。

## 🚀 部署步骤

### 1. 安装依赖

在宝塔面板的终端中执行：

```bash
cd /www/wwwroot/your-project-path/imageCompression_server
npm install cos-nodejs-sdk-v5
```

### 2. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 腾讯云COS配置（数据库备份）
COS_SECRET_ID=AKIDBiAXxIZhiYDj6OVSHvOkASqKv2OB1phU
COS_SECRET_KEY=f9TcgaHl4FG4g4HJAxgJl0683sX3Rm4A
COS_REGION=ap-shanghai
COS_BUCKET=imagecomp-db-backup-1253856911
```

### 3. 确保mysqldump可用

检查服务器是否安装了mysqldump：

```bash
which mysqldump
mysqldump --version
```

如果没有安装，在CentOS/RHEL上执行：
```bash
yum install mysql
```

在Ubuntu/Debian上执行：
```bash
apt-get install mysql-client
```

### 4. 创建备份目录

```bash
mkdir -p /www/wwwroot/your-project-path/imageCompression_server/backups
chmod 755 /www/wwwroot/your-project-path/imageCompression_server/backups
```

### 5. 测试备份系统

```bash
cd /www/wwwroot/your-project-path/imageCompression_server
node scripts/setup-backup.js
```

### 6. 重启应用

在宝塔面板中重启Node.js应用，或使用PM2：

```bash
pm2 restart your-app-name
```

## 🔧 功能特性

### 自动备份
- **时间**: 每天凌晨3点自动执行
- **格式**: SQL文件，包含完整数据库结构和数据
- **存储**: 上传到腾讯云COS，按年月分目录存储
- **清理**: 本地保留最近3个备份文件

### 手动备份
通过API接口可以手动触发备份：

```bash
curl -X POST http://your-domain/api/backup/execute
```

### 备份监控
- 备份状态检查
- COS连接测试
- 系统统计信息

## 📊 API接口

### 1. 手动执行备份
```
POST /api/backup/execute
```

### 2. 测试COS连接
```
GET /api/backup/test-cos
```

### 3. 获取备份统计
```
GET /api/backup/stats
```

### 4. 获取定时任务状态
```
GET /api/backup/cron-status
```

### 5. 获取系统统计
```
GET /api/backup/system-stats
```

## 🔍 故障排除

### 1. mysqldump命令不存在
```bash
# 安装MySQL客户端
yum install mysql  # CentOS/RHEL
apt-get install mysql-client  # Ubuntu/Debian
```

### 2. COS上传失败
- 检查SecretId和SecretKey是否正确
- 确认存储桶名称和地域设置
- 验证网络连接

### 3. 权限问题
```bash
# 确保备份目录有写权限
chmod 755 /path/to/backups
chown www:www /path/to/backups
```

### 4. 内存不足
如果数据库较大，可能需要调整Node.js内存限制：
```bash
node --max-old-space-size=4096 app.js
```

## 📁 文件结构

```
imageCompression_server/
├── services/
│   └── DatabaseBackupService.js    # 备份服务核心逻辑
├── routes/
│   └── backup.js                   # 备份管理API
├── scripts/
│   └── setup-backup.js            # 备份系统设置脚本
├── utils/
│   └── cronJobs.js                 # 定时任务管理（已更新）
├── backups/                        # 本地备份文件目录
└── .env                           # 环境变量配置
```

## 🔐 安全建议

1. **定期轮换密钥**: 建议定期更换COS的SecretKey
2. **访问控制**: 限制COS存储桶的访问权限
3. **备份加密**: 考虑对敏感数据进行加密后再备份
4. **监控告警**: 设置备份失败的告警通知

## 📈 监控和日志

系统会在控制台输出详细的备份日志：
- 备份开始和完成时间
- 文件大小和上传进度
- 错误信息和故障排除提示

建议配置日志收集系统来持久化这些日志信息。

## 🔄 备份恢复

如需恢复数据库，可以从COS下载备份文件：

```bash
# 下载备份文件
# 然后使用mysql命令恢复
mysql -h localhost -u username -p database_name < backup_file.sql
```

## 📞 技术支持

如遇到问题，请检查：
1. 服务器日志
2. COS控制台
3. 数据库连接状态
4. 网络连接情况
