// pages/idPhoto/idPhoto.js
const idPhotoAPI = require('../../utils/api/idPhotoAPI');
const shareHelper = require('../../utils/share.js');

Page({
  data: {
    selectedSize: '', // 选中的尺寸
    selectedImage: '', // 选中的图片路径
    processing: false, // 是否正在处理
    loading: true, // 页面加载状态
    sizeOptions: [], // 从服务器获取的尺寸选项
    colorOptions: [] // 从服务器获取的颜色选项
  },

  onLoad(options) {

    this.loadInitialData();
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    try {
      this.setData({ loading: true });

      // 并行获取尺寸和颜色列表
      const [sizesResult, colorsResult] = await Promise.all([
        idPhotoAPI.getSizes(),
        idPhotoAPI.getColors()
      ]);



      if (sizesResult.success && colorsResult.success) {
        this.setData({
          sizeOptions: sizesResult.data.sizes || [],
          colorOptions: colorsResult.data.colors || [],
          loading: false
        });
      } else {
        throw new Error('获取数据失败');
      }

    } catch (error) {
      console.error('加载初始数据失败:', error);
      this.setData({ loading: false });

      // 简化错误处理，不显示弹窗

    }
  },

  /**
   * 选择尺寸 - 修改为选择后直接触发照片选择
   */
  selectSize(e) {
    const size = e.currentTarget.dataset.size;

    this.setData({
      selectedSize: size
    });

    // 选择尺寸后直接触发照片选择
    this.chooseImage();
  },

  /**
   * 选择图片 - 修改为选择后直接开始处理
   */
  chooseImage() {
    const that = this;

    // 检查是否已选择尺寸
    if (!this.data.selectedSize) {
      wx.showToast({
        title: '请先选择尺寸',
        icon: 'error'
      });
      return;
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success(res) {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        that.setData({
          selectedImage: tempFilePath
        });

        // 选择图片后直接开始处理
        that.startProcess();
      },
      fail(err) {
        console.log('选择图片失败:', err);
      }
    });
  },

  /**
   * 开始处理证件照 - 修改为自动调用，增加处理状态
   */
  startProcess() {
    if (!this.data.selectedSize || !this.data.selectedImage) {
      wx.showToast({
        title: '请选择尺寸和照片',
        icon: 'error'
      });
      return;
    }

    // 设置处理状态
    this.setData({
      processing: true
    });

    // 显示加载提示
    wx.showLoading({
      title: '正在跳转...',
      mask: true
    });

    // 获取选中尺寸的详细信息
    const selectedSizeInfo = this.data.sizeOptions.find(item => item.value === this.data.selectedSize);

    // 跳转到处理页面，传递参数
    wx.navigateTo({
      url: `/pages/idPhoto/result/result?image=${encodeURIComponent(this.data.selectedImage)}&size=${this.data.selectedSize}&sizeName=${encodeURIComponent(selectedSizeInfo?.name || '')}`,
      success: () => {
        wx.hideLoading();
        // 跳转成功后重置状态
        this.setData({
          processing: false,
          selectedImage: '',
          selectedSize: ''
        });
      },
      fail: () => {
        wx.hideLoading();
        this.setData({
          processing: false
        });
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 页面分享 - 分享到好友
   */
  onShareAppMessage() {
    return shareHelper.getShareConfig({
      title: '个人简历模板简历制作 - 证件照制作',
      path: '/pages/idPhoto/idPhoto',
    });
  },

  /**
   * 页面分享 - 分享到朋友圈
   */
  onShareTimeline() {
    return shareHelper.getTimelineShareConfig({
      title: '个人简历模板简历制作 - 证件照制作',
    });
  }
});
