const fs = require('fs');
const path = require('path');
const schedule = require('node-schedule');

/**
 * 文件清理器 - 定期删除过期文件
 */
class FileCleaner {
  /**
   * 构造函数
   * @param {string} directory - 要清理的目录
   * @param {number} maxAgeHours - 文件最大保留时间(小时)
   */
  constructor(directory, maxAgeHours = 24) {
    this.directory = directory;
    this.maxAgeHours = maxAgeHours;
    this.job = null;
  }

  /**
   * 启动定时清理任务
   * @param {string} cronExpression - cron表达式，默认每6小时执行一次
   */
  start(cronExpression = '0 */6 * * *') {
    // 确保目录存在
    if (!fs.existsSync(this.directory)) {
      fs.mkdirSync(this.directory, { recursive: true });
    }

    // 立即执行一次清理
    this.cleanFiles();

    // 设置定时任务
    this.job = schedule.scheduleJob(cronExpression, () => {
      this.cleanFiles();
    });

    console.log(`文件清理任务已启动，目录: ${this.directory}, 最大保留时间: ${this.maxAgeHours}小时, 清理频率: 每6小时`);
    return this;
  }

  /**
   * 停止定时清理任务
   */
  stop() {
    if (this.job) {
      this.job.cancel();
      this.job = null;
      console.log('文件清理任务已停止');
    }
    return this;
  }

  /**
   * 清理过期文件
   */
  cleanFiles() {
    try {
      console.log(`开始清理目录: ${this.directory}`);
      
      // 获取当前时间
      const now = new Date();
      
      // 读取目录中的所有文件
      const files = fs.readdirSync(this.directory);
      
      let deletedCount = 0;
      let errorCount = 0;
      
      // 遍历文件
      for (const file of files) {
        // 跳过.gitkeep等特殊文件
        if (file.startsWith('.')) continue;
        
        const filePath = path.join(this.directory, file);
        
        // 获取文件状态
        const stats = fs.statSync(filePath);
        
        // 如果是文件夹，跳过
        if (stats.isDirectory()) continue;
        
        // 计算文件年龄(小时)
        const fileAge = (now - stats.mtime) / (1000 * 60 * 60);
        
        // 如果文件超过最大保留时间，删除它
        if (fileAge > this.maxAgeHours) {
          try {
            fs.unlinkSync(filePath);
            deletedCount++;
            console.log(`已删除过期文件: ${filePath}, 年龄: ${fileAge.toFixed(2)}小时`);
          } catch (err) {
            errorCount++;
            console.error(`删除文件失败: ${filePath}`, err);
          }
        }
      }
      
      console.log(`清理完成，共删除${deletedCount}个文件，失败${errorCount}个`);
    } catch (err) {
      console.error('清理文件时出错:', err);
    }
  }
}

module.exports = FileCleaner;
