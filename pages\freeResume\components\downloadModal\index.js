// 下载链接浮窗组件
Component({
  properties: {
    // 是否显示浮窗
    visible: {
      type: Boolean,
      value: false
    },
    // 模板数据
    templateData: {
      type: Object,
      value: null
    },
    // 下载数据
    downloadData: {
      type: Object,
      value: null
    },
    // 是否正在加载
    isLoading: {
      type: Boolean,
      value: false
    }
  },

  data: {
    // 组件内部数据
  },

  methods: {
    /**
     * 关闭浮窗
     */
    onClose() {
      this.triggerEvent('close');
    },

    /**
     * 点击遮罩关闭
     */
    onMaskTap() {
      this.onClose();
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击内容区域时关闭浮窗
    },

    /**
     * 图片加载失败处理
     */
    onImageError(e) {
      console.log('模板缩略图加载失败');
      // 可以在这里设置默认占位符，但由于是组件属性，通常由父组件处理
    },

    /**
     * 复制链接到剪贴板
     */
    onCopyLink(e) {
      const { type } = e.currentTarget.dataset;
      const downloadData = this.properties.downloadData;

      if (!downloadData || !downloadData[type]) {
        wx.showToast({
          title: '暂无可复制的内容',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      const content = downloadData[type];

      wx.setClipboardData({
        data: content,
        success: () => {
          // 根据类型显示不同的提示
          let message = '';
          switch (type) {
            case 'baidu_url':
              message = '百度链接已复制';
              break;
            case 'quark_url':
              message = '夸克链接已复制';
              break;
            default:
              message = '已复制到剪贴板';
          }

          wx.showToast({
            title: message,
            icon: 'success',
            duration: 2000
          });

          // 触发复制成功事件
          this.triggerEvent('copy', {
            type: type,
            content: content
          });
        },
        fail: (err) => {
          console.error('复制失败:', err);
          wx.showToast({
            title: '复制失败，请重试',
            icon: 'none',
            duration: 2000
          });

          // 触发复制失败事件
          this.triggerEvent('copyFail', {
            type: type,
            error: err
          });
        }
      });
    },


  }
});
